/**
 * 缓存工具类
 * 提供统一的本地存储接口
 */

class Cache {
  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   * @param {number} expire - 过期时间（秒），0表示永不过期
   */
  static set(key, value, expire = 0) {
    try {
      const data = {
        value,
        expire: expire > 0 ? Date.now() + expire * 1000 : 0,
        timestamp: Date.now()
      }
      
      uni.setStorageSync(key, JSON.stringify(data))
      return true
    } catch (error) {
      console.error('Cache.set error:', error)
      return false
    }
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   * @param {any} defaultValue - 默认值
   * @returns {any} 缓存值或默认值
   */
  static get(key, defaultValue = null) {
    try {
      const dataStr = uni.getStorageSync(key)
      if (!dataStr) {
        return defaultValue
      }

      const data = JSON.parse(dataStr)
      
      // 检查是否过期
      if (data.expire > 0 && Date.now() > data.expire) {
        this.remove(key)
        return defaultValue
      }

      return data.value
    } catch (error) {
      console.error('Cache.get error:', error)
      return defaultValue
    }
  }

  /**
   * 移除缓存
   * @param {string} key - 缓存键
   */
  static remove(key) {
    try {
      uni.removeStorageSync(key)
      return true
    } catch (error) {
      console.error('Cache.remove error:', error)
      return false
    }
  }

  /**
   * 清空所有缓存
   */
  static clear() {
    try {
      uni.clearStorageSync()
      return true
    } catch (error) {
      console.error('Cache.clear error:', error)
      return false
    }
  }

  /**
   * 检查缓存是否存在
   * @param {string} key - 缓存键
   * @returns {boolean} 是否存在
   */
  static has(key) {
    try {
      const dataStr = uni.getStorageSync(key)
      if (!dataStr) {
        return false
      }

      const data = JSON.parse(dataStr)
      
      // 检查是否过期
      if (data.expire > 0 && Date.now() > data.expire) {
        this.remove(key)
        return false
      }

      return true
    } catch (error) {
      console.error('Cache.has error:', error)
      return false
    }
  }

  /**
   * 获取缓存信息
   * @param {string} key - 缓存键
   * @returns {object|null} 缓存信息
   */
  static getInfo(key) {
    try {
      const dataStr = uni.getStorageSync(key)
      if (!dataStr) {
        return null
      }

      const data = JSON.parse(dataStr)
      
      return {
        key,
        size: dataStr.length,
        timestamp: data.timestamp,
        expire: data.expire,
        isExpired: data.expire > 0 && Date.now() > data.expire
      }
    } catch (error) {
      console.error('Cache.getInfo error:', error)
      return null
    }
  }

  /**
   * 获取所有缓存键
   * @returns {array} 缓存键数组
   */
  static getAllKeys() {
    try {
      const info = uni.getStorageInfoSync()
      return info.keys || []
    } catch (error) {
      console.error('Cache.getAllKeys error:', error)
      return []
    }
  }

  /**
   * 获取缓存大小信息
   * @returns {object} 缓存大小信息
   */
  static getStorageInfo() {
    try {
      return uni.getStorageInfoSync()
    } catch (error) {
      console.error('Cache.getStorageInfo error:', error)
      return {
        keys: [],
        currentSize: 0,
        limitSize: 0
      }
    }
  }

  /**
   * 批量设置缓存
   * @param {object} data - 键值对对象
   * @param {number} expire - 过期时间（秒）
   */
  static setMultiple(data, expire = 0) {
    const results = {}
    
    for (const [key, value] of Object.entries(data)) {
      results[key] = this.set(key, value, expire)
    }
    
    return results
  }

  /**
   * 批量获取缓存
   * @param {array} keys - 缓存键数组
   * @param {any} defaultValue - 默认值
   * @returns {object} 键值对对象
   */
  static getMultiple(keys, defaultValue = null) {
    const results = {}
    
    for (const key of keys) {
      results[key] = this.get(key, defaultValue)
    }
    
    return results
  }

  /**
   * 批量移除缓存
   * @param {array} keys - 缓存键数组
   */
  static removeMultiple(keys) {
    const results = {}
    
    for (const key of keys) {
      results[key] = this.remove(key)
    }
    
    return results
  }

  /**
   * 清理过期缓存
   * @returns {number} 清理的缓存数量
   */
  static clearExpired() {
    let count = 0
    const keys = this.getAllKeys()
    
    for (const key of keys) {
      const info = this.getInfo(key)
      if (info && info.isExpired) {
        this.remove(key)
        count++
      }
    }
    
    return count
  }
}

export default Cache
