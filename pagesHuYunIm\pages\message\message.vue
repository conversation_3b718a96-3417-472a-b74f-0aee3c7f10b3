<template>
  <view class="flex_c page" @touchmove="touchmove">
    <!-- 导航栏 -->
    <view class="navigationRef">
      <navigation ref="navigationRef" :groupCount="groupCount" :title="groupInfo.name" :group_id="groupInfo.id"></navigation>
    </view>
    <!-- 消息列表 -->
    <scroll-view
      class="flex1 scroll-Y"
      @tap.stop="onPage"
      id="scroll-view"
      lower-threshold="100"
      scroll-y
      scroll-with-animation
      :scroll-top="scroll_top"
      @scroll="scroll"
      @scrolltoupper="scrolltoupper"
      @scrolltolower="scrolltolower"
    >
      <view class="scroll-view-str" :style="{ height: `${reserveHeight}px` }" v-if="reserveHeight > 0"></view>
      <view class="messageList_">
        <template v-for="(item, index) in list">
          <!-- #ifdef APP || H5 -->
          <view
            class="z_index2"
            :class="`oneheight_${index}`"
            style="transform: rotate(-180deg)"
            :key="item.id + index"
            v-if="!item.isHide"
          >
            <view class="icon_ text_26 color__ time">
              {{ renderMessageDate(item, index) }}
            </view>
            <view :key="item.messageId + index" v-if="!item.recalled">
              <item
                :isMy="isSelf(item.senderId)"
                :myid="myid"
                :item="item"
                @onClick="onItem"
                @onLongpress="onLongpress"
                @mention="mention"
                @imgLoad="imgLoad"
              ></item>
            </view>
            <view class="icon_ text_26 recalled" v-else>
              <view class="">
                <text v-if="isSelf(item.senderId)">你</text>
                <text v-else>{{ item.senderData.name }}</text>
                撤回了一条消息
              </view>
              <view class="recalled-edit" v-if="item.type === 'text' && isSelf(item.senderId)" @click="recalledEdit(item)">重新编辑</view>
            </view>
          </view>
          <!-- #endif -->
          <!-- #ifdef MP -->
          <view class="z_index2" style="transform: rotate(-180deg)" :key="item.id" v-if="!item.isHide">
            <view class="icon_ text_26 color__ time">
              {{ renderMessageDate(item, index) }}
            </view>
            <view :key="item.id" v-if="!item.recalled">
              <item
                :isMy="isSelf(item.senderId)"
                :myid="myid"
                :item="item"
                @onClick="onItem"
                @onLongpress="onLongpress"
                @mention="mention"
              ></item>
            </view>
            <view class="icon_ text_26 recalled" v-else>
              <view class="">
                <text v-if="isSelf(item.senderId)">你</text>
                <text v-else>{{ item.senderData.name }}</text>
                撤回了一条消息
              </view>
              <view class="recalled-edit" v-if="item.type === 'text' && isSelf(item.senderId)" @click="recalledEdit(item)">重新编辑</view>
            </view>
          </view>
          <!-- #endif -->
        </template>
      </view>
      <view :style="{ height: $store.state.StatusBar.customBar - 8 + 4 + 'px' }"></view>
    </scroll-view>
    <!-- 底部操作区域 -->
    <view class="bottomOperationRef">
      <bottom-operation
        ref="bottomOperationRef"
        :to="to"
        :userList="userList"
        @pushList="pushList"
        @onBottom="onBottom"
        @backToBottom="bottomOperationScrollToBottom"
        @focus="focus"
        @keyboardheightchange="keyboardheightchange"
      ></bottom-operation>
    </view>
  </view>
</template>
<script>
import navigation from '../chat/components/navigation/index.vue'
import bottomOperation from '../chat/components/bottom-operation/index.vue'
import item from '../chat/components/item/index.vue'
import { mapState } from 'vuex'

// 是否是手动触发的列表滑动
let isBottomOperationScrollToBottom = false

const IMAGE_MAX_WIDTH = 200
const IMAGE_MAX_HEIGHT = 150
let scroll_top = 0
let reserveHeightRef = 0
let bottomOperationRefHeight = 0

export default {
  components: {
    navigation,
    bottomOperation,
    item
  },
  name: 'groupChat',
  data() {
    return {
      isHistoryGet: false,
      reserveHeight: 0,
      keyboardheightchangeValue: 0,
      myid: null,
      scroll_top,
      userList: [], //群成员列表
      groupCount: '',
      groupInfo: {
        id: '1921827090039152642',
        name: '项目讨论组'
      },
      to: {},
      // 历史数据
      history: {
        messages: [],
        allLoaded: false
      },
      // 添加缺失的数据属性
      page: 1,
      pageSize: 50,
      groupId: '',
      loading: false,
      loadend: false,
      list: [
        {
          id: '1921827090039152642_000001',
          messageId: '1921827090039152642_000001',
          senderId: '1921822887908581377',
          senderData: {
            name: '张三',
            avatar: 'https://via.placeholder.com/96x96/2196F3/FFFFFF?text=张'
          },
          type: 'text',
          payload: {
            text: '大家好，今天的项目进度怎么样？'
          },
          timestamp: Date.now() - 300000,
          recalled: false,
          status: 'success',
          isHide: false
        },
        {
          id: '1921827090039152642_000002',
          messageId: '1921827090039152642_000002',
          senderId: '1921822887908581378',
          senderData: {
            name: '李四',
            avatar: 'https://via.placeholder.com/96x96/4CAF50/FFFFFF?text=李'
          },
          type: 'text',
          payload: {
            text: '我这边基本完成了，正在测试'
          },
          timestamp: Date.now() - 240000,
          recalled: false,
          status: 'success',
          isHide: false
        },
        {
          id: '1921827090039152642_000003',
          messageId: '1921827090039152642_000003',
          senderId: '1921822887908581379',
          senderData: {
            name: '王五',
            avatar: 'https://via.placeholder.com/96x96/FF9800/FFFFFF?text=王'
          },
          type: 'text',
          payload: {
            text: '我还需要一点时间，预计明天完成'
          },
          timestamp: Date.now() - 180000,
          recalled: false,
          status: 'success',
          isHide: false
        },
        {
          id: '1921827090039152642_000004',
          messageId: '1921827090039152642_000004',
          senderId: '1921822887908581380',
          senderData: {
            name: '赵六',
            avatar: 'https://via.placeholder.com/96x96/9C27B0/FFFFFF?text=赵'
          },
          type: 'image',
          payload: {
            url: 'https://via.placeholder.com/200x150/FF5722/FFFFFF?text=截图'
          },
          timestamp: Date.now() - 120000,
          recalled: false,
          status: 'success',
          isHide: false
        },
        {
          id: '1921827090039152642_000005',
          messageId: '1921827090039152642_000005',
          senderId: '1921822887908581378',
          senderData: {
            name: '李四',
            avatar: 'https://via.placeholder.com/96x96/4CAF50/FFFFFF?text=李'
          },
          type: 'text',
          payload: {
            text: '看起来不错！'
          },
          timestamp: Date.now() - 60000,
          recalled: false,
          status: 'success',
          isHide: false
        }
      ],
      userMap: {},
      mqttClient: null,
      mqttPingInterval: null,
      groupIdNew: 0
    }
  },

  computed: {
    ...mapState(['StatusBar'])
  },

  watch: {
    list: {
      handler() {
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      },
      deep: true
    }
  },

  async onLoad(e) {
    console.log('🚀 ~ onLoad ~ e:', e)
    this.groupIdNew = e.groupId || '1921827090039152642'
    this.groupInfo.id = this.groupIdNew
    this.myid = '1921822887908581378' // 设置当前用户ID

    // 初始化数据
    this.initData()
  },

  mounted() {
    console.log('页面已挂载，消息列表长度:', this.list.length)
    this.scrollToBottom()
  },

  methods: {
    // 初始化数据
    initData() {
      // 设置当前用户ID
      this.myid = '1921822887908581378'

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    // 触摸移动事件
    touchmove() {
      // 处理触摸移动
    },

    // 页面点击事件
    onPage() {
      // 处理页面点击
    },

    // 滚动事件
    scroll(e) {
      // 处理滚动
    },

    // 滚动到顶部
    scrolltoupper() {
      console.log('滚动到顶部，加载更多消息')
      // 加载更多历史消息
    },

    // 滚动到底部
    scrolltolower() {
      console.log('滚动到底部')
    },

    // 判断是否为自己发送的消息
    isSelf(senderId) {
      return senderId === this.myid
    },

    // 渲染消息日期
    renderMessageDate(item, index) {
      if (index === 0) return ''

      const currentTime = new Date(item.timestamp)
      const prevTime = new Date(this.list[index - 1].timestamp)

      // 如果时间间隔超过5分钟，显示时间
      if (currentTime - prevTime > 5 * 60 * 1000) {
        return this.formatTime(currentTime)
      }

      return ''
    },

    // 格式化时间
    formatTime(date) {
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const msgDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())

      const diffDays = Math.floor((today - msgDate) / (24 * 60 * 60 * 1000))

      if (diffDays === 0) {
        // 今天，显示时间
        return date.toTimeString().slice(0, 5)
      } else if (diffDays === 1) {
        // 昨天
        return `昨天 ${date.toTimeString().slice(0, 5)}`
      } else {
        // 更早，显示日期
        return `${date.getMonth() + 1}月${date.getDate()}日 ${date.toTimeString().slice(0, 5)}`
      }
    },

    // 消息项点击事件
    onItem(item) {
      console.log('消息项点击:', item)
    },

    // 消息项长按事件
    onLongpress(item) {
      console.log('消息项长按:', item)
      // 显示消息操作菜单
      uni.showActionSheet({
        itemList: ['复制', '删除', '撤回'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.copyMessage(item)
              break
            case 1:
              this.deleteMessage(item)
              break
            case 2:
              this.recallMessage(item)
              break
          }
        }
      })
    },

    // 复制消息
    copyMessage(item) {
      if (item.type === 'text') {
        uni.setClipboardData({
          data: item.payload.text,
          success: () => {
            uni.showToast({
              title: '已复制',
              icon: 'success'
            })
          }
        })
      }
    },

    // 删除消息
    deleteMessage(item) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这条消息吗？',
        success: (res) => {
          if (res.confirm) {
            const index = this.list.findIndex((msg) => msg.id === item.id)
            if (index > -1) {
              this.list.splice(index, 1)
            }
          }
        }
      })
    },

    // 撤回消息
    recallMessage(item) {
      if (!this.isSelf(item.senderId)) {
        uni.showToast({
          title: '只能撤回自己的消息',
          icon: 'none'
        })
        return
      }

      // 检查时间限制（比如2分钟内）
      const now = new Date().getTime()
      if (now - item.timestamp > 2 * 60 * 1000) {
        uni.showToast({
          title: '超过时间限制，无法撤回',
          icon: 'none'
        })
        return
      }

      uni.showModal({
        title: '确认撤回',
        content: '确定要撤回这条消息吗？',
        success: (res) => {
          if (res.confirm) {
            item.recalled = true
            uni.showToast({
              title: '已撤回',
              icon: 'success'
            })
          }
        }
      })
    },

    // 重新编辑撤回的消息
    recalledEdit(item) {
      console.log('重新编辑消息:', item)
      // 这里可以将撤回的消息内容填入输入框
    },

    // @mention 事件
    mention(user) {
      console.log('提及用户:', user)
    },

    // 图片加载事件
    imgLoad(e) {
      console.log('图片加载:', e)
    },

    // 底部操作相关方法
    pushList(message) {
      console.log('推送新消息:', message)
      // 初始化消息项
      this.initMessageItem(message)

      // 添加到消息列表
      this.list.unshift(message)

      // 滚动到底部
      this.scrollToBottom()
    },

    // 初始化消息项
    initMessageItem(message) {
      message['isHide'] = false
      // 初始化语音
      if (message.type === 'audio') {
        message['pause'] = 4
      }
    },

    // 底部操作事件
    onBottom() {
      console.log('底部操作')
    },

    // 滚动到底部（底部操作触发）
    bottomOperationScrollToBottom() {
      this.scrollToBottom()
    },

    // 输入框获得焦点
    focus() {
      console.log('输入框获得焦点')
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    // 键盘高度变化
    keyboardheightchange(e) {
      console.log('键盘高度变化:', e)
      this.keyboardheightchangeValue = e.detail.height
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    // 滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        this.scroll_top = this.list.length * 1000
      })
    }
  },

  onLoad(options) {
    // 从参数中获取群组信息
    if (options.groupId) {
      this.groupInfo.id = options.groupId
    }
    if (options.groupName) {
      this.groupInfo.name = decodeURIComponent(options.groupName)
    }
  },

  onLoad(options) {
    // 从参数中获取群组信息
    if (options.groupId) {
      this.groupInfo.id = options.groupId
    }
    if (options.groupName) {
      this.groupInfo.name = decodeURIComponent(options.groupName)
    }
  }
}
</script>
<style lang="scss" scoped>
// 引入聊天页面的通用样式
@import '../../static/style/chatInterface.css';

.page {
  height: 100vh;
  background-color: #f8f8f8;
}

.flex_c {
  display: flex;
  flex-direction: column;
}

.flex1 {
  flex: 1;
}

.scroll-Y {
  overflow-y: auto;
}

.navigationRef {
  position: sticky;
  top: 0;
  z-index: 100;
}

.bottomOperationRef {
  position: sticky;
  bottom: 0;
  z-index: 100;
}

.messageList_ {
  padding: 20rpx;
  transform: rotate(180deg);
}

.z_index2 {
  position: relative;
  z-index: 2;
}

.icon_ {
  display: flex;
  align-items: center;
  justify-content: center;
}

.text_26 {
  font-size: 26rpx;
}

.color__ {
  color: #999999;
}

.time {
  text-align: center;
  margin: 20rpx 0;
  padding: 8rpx 16rpx;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 16rpx;
  color: #ffffff;
  font-size: 24rpx;
  display: inline-block;
}

.recalled {
  text-align: center;
  margin: 20rpx 0;
  color: #999999;
  font-size: 26rpx;
}

.recalled-edit {
  color: #007aff;
  text-decoration: underline;
  margin-left: 10rpx;
  cursor: pointer;
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .messageList_ {
    padding: 16rpx;
  }

  .time {
    font-size: 22rpx;
    padding: 6rpx 12rpx;
  }

  .recalled {
    font-size: 24rpx;
  }
}
</style>
