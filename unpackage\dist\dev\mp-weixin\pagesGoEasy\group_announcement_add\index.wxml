<view class="flex_c_c page data-v-1ab28e3b"><view class="flex1 box data-v-1ab28e3b"><view class="text_30 box-title data-v-1ab28e3b">预览：</view><view class="text_32 data-v-1ab28e3b" style="{{'white-space:'+('pre-wrap')+';'}}"><rich-text nodes="{{renderTextMessage}}"></rich-text></view></view><view class="bottom-operation-box data-v-1ab28e3b"><view hidden="{{!(keyboardHeight)}}" class="flex_r line-break data-v-1ab28e3b"><view data-event-opts="{{[['tap',[['lineBreak',['$event']]]]]}}" class="icon_ text_28 color__ line-break-box data-v-1ab28e3b" bindtap="__e"><view class="icon_ line-break-icon data-v-1ab28e3b"><image class="img data-v-1ab28e3b" src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTAgMGgxMDI0djEwMjRIMHoiIGZpbGw9IiMyYzJjMmMiIGZpbGwtb3BhY2l0eT0iLjAxIi8+PHBhdGggZD0iTTY4Mi42NjcgMTI4YTI5OC42NjcgMjk4LjY2NyAwIDAgMSAxMC4yNCA1OTcuMTYzbC0xMC4yNC4xN2gtNTEyYTQyLjY2NyA0Mi42NjcgMCAwIDEtNC45OTItODUuMDM0bDQuOTkyLS4yOTloNTEyYTIxMy4zMzMgMjEzLjMzMyAwIDAgMCA5LjI1OC00MjYuNDUzbC05LjI1OC0uMjE0aC01MTJhNDIuNjY3IDQyLjY2NyAwIDAgMS00Ljk5Mi04NS4wMzRsNC45OTItLjI5OWg1MTJ6IiBmaWxsPSIjMmMyYzJjIi8+PHBhdGggZD0iTTI0Ny4xNjggNDYwLjUwMWE0Mi42NjcgNDIuNjY3IDAgMCAxIDYzLjg3MiA1Ni4zMmwtMy41NDEgNC4wMTEtMTYwIDE2MCAxNTguMTY1IDE0MC42M2E0Mi42NjcgNDIuNjY3IDAgMCAxIDYuODI3IDU1Ljk3OGwtMy4yODYgNC4yNjdhNDIuNjY3IDQyLjY2NyAwIDAgMS01NS45NzggNi44MjZsLTQuMjY3LTMuMzI4LTE5Mi0xNzAuNjY2YTQyLjY2NyA0Mi42NjcgMCAwIDEtNS4yNDgtNTguMTU1bDMuNDEzLTMuODgzIDE5Mi0xOTJ6IiBmaWxsPSIjMmMyYzJjIi8+PC9zdmc+" mode="aspectFill"></image></view>换行</view></view><view class="flex_r bottom-operation data-v-1ab28e3b"><view style="width:10rpx;" class="data-v-1ab28e3b"></view><view class="flex_c_c flex1 data-v-1ab28e3b"><view class="bottom-operation-input data-v-1ab28e3b"><textarea class="input data-v-1ab28e3b" auto-height="true" confirm-type="done" type="text" maxlength="{{-1}}" focus="{{isFocus}}" adjust-position="{{false}}" confirm-hold="{{true}}" data-event-opts="{{[['confirm',[['sendingText',['$event']]]],['focus',[['focus',['$event']]]],['blur',[['e0',['$event']]]],['keyboardheightchange',[['keyboardheightchange',['$event']]]],['input',[['__set_model',['','text','$event',[]]]]]]}}" value="{{text}}" bindconfirm="__e" bindfocus="__e" bindblur="__e" bindkeyboardheightchange="__e" bindinput="__e"></textarea></view></view><view style="width:10rpx;" class="data-v-1ab28e3b"></view><view data-event-opts="{{[['tap',[['tapEmoji',['$event']]]]]}}" class="icon_ bottom-operation-icon data-v-1ab28e3b" bindtap="__e"><image class="img data-v-1ab28e3b" src="{{b}}" mode="aspectFill"></image></view></view><view class="data-v-1ab28e3b"><emoji bind:onEmoji="__e" bind:deleteFn="__e" bind:sendingText="__e" bind:sendingEmojiPack="__e" bind:input="__e" vue-id="3b9fba2b-1" value="{{isEmoji}}" data-event-opts="{{[['^onEmoji',[['onEmoji']]],['^deleteFn',[['deleteFn']]],['^sendingText',[['sendingText']]],['^sendingEmojiPack',[['sendingEmojiPack']]],['^input',[['__set_model',['','isEmoji','$event',[]]]]]]}}" class="data-v-1ab28e3b" bind:__l="__l"></emoji></view><view class="keyboard data-v-1ab28e3b" style="{{'height:'+(keyboardHeight+'px')+';'}}"></view><block wx:if="{{keyboardHeight===0}}"><view class="data-v-1ab28e3b"><m-bottom-paceholder vue-id="3b9fba2b-2" class="data-v-1ab28e3b" bind:__l="__l"></m-bottom-paceholder></view></block></view></view>