@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.row_.data-v-60de8d8d {
  flex-direction: row-reverse;
}
.text_box.data-v-60de8d8d {
  flex-direction: row-reverse;
}
.text.data-v-60de8d8d {
  position: relative;
  z-index: 99;
  box-sizing: border-box;
}
.text_r.data-v-60de8d8d {
  position: relative;
}
.text_l.data-v-60de8d8d {
  position: relative;
}
.text_r.data-v-60de8d8d::after {
  position: absolute;
  z-index: -1;
  content: "";
  top: 26rpx;
  right: -8rpx;
  width: 18rpx;
  height: 18rpx;
  border-radius: 2px;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  background-color: #fff;
}
.text_l.data-v-60de8d8d::after {
  position: absolute;
  z-index: -1;
  content: "";
  top: 26rpx;
  left: -8rpx;
  width: 18rpx;
  height: 18rpx;
  border-radius: 2px;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  background-color: #fff;
}
.article.data-v-60de8d8d {
  box-sizing: border-box;
  padding: 14rpx 20rpx 4rpx 20rpx;
  width: 490rpx;
  border-radius: 10rpx;
  overflow: hidden;
  background-color: #fff;
  border: 0.5px solid #fff;
}
.article .article-infr.data-v-60de8d8d {
  width: 100%;
  height: 46rpx;
  margin-bottom: 10rpx;
}
.article .article-infr .article-infr-img.data-v-60de8d8d {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f1f1f1;
}
.article .article-title.data-v-60de8d8d {
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 14rpx;
}
.article .article-img.data-v-60de8d8d {
  width: 450rpx;
  height: 350rpx;
  background-color: #f1f1f1;
}
.article .m-line.data-v-60de8d8d {
  width: 100%;
  height: 1px;
  margin-top: 20rpx;
}
.article .article-b.data-v-60de8d8d {
  width: 100%;
  margin-top: 4rpx;
}
.article .article-b .article-b-icon.data-v-60de8d8d {
  width: 26rpx;
  height: 26rpx;
  background-color: #f1f1f1;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10rpx;
}
