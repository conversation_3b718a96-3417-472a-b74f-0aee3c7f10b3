
 ::-webkit-scrollbar {
	width: 0;
	height: 0;
	color: transparent;
	display: none;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.next.data-v-fe3facfc {
  position: relative;
  width: 100%;
  height: 82vh;
  background-color: #f7f7f7;
  overflow: hidden;
  border-radius: 20rpx 20rpx 0 0;
}
.next .top.data-v-fe3facfc {
  width: 100%;
  height: 220rpx;
}
.next .top .top-title.data-v-fe3facfc {
  width: calc(100% - 60rpx);
  height: 120rpx;
  margin: 0 auto;
}
.next .top .top-title .top-title-text.data-v-fe3facfc {
  width: 100rpx;
}
.next .top .top-title .top-title-icon.data-v-fe3facfc {
  width: 44rpx;
  height: 44rpx;
  margin-left: 66rpx;
}
.next .top .top-title .top-title-button.data-v-fe3facfc {
  width: 100rpx;
  height: 66rpx;
  border-radius: 10rpx;
  background-color: #4ac165;
}
.next .top .search.data-v-fe3facfc {
  position: relative;
  width: calc(100% - 40rpx);
  height: 80rpx;
  margin: 0 auto;
  border-radius: 14rpx;
  background-color: #fff;
}
.next .top .search .search-input.data-v-fe3facfc {
  box-sizing: border-box;
  padding: 0 20rpx;
  position: absolute;
  z-index: 3;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.next .top .search .search-input input.data-v-fe3facfc {
  width: 100%;
  height: 100%;
  text-align: center;
}
.next .top .search .search-icon.data-v-fe3facfc {
  width: 34rpx;
  height: 34rpx;
  margin-right: 16rpx;
}
.next .top .search .search-text.data-v-fe3facfc {
  color: #9b9b9b;
}
.item.data-v-fe3facfc {
  box-sizing: border-box;
  width: 100%;
  padding: 0 0 0 20rpx;
}
.item .item-label.data-v-fe3facfc {
  box-sizing: border-box;
  padding: 0 14rpx;
  height: 44rpx;
  border-radius: 10rpx;
  color: #4ac165;
  border: #4ac165 1px solid;
  margin-right: 20rpx;
}
.item .choice.data-v-fe3facfc {
  opacity: 0;
  width: 0rpx;
  height: 0rpx;
  margin-right: 0rpx;
  background-color: #fff;
  border-radius: 50%;
  border: 1px solid #999;
  transition: all 0.3s;
}
.item .choice .img.data-v-fe3facfc {
  width: 80%;
  height: 80%;
  margin-top: 4rpx;
}
.item .choice_.data-v-fe3facfc {
  background-color: #4ac165;
  border: 1px solid #4ac165;
}
.item .showChoice.data-v-fe3facfc {
  opacity: 1;
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}
.item .item-img.data-v-fe3facfc {
  width: 80rpx;
  height: 80rpx;
  border-radius: 10rpx;
  margin-right: 30rpx;
  overflow: hidden;
  background-color: #f1f1f1;
}
.item .item-name.data-v-fe3facfc {
  position: relative;
  width: 100%;
  height: 120rpx;
}
.item .item-name .m-line.data-v-fe3facfc {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
}
.next-list.data-v-fe3facfc {
  position: relative;
  width: 100%;
  height: 0;
  border-radius: 10rpx 10rpx 0 0;
  box-sizing: border-box;
  background-color: #fff;
  overflow: hidden;
}
.next-list .next-scroll-left.data-v-fe3facfc {
  height: 100%;
}
.next-list .no-data.data-v-fe3facfc {
  width: 100%;
}
.next-list .no-data .no-data-img.data-v-fe3facfc {
  width: 200rpx;
  height: 200rpx;
  margin-top: 100rpx;
  margin-bottom: 20rpx;
}
