<template>
	<view>
		<view class="flex_r fa_c title" @click="onSelect">
			<view class="">{{ envelopeTexte[tapIndex].title }}</view>
			<view class="icon_ title-icon">
				<image
					class="img"
					src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTQxNS40NTEgOTg4Ljg5MWMtMjAuNDggMTcuNTU1LTUyLjY2MiAxNy41NTUtNzAuMjE3LTIuOTI1LTIzLjQwNS0xNy41NTUtMjMuNDA1LTQ5LjczNy01Ljg1MS03MC4yMTdMNzQzLjEzIDUxMiAzMzkuMzgzIDExMS4xNzdjLTE0LjYyOS0xMS43MDMtMjAuNDgtMzIuMTgzLTE0LjYyOS00OS43MzcgNS44NTItMTcuNTU0IDIwLjQ4LTMyLjE4MyAzOC4wMzUtMzguMDM0IDE3LjU1NC01Ljg1MiAzOC4wMzQgMCA0OS43MzcgMTQuNjI4bDQzOC44NTcgNDM4Ljg1N2M4Ljc3NyA4Ljc3OCAxNC42MjggMjMuNDA2IDE0LjYyOCAzOC4wMzVzLTUuODUxIDI2LjMzMS0xNC42MjggMzguMDM0TDQxNS40NSA5ODguODkxem0wIDAiIGZpbGw9IiNiYTlkNjMiLz48L3N2Zz4="
					mode="aspectFill"
				></image>
			</view>
		</view>
		<!-- 拼手气 -->
		<template v-if="tapType === 'fortune'">
			<view class="icon_ text_30 row">
				<view class="row-img">
					<image
						class="img"
						src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTg3OC41OTIgMTAyNEgxNDUuNDA4Yy0yNC4wNjQgMC00My41Mi0xOS40NTYtNDMuNTItNDMuNTJWNDMuNTJjMC0yNC4wNjQgMTkuNDU2LTQzLjUyIDQzLjUyLTQzLjUyaDczMy4xODRjMjQuMDY0IDAgNDMuNTIgMTkuNDU2IDQzLjUyIDQzLjUydjkzNi40NDhjMCAyNC41NzYtMTkuNDU2IDQ0LjAzMi00My41MiA0NC4wMzJ6IiBmaWxsPSIjREY0OTQ5Ii8+PHBhdGggZD0iTTg3OC41OTIgMEgxNDUuNDA4Yy0yNC4wNjQgMC00NC4wMzIgMTkuNDU2LTQ0LjAzMiA0My41MnYzNzMuMjQ4QzIxMC45NDQgNDUzLjEyIDMzNS44NzIgNDczLjYgNDY4Ljk5MiA0NzMuNmMxNjguOTYgMCAzMjUuNjMyLTMzLjI4IDQ1My4xMi05MC4xMTJWNDMuNTJjMC0yNC4wNjQtMTkuNDU2LTQzLjUyLTQzLjUyLTQzLjUyeiIgZmlsbD0iI0ZCNTM1MiIvPjxwYXRoIGQ9Ik0zNzUuMjk2IDQ4OS45ODRjMCA3NS4yNjQgNjEuNDQgMTM2LjcwNCAxMzYuNzA0IDEzNi43MDRzMTM2LjcwNC02MS40NCAxMzYuNzA0LTEzNi43MDRTNTg3LjI2NCAzNTMuMjggNTEyIDM1My4yOHMtMTM2LjcwNCA2MS40NC0xMzYuNzA0IDEzNi43MDR6IiBmaWxsPSIjRkNDRTNFIi8+PHBhdGggZD0iTTU2MS42NjQgNDgzLjg0aDMuMDcydi0uNTEyYzQuMDk2LTEuNTM2IDcuMTY4LTUuMTIgNy4xNjgtOS43MjggMC01LjYzMi00LjYwOC0xMC4yNC0xMC4yNC0xMC4yNEg1MjIuMjR2LS41MTJsNDEuNDcyLTQwLjQ0OCAyLjA0OC0yLjA0OGMxLjAyNC0xLjUzNiAxLjUzNi0zLjA3MiAxLjUzNi00LjYwOCAwLTIuNTYtLjUxMi01LjEyLTIuNTYtNy42OC0zLjU4NC00LjA5Ni0xMC4yNC00LjYwOC0xNC4zMzYtLjUxMmwtMzcuODg4IDM3LjM3Ni0zOS45MzYtMzguOTEyYy00LjA5Ni0zLjU4NC0xMC43NTItMy4wNzItMTQuMzM2IDEuMDI0LTMuMDcyIDMuNTg0LTMuNTg0IDguMTkyLTEuMDI0IDEyLjI4OHYuNTEybDQ1LjA1NiA0NC4wMzJ2MS4wMjRoLTQyLjQ5NnYuNTEyYy00LjA5NiAxLjUzNi03LjE2OCA1LjEyLTcuMTY4IDkuNzI4czMuMDcyIDguMTkyIDcuMTY4IDkuNzI4di41MTJoNDEuOTg0djMxLjIzMmgtNDMuMDA4di41MTJjLTQuMDk2IDEuNTM2LTcuMTY4IDUuMTItNy4xNjggOS43MjhzMy4wNzIgOC4xOTIgNy4xNjggOS43Mjh2LjUxMmg0My4wMDh2MzQuMzA0aC41MTJjMS41MzYgNC4wOTYgNS4xMiA3LjE2OCA5LjcyOCA3LjE2OHM4LjE5Mi0zLjA3MiA5LjcyOC03LjE2OGguNTEydi0zNC4zMDRoNDIuNDk2di0uNTEyYzQuMDk2LTEuNTM2IDcuMTY4LTUuMTIgNy4xNjgtOS43MjggMC01LjYzMi00LjYwOC0xMC4yNC0xMC4yNC0xMC4yNEg1MjIuMjR2LTMxLjIzMmwzOS40MjQtMS41MzZ6IiBmaWxsPSIjRDg4NjE5Ii8+PC9zdmc+"
						mode="aspectFill"
					></image>
				</view>
				<view class="flex1 row-title">红包个数</view>
				<view class="icon_ row-input">
					<input class="input" v-model="form.red_num" placeholder="填写红包个数" type="number" @input="redNumFun" />
				</view>
				<view style="color: grey">个</view>
			</view>
			<view class="text_28 row-num" style="color: grey">本群共{{ memberNumber }}个人</view>
			<view class="icon_ text_30 row">
				<view class="row-img">
					<image
						class="img"
						src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTExMy43NzggMGg3OTYuNDQ0UTEwMjQgMCAxMDI0IDExMy43Nzh2Nzk2LjQ0NFExMDI0IDEwMjQgOTEwLjIyMiAxMDI0SDExMy43NzhRMCAxMDI0IDAgOTEwLjIyMlYxMTMuNzc4UTAgMCAxMTMuNzc4IDB6IiBmaWxsPSIjQzNBNjcwIi8+PHBhdGggZD0iTTUyMy42MDUgMTc2LjM1NmwtNTMuOTMgMjUuMjU4YTczNC4wNjYgNzM0LjA2NiAwIDAgMSA2NS41MzYgMTAyLjRoLTk2LjkzOXY2MC43NThoNzUuNzc2djExNC4wMDVjMCAxMC45MjMtLjY4MyAyMS44NDUtMS4zNjUgMzIuNzY4aC04Ni42OTl2NjAuNzU3aDgxLjIzN2E0MjQuNTMzIDQyNC41MzMgMCAwIDEtMTguNDMyIDgwLjU1NWMtMTYuMzg0IDQxLjY0My00Ni40MjEgNzYuNDU5LTkwLjc5NCAxMDMuNzY1bDM1LjQ5OCA1NS45NzljNDkuMTUyLTI3LjMwNyA4NC42NTEtNjYuMjE5IDEwNi40OTYtMTE2LjczNiAxMy42NTQtMzQuMTMzIDIzLjg5NC03NS4wOTMgMzAuMDM4LTEyMy41NjNoMTA3LjE3OHYyMzQuMTU1aDYyLjgwNlY1NzIuMzAyaDg2LjY5OHYtNjAuNzU3aC04Ni42OThWMzY0Ljc3Mmg3My4wNDV2LTYwLjc1OGgtODMuMjg1YzE3Ljc0OS0zMi4wODUgMzQuMTMzLTY4LjI2NiA0OS4xNTItMTA4LjU0NGwtNjAuNzU4LTIxLjg0NWMtMTYuMzg0IDQ2LjQyMS0zNC44MTYgOTAuMTEyLTU1Ljk3OCAxMzAuMzloLTExMi42NGw0My4wMDgtMjEuMTYzYTE1MTQuODA5IDE1MTQuODA5IDAgMCAwLTY4Ljk1LTEwNi40OTZ6bTUxLjg4MyAzMzUuMTg5bDEuMzY1LTMyLjc2OFYzNjQuNzcyaDEwMC4zNTJ2MTQ2Ljc3M0g1NzUuNDg4em0tMTYxLjExLTY3LjU4NGMtMjAuNDggMTIuMjg4LTQxLjY0MiAyMy4yMS02Mi44MDUgMzQuMTMzVjM2Mi4wNDFoNjQuMTcxdi02Mi4xMjNoLTY0LjE3VjE4MS44MTdoLTY1LjUzN3YxMTguMTAxSDIwNC44djYyLjEyM2g4MS4yMzd2MTQ0LjcyNWE4NjkuNTQ3IDg2OS41NDcgMCAwIDEtOTAuNzk0IDI3Ljk5bDE2LjM4NCA2NS41MzZjMjQuNTc2LTguODc1IDQ5LjgzNC0xOC40MzIgNzQuNDEtMjcuOTl2MTQ4LjgyMmMwIDE1LjctOC4xOTIgMjMuODkzLTIzLjg5MyAyMy44OTMtMTUuMDE5IDAtMzEuNDAzLTEuMzY1LTQ4LjQ3LTMuNDEzbDE0LjMzNyA2Mi44MDVoNTYuNjYxYzQ0LjM3MyAwIDY2LjkwMS0yMi41MjggNjYuOTAxLTY2LjkwMXYtMTk0LjU2YTEwOTYuMjQ5IDEwOTYuMjQ5IDAgMCAwIDYyLjgwNi0zMi43Njh2LTY2LjIyeiIgZmlsbD0iI0ZGRiIvPjwvc3ZnPg=="
						mode="aspectFill"
					></image>
				</view>
				<view class="flex1 row-title">总金额</view>
				<view class="icon_ row-input">
					<input class="input" v-model="form.red_money" placeholder="￥0.00" type="digit" @blur="redMoneyFun" />
				</view>
			</view>
		</template>
		<template v-if="tapType === 'exclusive'">
			<view class="icon_ text_30 row row-user" @click="onUser">
				<view class="row-title">发给谁</view>
				<view class="flex1 flex_r fa_c row-user-box" v-if="toUser.member_id">
					<view class="row-user-name">{{ toUser.name }}</view>
					<view class="row-user-img">
						<image class="img" :src="toUser.member_avatar" mode="aspectFill"></image>
					</view>
				</view>
				<view class="flex1" v-else></view>
				<view class="icon_ row-icon">
					<image
						class="img"
						src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTQxNS40NTEgOTg4Ljg5MWMtMjAuNDggMTcuNTU1LTUyLjY2MiAxNy41NTUtNzAuMjE3LTIuOTI1LTIzLjQwNS0xNy41NTUtMjMuNDA1LTQ5LjczNy01Ljg1MS03MC4yMTdMNzQzLjEzIDUxMiAzMzkuMzgzIDExMS4xNzdjLTE0LjYyOS0xMS43MDMtMjAuNDgtMzIuMTgzLTE0LjYyOS00OS43MzcgNS44NTItMTcuNTU0IDIwLjQ4LTMyLjE4MyAzOC4wMzUtMzguMDM0IDE3LjU1NC01Ljg1MiAzOC4wMzQgMCA0OS43MzcgMTQuNjI4bDQzOC44NTcgNDM4Ljg1N2M4Ljc3NyA4Ljc3OCAxNC42MjggMjMuNDA2IDE0LjYyOCAzOC4wMzVzLTUuODUxIDI2LjMzMS0xNC42MjggMzguMDM0TDQxNS40NSA5ODguODkxem0wIDAiIGZpbGw9IiM3MzczNzMiLz48L3N2Zz4="
						mode="aspectFilla"
					></image>
				</view>
			</view>
			<view class="icon_ text_30 row">
				<view class="row-img">
					<image
						class="img"
						src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTE2OS42IDY0aDY4NC44QzkxMiA2NCA5NjAgMTEyIDk2MCAxNjkuNnY3OTMuNmMwIDEyLjgtOS42IDIyLjQtMjIuNCAyMi40LTYuNCAwLTEyLjgtMy4yLTE2LTkuNmwtNTEuMi02NGMtMjguOC0zOC40LTgzLjItNDQuOC0xMTguNC0xNi0zLjIgMy4yLTkuNiA2LjQtMTIuOCAxMi44TDY3MiA5ODUuNmMtNi40IDkuNi0yMi40IDkuNi0yOC44IDMuMmwtMy4yLTMuMi02NC04MGMtMjguOC0zNS4yLTgzLjItMzguNC0xMTguNC05LjZsLTkuNiA5LjYtNzAuNCA4MGMtNi40IDkuNi0yMi40IDkuNi0yOC44IDMuMmwtMy4yLTMuMi02Ny4yLTc2LjhjLTMyLTM1LjItODMuMi0zOC40LTExOC40LTYuNC0zLjIgMy4yLTkuNiA2LjQtMTIuOCAxMi44TDEwMi40IDk3NmMtNi40IDkuNi0xOS4yIDEyLjgtMjguOCAzLjItNi40LTYuNC05LjYtMTIuOC05LjYtMTkuMlYxNjkuNkM2NCAxMTIgMTEyIDY0IDE2OS42IDY0em0zMjMuMiA1NTMuNmwxMjEuNiA2NGMzLjIgMy4yIDkuNiAzLjIgMTIuOCAzLjIgMTIuOC0zLjIgMTkuMi0xMi44IDE2LTI1LjZsLTI1LjYtMTM0LjQgOTkuMi05Mi44YzMuMi0zLjIgNi40LTYuNCA2LjQtMTIuOCAwLTEyLjgtNi40LTIyLjQtMTkuMi0yMi40bC0xMzcuNi0xOS4yTDUxMiAyNTZsLTkuNi05LjZjLTkuNi02LjQtMjIuNCAwLTI4LjggOS42bC02MC44IDEyMS42LTEzNy42IDE5LjJjLTYuNCAwLTkuNiAzLjItMTIuOCA2LjQtNi40IDkuNi02LjQgMjIuNCAwIDI4LjhsOTkuMiA5Mi44TDMzNiA2NTkuMmMwIDMuMiAwIDkuNiAzLjIgMTIuOCA2LjQgOS42IDE5LjIgMTIuOCAyOC44IDkuNmwxMjQuOC02NHoiIGZpbGw9IiNiYTlkNjMiIGRhdGEtc3BtLWFuY2hvci1pZD0iYTMxM3guc2VhcmNoX2luZGV4LjAuaTcuNjAyYTNhODFzZjNia20iIGNsYXNzPSJzZWxlY3RlZCIvPjwvc3ZnPg=="
						mode="aspectFill"
					></image>
				</view>
				<view class="flex1 row-title">总金额</view>
				<view class="icon_ row-input">
					<input class="input" v-model="form.red_money" placeholder="￥0.00" type="digit" @input="redMoneyFun" />
				</view>
			</view>
		</template>

		<!-- 以下是专属和拼手气通用 -->

		<view class="icon_ text_30 row">
			<view class="row-img">
				<image class="img" :src="icon_zf" mode="aspectFill"></image>
			</view>
			<view class="row-title">祝贺语</view>
			<view class="icon_ row-input flex1">
				<input class="input" v-model="form.remark" placeholder="恭喜发财,大吉大利" type="text" />
			</view>
		</view>

		<view class="icon_ bold_ money-box">
			<view class="text_48 money-text">￥</view>
			<view class="money-value">{{ form.red_money || '0.00' }}</view>
		</view>
		<view class="icon_ size_white text_32 button" hover-class="hover_class" @click="submit">塞钱进红包</view>
		<view class="icon_ text_28 illustrate">可直接使用收到的发红包</view>
		<member-selection-loading title="指定领取人" ref="memberSelectionLoadingRef" :group_id="to_data.id" @itemclick="itemclick"></member-selection-loading>
	</view>
</template>

<script>
// import memberSelection from '../components/memberSelection/index.vue';
import memberSelectionLoading from '../components/memberSelectionLoading/index.vue';
import { jsonUrl, show, to } from '@/utils/index.js';
import { icon_zf } from './icon.js';
let data = {};
export default {
	components: {
		memberSelectionLoading
	},
	data() {
		return {
			icon_zf,
			audioObj: {}, //语音数据
			to_data: {},
			tapIndex: 0,
			tapType: 'fortune',
			envelopeTexte: [
				{
					title: '拼手气红包',
					type: 'fortune'
				},
				{
					title: '专属红包',
					type: 'exclusive'
				}
			],
			toUser: {},
			userList: [],
			memberNumber: '--',
			ispayFlag: false,
			form: {
				userInfr: {},
				red_num: '', //数量
				red_money: '', //金额
				remark: '' //祝贺语
			}
		};
	},
	onLoad(e) {
		// data = jsonUrl(e);
		// this.to_data = data;
		this.memberNumber = 10;
		console.log(data);
		// this.getData();
	},

	onHide() {
		uni.$off('send_red_envelope');
		this.$refs.m_recorder?.onDelete();
	},
	onUnload() {
		uni.$off('send_red_envelope');
		this.$refs.m_recorder?.onDelete();
	},

	methods: {
		to,
		// 输入校验
		redNumFun(e) {
			const value = parseInt(e.detail.value);
			if (value <= 0) {
				this.$nextTick(() => {
					this.form.red_num = 1;
				});
				show('数量至少1个');
				return;
			}
			if (value > this.memberNumber) {
				this.$nextTick(() => {
					this.form.red_num = this.memberNumber;
				});
				show('数量不可高于群人数');
			}
			if (value > 500) {
				this.$nextTick(() => {
					this.form.red_num = 500;
				});
				show('不能大于500个');
			}
			if (this.form.red_money == '') return;
			if (this.tapType === 'fortune') {
				if (value != 0 && this.form.red_money / value < 0.01) {
					this.$nextTick(() => {
						this.form.red_num = parseInt(this.form.red_money / 0.01);
					});
					show('单个红包不可低于0.01蝌蚪');
					return;
				}
			}
		},
		redMoneyFun(e) {
			const value = Number(e.detail.value);
			if (value > 10000) {
				this.$nextTick(() => {
					this.form.red_money = 10000;
				});
				show('金额不能大于10000');
				return;
			}
			if (value != 0 && value < 0.01) {
				this.$nextTick(() => {
					this.form.red_money = 0.01;
				});
				show('金额不能小于0.01', 2000);
				return;
			}
			if (this.tapType === 'fortune') {
				if (value != 0 && value / this.form.red_num < 0.01) {
					this.$nextTick(() => {
						this.form.red_money = (this.form.red_num * 0.01).toFixed(2);
					});
					show('单个红包不可低于0.01蝌蚪');
					return;
				}
			}
		},
		// 向谁发红包
		onUser() {
			// this.$refs.memberSelectionRef.open();
			this.$refs.memberSelectionLoadingRef.open();
		},
		itemclick(e) {
			this.toUser = e;
		},

		onSelect() {
			uni.showActionSheet({
				itemList: this.envelopeTexte.map((item) => {
					return item.title;
				}),
				success: (res) => {
					this.tapIndex = res.tapIndex;
					this.tapType = this.envelopeTexte[res.tapIndex].type;
				},
				fail: function (res) {}
			});
		},
		async submit() {
			if (this.tapType === 'fortune') {
				if (!this.form.red_num) return show('需填写数量');
				if (!this.form.red_money) return show('需填写总金额');
			} else if (this.tapType === 'exclusive') {
				if (!this.toUser.member_id) return show('需选择收红包人');
				if (!this.form.red_money) return show('需填写金额');
			}

			// uni.$emit('send_red_envelope'); //类型是red_envelope
			// await show('发送成功', 1500, 'success');

			uni.$emit('send_red_envelope', {
				payload: {
					message_id: 'ODcyNTMxNzIyMjM4MjkxMTk4QVpCUQ==',
					remark: '恭喜发财，大吉大利',
					account_type_text: '蝌蚪红包',
					type: 'fortune',
					exclusive: {}
				},
				type: 'red_envelope'
			}); //类型是red_envelope
			await show('这里的数要据结合自己的业务处理');
			this.to();
		},
		//生成n位数字字母混合字符串
		generateMixed(n) {
			let chars = [
				'0',
				'1',
				'2',
				'3',
				'4',
				'5',
				'6',
				'7',
				'8',
				'9',
				'A',
				'B',
				'C',
				'D',
				'E',
				'F',
				'G',
				'H',
				'I',
				'J',
				'K',
				'L',
				'M',
				'N',
				'O',
				'P',
				'Q',
				'R',
				'S',
				'T',
				'U',
				'V',
				'W',
				'X',
				'Y',
				'Z'
			];
			let res = '';
			for (let i = 0; i < n; i++) {
				let id = Math.floor(Math.random() * 36);
				res += chars[id];
			}
			return res;
		}
	}
};
</script>

<style>
page {
	background-color: #ededed;
}
</style>
<style lang="scss" scoped>
.row {
	box-sizing: border-box;
	padding: 10rpx 20rpx 10rpx 30rpx;
	width: calc(100% - 60rpx);
	height: 100rpx;
	background-color: #fff;
	margin: 30rpx auto;
	border-radius: 10rpx;
	.row-img {
		width: 40rpx;
		height: 40rpx;
		margin-right: 20rpx;
	}
	.row-title {
	}
	.row-input {
		width: 200rpx;
		height: 100%;

		margin: 0 10rpx;
		.input {
			width: 100%;
			height: 50rpx;
			text-align: right;
		}
	}
}
.row-num {
	position: relative;
	top: -10rpx;
	width: calc(100% - 60rpx);
	margin: 0 auto;
	box-sizing: border-box;
	padding: 0 30rpx;
}
.title {
	position: relative;
	padding-top: 20rpx;
	top: 10rpx;
	width: calc(100% - 60rpx);
	margin: 0 auto;
	box-sizing: border-box;
	padding: 0 30rpx;
	color: #ba9d63;
	.title-icon {
		width: 26rpx;
		height: 26rpx;
		margin-left: 4rpx;
	}
}
.money-box {
	width: 100%;
	height: 100rpx;
	margin: 120rpx auto 40rpx auto;
	.money-text {
	}
	.money-value {
		font-size: 80rpx;
	}
}
.button {
	position: relative;
	overflow: hidden;
	width: 330rpx;
	height: 90rpx;
	border-radius: 16rpx;
	background-color: #ff6146;
	margin: 0 auto;
}
.illustrate {
	position: absolute;
	bottom: 150rpx;
	left: 0;
	width: 100%;
	height: 50rpx;
	color: #6a6a6a;
}

.row-user {
	.row-user-box {
		flex-direction: row-reverse;
		.row-user-img {
			width: 54rpx;
			height: 54rpx;
			margin-right: 16rpx;
			border-radius: 6rpx;
			overflow: hidden;
			background-color: #f1f1f1;
		}

		.row-user-name {
		}
	}

	.row-icon {
		width: 30rpx;
		height: 30rpx;
		margin-left: 10rpx;
	}
}

.row_input {
	box-sizing: border-box;
	margin-left: 20rpx;
}
</style>
