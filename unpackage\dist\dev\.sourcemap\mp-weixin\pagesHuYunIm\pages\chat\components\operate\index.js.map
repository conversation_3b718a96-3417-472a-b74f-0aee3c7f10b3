{"version": 3, "sources": [null, "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/operate/index.vue?af31", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/operate/index.vue?52c3", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/operate/index.vue?52c4", "uni-app:///pagesHuYunIm/pages/chat/components/operate/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/operate/index.vue?539d", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/operate/index.vue?2470"], "names": ["type", "icon", "title", "props", "userInfo", "default", "data", "show", "item", "list", "style", "styleStr", "computed", "Style", "StyleStr", "isSelf", "userId", "methods", "parseTimestamp", "timestamp", "console", "open", "length", "top", "left", "right", "init", "retractx", "close", "onClick", "uni", "showToast", "success", "name", "address", "latitude", "longitude", "add_emoji", "res", "url", "path", "quote", "recallMessage", "content", "deleteMessage", "API_collectEmoji", "http"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACiM;AACjM,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxBA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACwB5wB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;AACA;EACAA;EACAC;EACAC;AACA;AACA;EACAF;EACAC;EACAC;AACA;AACA;EACAF;EAEAC;EACAC;AACA;AACA;EACAF;EACAC;EACAC;AACA;AACA;EACAF;EACAC;EACAC;AACA;AACA;EACAF;EACAC;EACAC;AACA;AACA;EACAF;EACAC;EACAC;AACA;AAEA;EACAF;EACAC;EACAC;AACA;AAAA,eAEA;EACAC;IACAC;MACAJ;MACAK;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACA;MACAJ;MACA;IACA;IACA;IACAK;MACA;QAAAC;MACA;IACA;EACA;EACAC;IACA;AACA;AACA;AACA;AACA;IACAC;MACA;MAEA;QACA;QACA;UACA;QACA;;QAEA;QACA;UACA;UACA;;UAEA;UACA;YACAC;UACA;;UAEA;UACA;YACA;YACA;YACAA;UACA;UAEA;QACA;MACA;QACAC;MACA;MAEA;IACA;IAEAC;MACAD;MACA;MACA;MACA;MACA;MACA;MACA;QACAE;MACA;MACA;QACAC;MACA;MACA;QACAA;MACA;MACA;MACA;QACAA;QACAC;MACA;MACA;QACA;UACAD;UACAE;QACA;MACA;QACA;UACAF;UACAC;QACA;MACA;MACA;IACA;IACAE;MACA;QACA1B;QACAC;QACAC;MACA;MACA;MACAkB;MACA;MACA;MAEA;QACA;UAAAJ;QACA;UACAI;UACA;QACA;UACAO;QACA;MACA;QACAA;MACA;MACA;MACA;MACA;QACAlB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAA;MACA;MACA;IACA;IACAmB;MACA;MACA;IACA;IACAC;MACA;QACA;UACA;UACAC;YACAxB;YACAyB;YACAC;UACA;UACA;QACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;QAEA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;UACA;UACA;YACAC;YACAC;YACAC;YACAC;UACA;UACA;QACA;UACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;QAEA;UACA;MAAA;MAEA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,qBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACA;kBACAR;oBAAAS;oBAAAC;kBAAA;kBACAV;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAW;MACA;IACA;IACA;IACAC;MAAA;MACAtB;MACAU;QACAa;QACAX;UACA;YACA;YACA;YACA;YACA;UACA,wBACA;QACA;MACA;IACA;IACA;IACAY;MAAA;MACAd;QACAa;QACAX;UACA;YACA;UACA,wBACA;QACA;MACA;IACA;IACAa;MACA;QACAC,SACA,sBACA;UACAP;UACAC;QACA,GACA,MACA;UACA;UACA;QACA,EACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9UA;AAAA;AAAA;AAAA;AAAm7C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAv8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/pages/chat/components/operate/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=26d70dbe&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=26d70dbe&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"26d70dbe\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/chat/components/operate/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=26d70dbe&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.show ? _vm.__get_style([_vm.Style]) : null\n  var s1 = _vm.show ? _vm.__get_style([_vm.StyleStr]) : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.show = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"flex_r operate-box\" v-if=\"show\" :style=\"[Style]\" @click=\"show = false\">\n    <view :class=\"{ flex1: isSelf }\" style=\"transition: all 0.2s\"></view>\n    <view class=\"flex_r operate\">\n      <view\n        class=\"flex_c_c size_white operate-item\"\n        v-for=\"(item, index) in list\"\n        :key=\"index\"\n        @click.stop=\"onClick(item)\"\n        v-if=\"item.icon\"\n      >\n        <view class=\"operate-item-icon\">\n          <image class=\"img\" :src=\"item.icon\" mode=\"aspectFill\"></image>\n        </view>\n        <view class=\"text_24\">\n          {{ item.title }}\n        </view>\n      </view>\n      <view class=\"operate-str\" :style=\"[StyleStr]\"></view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { 自己的信息 } from '@/TEST/index'\nimport { getLocation, show } from '@/utils/index.js'\nconst transmit = {\n  type: 'transmit',\n  icon: 'data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTU2NS45MzQgODE3LjU3NGEzNC44MTYgMzQuODE2IDAgMCAwIDkuODE4IDI5LjM5NGwuMzAyLjI0MWEzNS41MzkgMzUuNTM5IDAgMCAwIDI1LjYgMTAuOTYzYzExLjE0MyAwIDIwLjY2LTUuNDIgMjcuMjI2LTEzLjMxMmwzNTQuNTQ1LTM4Ny4wNzJhMzUuMTE3IDM1LjExNyAwIDAgMCAxMC4yNC0yNy4xMDYgMzUuMDU3IDM1LjA1NyAwIDAgMC0xMC4yNC0yNy4xMDZMNjI2Ljg5MiAxNC4zMzZhMzYuNTAzIDM2LjUwMyAwIDAgMC01MS4yIDAgMzQuOTM2IDM0LjkzNiAwIDAgMC05Ljc1OCAyOS4zOTVWMjUzLjM1Yy0yOTUuOTk2IDAtNTM1Ljk3NCAyMzguODkzLTUzNS45NzQgNTMzLjY4NGE1MjkuNDY4IDUyOS40NjggMCAwIDAgNDQuNDU0IDIxMi41MUMxMTYuNyA3NzcuMjc2IDMyOS44MTIgNjA4LjQzNyA1NjUuMzMyIDYwOC40MzdsLjYwMiAyMDkuMTM3eiIgZmlsbD0iI2ZmZiIvPjwvc3ZnPg==',\n  title: '转发'\n}\nconst copy = {\n  type: 'copy',\n  icon: 'data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTc2MiA4NjYuMTd2MzMuMzNjMCAyNy42Mi0yMi4zOCA1MC01MCA1MEgxODdjLTI3LjYyIDAtNTAtMjIuMzgtNTAtNTB2LTY1MGMwLTI3LjYyIDIyLjM4LTUwIDUwLTUwaDMzLjMzdjYxNi42N2MwIDI3LjYyIDIyLjM4IDUwIDUwIDUwSDc2MnoiIGRhdGEtc3BtLWFuY2hvci1pZD0iYTMxM3guc2VhcmNoX2luZGV4LjAuaTEuNGE2MzNhODFFQkU0bnEiIGZpbGw9IiNmZmYiLz48cGF0aCBkPSJNODg3IDMwMC44NFY3NzQuNWMwIDI3LjYyLTIyLjM4IDUwLTUwIDUwSDMxMmMtMjcuNjIgMC01MC0yMi4zOC01MC01MHYtNjUwYzAtMjcuNjIgMjIuMzgtNTAgNTAtNTBoMzQ4LjY2YzQuNDIgMCA4LjY2IDEuNzYgMTEuNzggNC44OGwyMDkuNjcgMjA5LjY3YzMuMTMgMy4xMyA0Ljg5IDcuMzcgNC44OSAxMS43OXoiIGRhdGEtc3BtLWFuY2hvci1pZD0iYTMxM3guc2VhcmNoX2luZGV4LjAuaTAuNGE2MzNhODFFQkU0bnEiIGZpbGw9IiNmZmYiLz48cGF0aCBkPSJNNjUwLjg5IDI3Ny4yN1Y5MS4xN2wyMTkuNDQgMjE5LjQ0SDY4NC4yMmMtMTguNCAwLTMzLjMzLTE0LjkzLTMzLjMzLTMzLjM0eiIgZmlsbD0iIzRjNGM0YyIgZGF0YS1zcG0tYW5jaG9yLWlkPSJhMzEzeC5zZWFyY2hfaW5kZXguMC5pMi40YTYzM2E4MUVCRTRucSIgY2xhc3M9InNlbGVjdGVkIi8+PC9zdmc+',\n  title: '复制'\n}\nconst hide = {\n  type: 'hide',\n\n  icon: 'data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTg4Mi43ODEgNzAuNzg0YTY0IDY0IDAgMCAxIDcuODcyIDkwLjExMmwtNzMuNiA4Ny42OGM2Ny45NjggNTYuNDQ4IDEzMy42MzIgMTMyLjY3MiAxOTYuOTI4IDIyOC43MzZhNjQgNjQgMCAwIDEgNC40OCA2Mi40bC00LjAzMiA3LjM2Qzg2NS4zMSA3NzkuNzEyIDY5OC4wNzcgODk2IDUxMi42NyA4OTZhNDM3Ljc2IDQzNy43NiAwIDAgMS0xOTguNTkyLTQ3LjkzNmwtODEuNiA5Ny4yOGE2NCA2NCAwIDAgMS05OC4wNDgtODIuMjRsNzIuMTI4LTg2LjA4QzEzOC4xNDEgNzIwLjQ0OCA3Mi44NjEgNjQzLjU4NCAxMC42NTMgNTQ2LjU2YTY0IDY0IDAgMCAxLTQuMDMyLTYxLjc2bDQuMDMyLTcuMjMyQzE1OS44MzcgMjQ0LjQ4IDMyNy4xOTcgMTI4IDUxMi43MzMgMTI4YzY3LjY0OCAwIDEzMy41MDQgMTYuMzIgMTk3LjM3NiA0OS4wMjRsODIuNTYtOTguMzY4YTY0IDY0IDAgMCAxIDkwLjExMi03Ljg3MnptLTE2MS45MiAyOTIuNDhsLTg4IDEwNC44OTZhMTI4IDEyOCAwIDAgMS0xNDIuNTI4IDE2OS45MmwtODguMTI4IDEwNC45NmEyNTYgMjU2IDAgMCAwIDMxOC43Mi0zNzkuNzc2ek01MTIuNTQxIDI1NmEyNTYgMjU2IDAgMCAwLTIwOC4zMiA0MDQuNzM2bDg3Ljg3Mi0xMDQuODMyQTEyOCAxMjggMCAwIDEgNTM0Ljc1IDM4NS45Mmw4OC4xMjgtMTA0Ljk2QTI1NS4wNCAyNTUuMDQgMCAwIDAgNTEyLjU0MSAyNTZ6IiBmaWxsPSIjZmZmIi8+PC9zdmc+',\n  title: '隐藏'\n}\nconst quote = {\n  type: 'quote',\n  icon: 'data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTUxMiAxMDI0QTUxMi41NzYgNTEyLjU3NiAwIDAgMSAwIDUxMiA1MTIuNTc2IDUxMi41NzYgMCAwIDEgNTEyIDBhNTEyLjU3NiA1MTIuNTc2IDAgMCAxIDUxMiA1MTIgNTEyLjU3NiA1MTIuNTc2IDAgMCAxLTUxMiA1MTJ6bTI1LjYtNTExLjA0djE5MmgxODIuNTI4di0xOTJINjAyLjgxNmEyMDQuMjI0IDIwNC4yMjQgMCAwIDEgMTA4LjgtMTM1LjkzNmwtMjcuMDA4LTU4LjA0OGEyNjIuNCAyNjIuNCAwIDAgMC0xNDYuMDQ4IDE5My45MnptLTI1NiAwdjE5MmgxODIuNDY0di0xOTJIMzQ2Ljg4YTIwNC4yMjQgMjA0LjIyNCAwIDAgMSAxMDguOC0xMzUuOTM2TDQyOC44IDMxOS4wNGEyNjIuNCAyNjIuNCAwIDAgMC0xNDYuMTEyIDE5My45MnoiIGRhdGEtc3BtLWFuY2hvci1pZD0iYTMxM3guc2VhcmNoX2luZGV4LjAuaTIwLjRhNjMzYTgxRUJFNG5xIiBjbGFzcz0ic2VsZWN0ZWQiIGZpbGw9IiNmZmYiLz48L3N2Zz4=',\n  title: '引用'\n}\nconst thank = {\n  type: 'thank',\n  icon: 'data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTQ5MC42NjcgNzQ2LjExMkMyOTkuODQgNzM1LjUzMSAxNDguMzMgNTc3LjAwMiAxNDguMzMgMzgzLjAxOWMwLTcwLjIzIDE5Ljg0LTEzNS44MDggNTQuMjI5LTE5MS4zODIgNTUuMzM5IDQuNjMgMTMwLjg1OSAxNy4zODcgMTg3Ljg2MSA1MS41NDJDNDEwLjQxMSAxNTkuOTM2IDQ1Mi44NDMgMTAxLjMzMyA1MTIgNjQuMDg1YzYyLjgyNyAzNy4yNDggOTguNzczIDk3LjY0MyAxMjIuMDkgMTc5LjA5NCA0NS42MTEtMzUuNTQyIDEyNi4yMy00Ny45MTUgMTg1LjA0Ni01Mi4wNTRhMzYyLjY4OCAzNjIuNjg4IDAgMCAxIDU0LjUyOCAxOTEuODcyYzAgMTkzLjM0NC0xNTAuNDQzIDM1MS40MjQtMzQwLjMzIDM2Mi45ODdWOTYwYTIxLjMzMyAyMS4zMzMgMCAwIDEtNDIuNjY3IDBWNzQ2LjExMnpNMzg1LjkyIDkyNi4xODdDMzM4LjgzNyA4MjIuOTk3IDI1MS4xNTcgNzY4LjUzMyAxNDkuMjI3IDc2OGEyMS4zMzMgMjEuMzMzIDAgMSAxIC4yMTMtNDIuNjY3YzExOC4yNzIuNTk4IDIyMS4xODQgNjQuNTU1IDI3NS4zMDcgMTgzLjE0N2EyMS4zMzMgMjEuMzMzIDAgMSAxLTM4LjgyNyAxNy43MDd6bTIxMy4zMzMtMTcuNzA3YzU0LjEyMy0xMTguNjEzIDE1Ny4wMzUtMTgyLjU1IDI3NS4zMDctMTgzLjE0N2EyMS4zMzMgMjEuMzMzIDAgMSAxIC4yMTMgNDIuNjY3Yy0xMDEuOTMuNTEyLTE4OS42MSA1NC45OTctMjM2LjY5MyAxNTguMTg3YTIxLjMzMyAyMS4zMzMgMCAxIDEtMzguODI3LTE3LjcwN3oiIGZpbGw9IiNmZmYiIGRhdGEtc3BtLWFuY2hvci1pZD0iYTMxM3guc2VhcmNoX2luZGV4LjAuaTguMWRiNDNhODFUTVJ3UTIiIGNsYXNzPSJzZWxlY3RlZCIvPjwvc3ZnPg==',\n  title: '谢谢'\n}\nconst map = {\n  type: 'map',\n  icon: 'data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTg5OC4yIDExMy4xYy0xMS40LTYuNC0yNS4yLTYuNC0zNi42IDBMMTMwLjEgNDc2LjRjLTEyIDUtMjAuNCAxNi4yLTIxLjkgMjkuMS0uNCAxMS42IDUuMSAyMi42IDE0LjYgMjkuMWwxNzUuNiAxMDljMTQuNSA4LjUgMzMgNS41IDQ0LTcuM2wzODMuNC0zNDQuOWMyLjUtMi4yIDYuMi0yLjIgOC43IDAgMS4yIDEgMS45IDIuNCAyIDMuOS4xIDEuNS0uNCAzLTEuNSA0LjFsLTM0MS40IDM2NmMtNS42IDUuOC04LjIgMTMuOC03LjIgMjEuOHYxNTkuOWMtLjMgMTUuMyA4LjIgMjkuNSAyMS45IDM2LjQgMTIuNSA1LjYgMjcuMiAyLjYgMzYuNi03LjNsODcuOS04Ny4yIDE3NS42IDExNi4zYzExIDYuNCAyMy45IDkgMzYuNiA3LjMgMTEuMy01LjkgMTkuMy0xNi42IDIxLjktMjkuMWwxNDYuMy03MjYuNmM1LjItMTYuMi0uOC0zMy44LTE0LjctNDMuNWwtLjMtLjN6IiBmaWxsPSIjZmZmIiBkYXRhLXNwbS1hbmNob3ItaWQ9ImEzMTN4LnNlYXJjaF9pbmRleC4wLmkxNS43Yzg1M2E4MUQ0T2VKMiIgY2xhc3M9InNlbGVjdGVkIi8+PC9zdmc+',\n  title: '导航'\n}\nconst deletex = {\n  type: 'delete',\n  icon: 'data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTIyNi42NSA4NTAuMTQ3YzAgNTkuNDQxIDQ3Ljg4NiAxMTAuMDYyIDEwNi45NzggMTEwLjA2MmgzNTYuNjU2YzU5LjA5MiAwIDEwNi45NzktNTAuNjIgMTA2Ljk3OS0xMTAuMDYybDcxLjM0OS01NzYuMzQ1SDE1NS4zbDcxLjM1IDU3Ni4zNDV6bTM5Ny42Ny00NjQuNTU1aDY5Ljk5MXY0NjIuODI3aC02OS45OTJWMzg1LjU5MnptLTE1NC42NDggMGg4NC42NTV2NDYyLjgyN2gtODQuNjU1VjM4NS41OTJ6bS0xMzkuOTg0IDBoNjkuOTkydjQ2Mi44MjdoLTY5Ljk5MlYzODUuNTkyem01MjEuMTA3LTI1MS43ODlINjE4Ljk3OHMtMTUuOTc2LTcwLjAxLTM1LjY3NC03MC4wMUg0NDAuNjUxYy0xOS42OTggMC0zNS42NzQgNzAuMDEtMzUuNjc0IDcwLjAxSDE3My4xNmMtMjkuNTQ2IDAtNTMuNDkgMjUuODI1LTUzLjQ5IDU1LjU0NXY1Ni4yNDVoNzg0LjY2di01Ni4yNDVjMC0yOS43Mi0yMy45ODktNTUuNTQ1LTUzLjUzNS01NS41NDV6IiBkYXRhLXNwbS1hbmNob3ItaWQ9ImEzMTN4LnNlYXJjaF9pbmRleC4wLmkxMi40YTYzM2E4MUVCRTRucSIgY2xhc3M9InNlbGVjdGVkIiBmaWxsPSIjZmZmIi8+PC9zdmc+',\n  title: '删除'\n}\n\nconst add_emoji = {\n  type: 'add_emoji',\n  icon: 'data:image/svg+xml;base64,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',\n  title: '添加'\n}\n\nexport default {\n  props: {\n    userInfo: {\n      type: Object,\n      default: {}\n    }\n  },\n  data() {\n    return {\n      show: false,\n      item: {},\n      list: [],\n      style: {},\n      styleStr: {}\n    }\n  },\n  computed: {\n    Style() {\n      const style = this.style\n      return style\n    },\n    StyleStr() {\n      const style = this.styleStr\n      style.transition = 'all 0.2s'\n      return style\n    },\n    // 是否本人isMy\n    isSelf() {\n      const { userId = '' } = this.userInfo\n      return this.item.userId === `${userId}`\n    }\n  },\n  methods: {\n    /**\n     * 解析时间字符串为时间戳\n     * @param {string|number} timeStr - 时间字符串或时间戳\n     * @returns {number} 时间戳（毫秒）\n     */\n    parseTimestamp(timeStr) {\n      if (!timeStr) return 0\n\n      try {\n        // 如果已经是数字类型的时间戳\n        if (typeof timeStr === 'number') {\n          return timeStr\n        }\n\n        // 字符串格式处理\n        if (typeof timeStr === 'string') {\n          // 先尝试直接解析\n          let timestamp = new Date(timeStr).getTime()\n\n          // 如果解析失败，尝试替换格式后解析\n          if (isNaN(timestamp)) {\n            timestamp = new Date(timeStr.replace(/-/g, '/')).getTime()\n          }\n\n          // 如果还是失败，尝试其他格式\n          if (isNaN(timestamp)) {\n            // 处理可能的其他格式，如 \"2024-01-01T10:30:00\"\n            const isoFormat = timeStr.replace(' ', 'T')\n            timestamp = new Date(isoFormat).getTime()\n          }\n\n          return isNaN(timestamp) ? 0 : timestamp\n        }\n      } catch (error) {\n        console.error('解析时间失败:', error, timeStr)\n      }\n\n      return 0\n    },\n\n    open(item, e) {\n      console.log('🚀 ~ open ~ item:', item)\n      this.item = item\n      this.init(item)\n      let top = e.top\n      let title = this.list[this.list.length - 1].title\n      let length = this.list.length\n      if (!title) {\n        length = length - 1\n      }\n      if (length >= 5) {\n        top = e.top - uni.upx2px(100)\n      }\n      this.style = {\n        top: `${top - uni.upx2px(160)}px`\n      }\n      const value = (e.right - e.left) / 2 + uni.upx2px(122)\n      this.styleStr = {\n        top: `${e.top - uni.upx2px(31)}px`,\n        left: `${value + uni.upx2px(122)}px`\n      }\n      if (this.isSelf) {\n        this.styleStr = {\n          top: `${e.top - uni.upx2px(31)}px`,\n          right: `${value}px`\n        }\n      } else {\n        this.styleStr = {\n          top: `${e.top - uni.upx2px(31)}px`,\n          left: `${value}px`\n        }\n      }\n      this.show = true\n    },\n    init(item) {\n      let retractx = {\n        type: 'retractx',\n        icon: 'data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTMyOS4wNDUgMTM3LjY4NWEzMiAzMiAwIDAgMSA0OC4xMjggNDEuOTg0bC0yLjg1OCAzLjI4Ni0xNTguNDIyIDE1OC4zNzggMTU4LjQyMiAxNTguMzc5YTMyIDMyIDAgMCAxIDIuODU4IDQxLjk4NGwtMi44NTggMy4yODVhMzIgMzIgMCAwIDEtNDEuOTg0IDIuODU5bC0zLjI4Ni0yLjg1OS0xODAuOTkyLTE4MS4wMzRhMzIgMzIgMCAwIDEtMi44NTgtNDEuOTg0bDIuODU4LTMuMjQzIDE4MC45OTItMTgxLjAzNXoiIGZpbGw9IiNmZmYiLz48cGF0aCBkPSJNNjQwIDMwOS4zMzNhMjQ1LjMzMyAyNDUuMzMzIDAgMCAxIDkuMzg3IDQ5MC40OTZMNjQwIDgwMEgxNzAuNjY3YTMyIDMyIDAgMCAxLTQuMzUyLTYzLjcwMWw0LjM1Mi0uMjk5SDY0MGExODEuMzMzIDE4MS4zMzMgMCAwIDAgOC43OS0zNjIuNDUzbC04Ljc5LS4yMTRIMTcwLjY2N2EzMiAzMiAwIDAgMS00LjM1Mi02My43MDFsNC4zNTItLjI5OUg2NDB6IiBmaWxsPSIjZmZmIi8+PC9zdmc+',\n        title: '撤回'\n      }\n      const MAX_RECALLABLE_TIME = 5 * 60 * 1000 // 5分钟以内的消息才可以撤回\n      console.log('🚀 ~ init ~ item:', item)\n      // 使用工具函数解析时间\n      const timestamp = this.parseTimestamp(item.createTime)\n\n      if (timestamp > 0 && Date.now() - timestamp < MAX_RECALLABLE_TIME) {\n        const { userId = '' } = this.userInfo\n        if (item.status === '正常' && item.userId === userId) {\n          console.log('🚀 ~ 可撤回:', item.status)\n          //\n        } else {\n          retractx = {}\n        }\n      } else {\n        retractx = {}\n      }\n      // let list = [quote, hide, deletex]\n      let list = []\n      if (item.msgType === 'text' || item.msgType === 'text_quote') {\n        list.unshift(copy)\n      }\n      // else if (item.msgType === 'red_envelope') {\n      //   list.unshift(thank)\n      // } else if (item.msgType === 'map') {\n      //   list.unshift(map)\n      // } else if (item.msgType === 'emoji_pack') {\n      //   list.unshift(add_emoji)\n      // }\n      // 语音和红包不可转发\n      // if (item.msgType !== 'audio' && item.msgType !== 'red_envelope') {\n      //   list.unshift(transmit)\n      // }\n      // 红包不能撤回\n      if (item.msgType != 'red_envelope') {\n        list.push(retractx)\n      }\n      this.list = list\n    },\n    close() {\n      if (!this.show) return\n      this.show = false\n    },\n    onClick(item) {\n      switch (item.type) {\n        case 'copy':\n          // 复制\n          uni.setClipboardData({\n            data: this.item.payload.text,\n            showToast: true,\n            success: () => {}\n          })\n          break\n        case 'hide':\n          // 删除/隐藏\n          this.item.isHide = 1\n          break\n        case 'quote':\n          // 引用\n          this.quote()\n          break\n\n        case 'retractx':\n          // 撤回\n          this.recallMessage()\n          break\n        case 'thank':\n          // 引用\n          this.$emit('thank', this.item)\n          break\n        case 'map':\n          // 导航\n          getLocation({\n            name: this.item.payload.title,\n            address: this.item.payload.address,\n            latitude: this.item.payload.latitude,\n            longitude: this.item.payload.longitude\n          })\n          break\n        case 'add_emoji':\n          // 添加为表情包\n          this.add_emoji()\n          break\n        case 'delete':\n          this.deleteMessage()\n          break\n        case 'transmit':\n          this.$emit('transmit', this.item)\n          break\n\n        default:\n          break\n      }\n      this.close()\n    },\n    // 添加为表情包\n    async add_emoji() {\n      const { url, path = '' } = this.item.payload\n      const res = await this.API_collectEmoji(url, path)\n      if (res) {\n        uni.$emit('collectionEmoji', { url, path })\n        uni.$off('collectionEmoji')\n      }\n    },\n    // 引用\n    quote() {\n      this.$emit('quote', this.item)\n    },\n    // 撤回\n    recallMessage() {\n      console.log(this.item)\n      uni.showModal({\n        content: '撤回该条信息？',\n        success: (res) => {\n          if (res.confirm) {\n            this.item.recalled = true\n            //调用通用方法，传类型\n            this.$emit('recallMessage', this.item)\n            //\n          } else if (res.cancel) {\n          }\n        }\n      })\n    },\n    // 删除\n    deleteMessage() {\n      uni.showModal({\n        content: '删除该条信息？不可恢复！',\n        success: (res) => {\n          if (res.confirm) {\n            this.item.isHide = 1\n          } else if (res.cancel) {\n          }\n        }\n      })\n    },\n    API_collectEmoji(url, path) {\n      return new Promise((res) => {\n        http.get(\n          'Emoji/collectEmoji',\n          {\n            url,\n            path\n          },\n          true,\n          (r) => {\n            if (r.data.code == 0) return res(r)\n            return show(r.data.msg), res(false)\n          }\n        )\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.operate-box {\n  position: fixed;\n  z-index: 9;\n  left: 122rpx;\n  width: calc(100vw - 244rpx);\n  transition: all 0.2s;\n}\n.operate_box {\n  flex-direction: row-reverse;\n}\n.operate {\n  position: relative;\n  box-sizing: border-box;\n  padding: 20rpx;\n  border-radius: 14rpx;\n  background-color: #4c4c4c;\n  max-width: 440rpx;\n  box-shadow: rgba(76, 76, 76, 0.3) 0rpx 2rpx 20rpx;\n  flex-wrap: wrap;\n  .operate-item-icon {\n    width: 40rpx;\n    height: 40rpx;\n    margin-bottom: 6rpx;\n  }\n  .operate-item {\n    width: 100rpx;\n    height: 100rpx;\n  }\n}\n.operate-str {\n  position: fixed;\n  width: 18rpx;\n  height: 18rpx;\n  border-radius: 2px;\n  transform: rotate(45deg);\n  background-color: #4c4c4c;\n  transition: all 0.2s;\n}\n</style>\n", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=26d70dbe&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=26d70dbe&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755074487072\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}