# MQTT Toolkit 分包

这是一个完整的 MQTT 工具分包，包含了 MQTT 连接、消息处理、配置管理等功能，可以直接在 UniApp 项目中使用。

## 🚀 特性

- 🔄 **自动重连**: 内置智能重连机制，网络断开后自动恢复
- 💓 **心跳保活**: 自动发送心跳包保持连接活跃
- 📦 **单例模式**: 全局共享连接实例，避免重复连接
- ⚙️ **配置化管理**: 统一的配置文件，支持多环境切换
- 🎯 **主题模板**: 预定义主题模板，避免字符串拼接错误
- 🛡️ **错误处理**: 完善的异常处理和状态管理
- 📱 **跨平台**: 支持 Web、UniApp、小程序等多种环境
- 🔧 **TypeScript 友好**: 完整的类型定义支持

### 基本使用

```javascript
import { MqttClient, createUserInfo } from 'mqtt-toolkit'
import mqtt from 'mqtt' // 或者你的MQTT库

// 1. 创建用户信息
const userInfo = createUserInfo(
  'user123', // 用户ID
  'John Doe', // 昵称
  'channel001', // 频道代码
  'your-jwt-token', // 认证token
  'avatar-url', // 头像URL (可选)
  'DEV' // 环境 (DEV/TEST/PROD)
)

// 2. 配置事件回调
const callbacks = {
  onConnect: () => console.log('连接成功'),
  onMessage: (topic, message) => console.log('收到消息:', message),
  onError: (error) => console.error('连接错误:', error)
}

// 3. 设置MQTT库并连接
MqttClient.setMqttLib(mqtt)
MqttClient.connect(userInfo, callbacks)

// 4. 发送消息
import { createChatMessage, TOPIC_TEMPLATES } from 'mqtt-toolkit'

const message = createChatMessage('Hello World', 'user123', 'John Doe')
MqttClient.publish(TOPIC_TEMPLATES.CHAT_ROOM('room001'), message)
```

### Vue/UniApp 中使用

```vue
<template>
  <view>
    <button @click="sendMessage">发送消息</button>
  </view>
</template>

<script>
import { MqttClient, createUserInfo, createChatMessage } from 'mqtt-toolkit'
import mqtt from './lib/mqtt.min.js'

export default {
  onLoad() {
    const userInfo = createUserInfo('user123', 'John', 'channel001', 'token')

    MqttClient.setMqttLib(mqtt)
    MqttClient.connect(userInfo, {
      onConnect: () => console.log('连接成功'),
      onMessage: (topic, msg) => this.handleMessage(msg)
    })
  },

  onUnload() {
    MqttClient.disconnect()
  },

  methods: {
    sendMessage() {
      const message = createChatMessage('Hello', 'user123', 'John')
      MqttClient.publish('/chat/room/general', message)
    },

    handleMessage(message) {
      console.log('收到消息:', message)
    }
  }
}
</script>
```

## 📚 API 文档

### MqttClient

#### 方法

##### `setMqttLib(mqtt)`

设置 MQTT 库实例

```javascript
import mqtt from 'mqtt'
MqttClient.setMqttLib(mqtt)
```

##### `connect(userInfo, callbacks, customOptions, mqttLib)`

连接 MQTT 服务器

```javascript
MqttClient.connect(
  userInfo,
  {
    onConnect: () => {},
    onMessage: (topic, message) => {},
    onReconnect: () => {},
    onError: (error) => {},
    onEnd: () => {}
  },
  {
    keepalive: 60,
    connectTimeout: 30000
  }
)
```

##### `publish(topic, message, options, callback)`

发布消息

```javascript
MqttClient.publish('/chat/room/001', 'Hello World')
MqttClient.publish('/chat/room/001', { type: 'text', content: 'Hello' })
```

##### `subscribe(topics, options, callback)`

订阅主题

```javascript
MqttClient.subscribe('/chat/room/001')
MqttClient.subscribe(['/chat/room/001', '/chat/room/002'])
```

##### `disconnect(force, callback)`

断开连接

```javascript
MqttClient.disconnect()
MqttClient.disconnect(true) // 强制断开
```

### 配置工具

#### `createUserInfo(userId, nickname, channelCode, token, avatar, env)`

创建用户信息对象

#### `createChatMessage(content, userId, nickname, groupId, messageType)`

创建聊天消息

#### `createSystemMessage(content, type)`

创建系统消息

### 主题模板

```javascript
import { TOPIC_TEMPLATES } from 'mqtt-toolkit'

TOPIC_TEMPLATES.USER_CHANNEL(userId) // 用户频道
TOPIC_TEMPLATES.CHAT_ROOM(roomId) // 聊天室
TOPIC_TEMPLATES.PRIVATE_CHAT(fromId, toId) // 私聊
TOPIC_TEMPLATES.GROUP_CHAT(groupId) // 群聊
TOPIC_TEMPLATES.SYSTEM_NOTIFY(userId) // 系统通知
```

## 🔧 配置

### 环境配置

```javascript
// config/mqttConfig.js
export const MQTT_CONFIG = {
  WS_URL: {
    DEV: 'ws://localhost:8083/mqtt',
    TEST: 'ws://test.example.com:8083/mqtt',
    PROD: 'wss://prod.example.com:443/mqtt'
  },

  DEFAULT_OPTIONS: {
    keepalive: 60,
    connectTimeout: 30000,
    reconnectPeriod: 1000
  }
}
```

### 自定义配置

```javascript
const customOptions = {
  keepalive: 30,
  connectTimeout: 10000,
  reconnectPeriod: 2000,
  clean: true
}

MqttClient.connect(userInfo, callbacks, customOptions)
```

## 📁 项目结构

```
mqtt-toolkit/
├── index.js                    # 主入口文件
├── package.json               # 包配置
├── README.md                  # 说明文档
├── lib/
│   └── mqttClient.js          # 核心客户端类
├── config/
│   └── mqttConfig.js          # 配置文件
├── examples/
│   ├── basic-usage.js         # 基本使用示例
│   └── vue-uniapp-example.vue # Vue/UniApp示例
└── docs/
    └── api.md                 # 详细API文档
```

## 🌟 高级功能

### 自定义心跳间隔

```javascript
// 设置15秒心跳
MqttClient.setPingInterval(15000)
```

### 批量订阅

```javascript
const topics = [TOPIC_TEMPLATES.CHAT_ROOM('room1'), TOPIC_TEMPLATES.CHAT_ROOM('room2'), TOPIC_TEMPLATES.SYSTEM_NOTIFY('user123')]

MqttClient.subscribe(topics)
```

### 消息格式验证

```javascript
import { validateUserInfo } from 'mqtt-toolkit'

try {
  validateUserInfo(userInfo)
  // 用户信息有效
} catch (error) {
  console.error('用户信息无效:', error.message)
}
```

## 🔄 迁移指南

### 从原始 MQTT 代码迁移

**之前:**

```javascript
// 70+ 行复杂的MQTT连接代码
const options = { clientId: userId, username: channelCode, ... }
const client = mqtt.connect(wsUrl, options)
client.on('connect', function() { /* 复杂逻辑 */ })
// ... 更多事件处理
```

**现在:**

```javascript
// 10+ 行简洁代码
const userInfo = createUserInfo(userId, nickname, channelCode, token)
MqttClient.setMqttLib(mqtt)
MqttClient.connect(userInfo, { onConnect: () => console.log('连接成功') })
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 🆘 支持

如有问题，请查看：

- [API 文档](./docs/api.md)
- [示例代码](./examples/)
- [GitHub Issues](your-repo-url/issues)
