{"version": 3, "sources": ["webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/components/m-screen-animation-lihua/m-screen-animation-lihua.vue?1f25", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/components/m-screen-animation-lihua/m-screen-animation-lihua.vue?f991", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/components/m-screen-animation-lihua/m-screen-animation-lihua.vue?c75d", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/components/m-screen-animation-lihua/m-screen-animation-lihua.vue?ea71", "uni-app:///components/m-screen-animation-lihua/m-screen-animation-lihua.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/components/m-screen-animation-lihua/m-screen-animation-lihua.vue?3c4a", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/components/m-screen-animation-lihua/m-screen-animation-lihua.vue?6056"], "names": ["window", "setTimeout", "props", "particleCount", "type", "default", "angle", "spread", "startVelocity", "decay", "ticks", "zIndex", "colors", "canvasId", "width", "height", "x", "y", "data", "show_canvas", "pixelRatio", "mounted", "methods", "show", "parseInt16", "canvasIdErrorCallback", "console", "initCanvas", "fireCanvasBox", "fireworksDraw", "n", "color", "i", "r", "a", "ribbon", "depth", "wobble", "velocity", "angle2D", "tiltAngle", "g", "b", "tick", "totalTicks", "random", "tiltSin", "tiltCos", "wobbleX", "wobbleY", "minBrowserRefreshTime", "e"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AAC4E;AACL;AACa;;;AAGpF;AACwL;AACxL,gBAAgB,yLAAU;AAC1B,EAAE,8FAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA8tB,CAAgB,8rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;ACOlvB;AACA;AACA;AACA,4BACAA,WACAA,gCACAA,sCACAA,mCACAA,iCACAA,mCACA;EACAC;AACA;AACA;AACA;AAAA,gBACA;EACAC;IACA;AACA;AACA;IACAC;MACAC;MACAC;IACA;IACA;AACA;AACA;AACA;AACA;AACA;IACAC;MACAF;MACAC;IACA;IACA;AACA;AACA;IACAE;MACAH;MACAC;IACA;IACA;AACA;AACA;IACAG;MACAJ;MACAC;IACA;IACA;AACA;AACA;IACAI;MACAL;MACAC;IACA;IACA;AACA;AACA;IACAK;MACAN;MACAC;IACA;IACA;AACA;AACA;IACAM;MACAP;MACAC;IACA;IACA;AACA;AACA;IACAO;MACAR;MACAC;QAAA;MAAA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;AACA;AACA;IACAS;MACAV;MACAC;QACA;MACA;IACA;IACA;AACA;AACA;IACAU;MACAX;MACAC;QACA;MACA;IACA;IACA;AACA;AACA;IACAW;MACAZ;MACAC;QACA;MACA;IACA;IACA;AACA;AACA;IACAY;MACAb;MACAC;QACA;MACA;IACA;EACA;EACAa;IACA;MACAC;MACA;AACA;AACA;MACAC;IACA;EACA;EACAC;IACA;EAAA,CACA;EACAC;IACAC;MAAA;MACA;MACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACAC;MACAC;IACA;IACAC;MACAC;MACAA;MACAA;MACAA;MACAA;MACA;IACA;IACAC;MACA;MACA;MAAA;MACA1B;MACA;MACA;MACA;MACA;MACA;QACA2B;UACAd;UACAC;UACAX;UACAC;UACAC;UACAuB;UACArB;UACAD;QACA,GACAuB,OACAC,+BACAC;QACAC;UACA;UACAnB;UACAC;UACAmB;UACAC;UACAC;UACAC;UACAC;UACAT,QACAC,+CACAA,+DACA;YACA;YACAC;YACAQ;YACAC;UACA;UACAC;UACAC;UACAnC;UACAoC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;MACA;MACAC;QACA;QAEA;UACAtB,sBACAA,yBACAO;YACAgB,yCACAA,uDACAA,iBACAA,uBACAA,4CACAA,mCACAA,mCACAA,8BACAA,qDACAA;YACA;YACAvB,kIACAA,2BACAA,wDACAA,qFACAA,kHACAA,qFACAA,2BACAA;YACA;UACA;UACAO;QACA;UACA;QAAA;MAEA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACvPA;AAAA;AAAA;AAAA;AAA2hC,CAAgB,28BAAG,EAAC,C;;;;;;;;;;;ACA/iC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/m-screen-animation-lihua/m-screen-animation-lihua.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./m-screen-animation-lihua.vue?vue&type=template&id=4c8418a4&\"\nvar renderjs\nimport script from \"./m-screen-animation-lihua.vue?vue&type=script&lang=js&\"\nexport * from \"./m-screen-animation-lihua.vue?vue&type=script&lang=js&\"\nimport style0 from \"./m-screen-animation-lihua.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/m-screen-animation-lihua/m-screen-animation-lihua.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-screen-animation-lihua.vue?vue&type=template&id=4c8418a4&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-screen-animation-lihua.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-screen-animation-lihua.vue?vue&type=script&lang=js&\"", "<template>\n\t<view v-if=\"show_canvas\">\n\t\t<canvas class=\"fire-canvas\" :style=\"{ width: width + 'px', height: height + 'px', 'z-index': zIndex }\" :canvas-id=\"canvasId\" @error=\"canvasIdErrorCallback\"></canvas>\n\t</view>\n</template>\n\n<script>\n/*\n    浏览器的最高刷新赔率（最后一个表示1秒刷新60次）\n*/\nconst minBrowserRefreshTime =\n\t(window &&\n\t\t(window.requestAnimationFrame ||\n\t\t\twindow.webkitRequestAnimationFrame ||\n\t\t\twindow.mozRequestAnimationFrame ||\n\t\t\twindow.oRequestAnimationFrame ||\n\t\t\twindow.msRequestAnimationFrame)) ||\n\tfunction (t) {\n\t\tsetTimeout(t, 1e3 / 60);\n\t};\nconst systenInfo = uni.getSystemInfoSync();\nlet fireCanvasBox = null;\nexport default {\n\tprops: {\n\t\t/*\n            礼花数量（最好小于500,太多会卡顿）\n        */\n\t\tparticleCount: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: 100\n\t\t},\n\t\t/* 取值\t0-360\n            喷发角度示意图（简单说就是喷射方向）\n                    礼花（90）\n            礼花（180）\t\t礼花0\n                    礼花（270）\n        */\n\t\tangle: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: 90\n\t\t},\n\t\t/*\n            爆炸范围\n        */\n\t\tspread: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: 100\n\t\t},\n\t\t/*\n            喷发的初始速度\n        */\n\t\tstartVelocity: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: 45\n\t\t},\n\t\t/*\n            喷发的衰退时间，超出canvas会被清除，跟startVelocity配合使用\n        */\n\t\tdecay: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: 0.9\n\t\t},\n\t\t/*\n            刷新几次消失（其实是透明度为0），这个跟间隔的刷新频率有关\n        */\n\t\tticks: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: 200\n\t\t},\n\t\t/*\n            礼花层级\n        */\n\t\tzIndex: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: 1\n\t\t},\n\t\t/*\n            所有要随机的礼花颜色预选值\n        */\n\t\tcolors: {\n\t\t\ttype: Array,\n\t\t\tdefault: () => ['#5BC0EB', '#2176AE', '#FDE74C', '#9BC53D', '#E55934', '#FA7921', '#FF4242']\n\t\t},\n\t\tcanvasId: {\n\t\t\ttype: String,\n\t\t\tdefault: 'fireCanvas'\n\t\t},\n\t\t/*\n            canvas宽度(单位px)\n        */\n\t\twidth: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: () => {\n\t\t\t\treturn systenInfo.windowWidth;\n\t\t\t}\n\t\t},\n\t\t/*\n            canvas高度(单位px)\n        */\n\t\theight: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: () => {\n\t\t\t\treturn systenInfo.windowHeight;\n\t\t\t}\n\t\t},\n\t\t/*\n            中心点-x\n        */\n\t\tx: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: () => {\n\t\t\t\treturn systenInfo.windowWidth / 2;\n\t\t\t}\n\t\t},\n\t\t/*\n            中心点-y\n        */\n\t\ty: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: () => {\n\t\t\t\treturn systenInfo.windowHeight * 0.4;\n\t\t\t}\n\t\t}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tshow_canvas: false,\n\t\t\t/*\n                手机分辨率\n            */\n\t\t\tpixelRatio: systenInfo.pixelRatio\n\t\t};\n\t},\n\tmounted() {\n\t\t// this.initCanvas();\n\t},\n\tmethods: {\n\t\tshow() {\n\t\t\tif (this.show_canvas) return;\n\t\t\tthis.show_canvas = true;\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tthis.initCanvas();\n\t\t\t});\n\t\t},\n\t\t/*\n            转16进制（颜色用）\n        */\n\t\tparseInt16(t) {\n\t\t\treturn parseInt(t, 16);\n\t\t},\n\t\tcanvasIdErrorCallback(e) {\n\t\t\tconsole.error(e.detail.errMsg);\n\t\t},\n\t\tinitCanvas() {\n\t\t\tfireCanvasBox = null;\n\t\t\tfireCanvasBox = uni.createCanvasContext(this.canvasId, this);\n\t\t\tfireCanvasBox.fillRect(0, 0, this.width * this.pixelRatio, this.height * this.pixelRatio);\n\t\t\tfireCanvasBox.scale(this.pixelRatio, this.pixelRatio);\n\t\t\tfireCanvasBox.save();\n\t\t\tthis.fireworksDraw();\n\t\t},\n\t\tfireworksDraw() {\n\t\t\tlet ribbon = [];\n\t\t\tlet // 彩带容器\n\t\t\t\tparticleCount = this.particleCount;\n\t\t\tlet n = null;\n\t\t\tlet r = null;\n\t\t\tlet a = null;\n\t\t\tlet i = null;\n\t\t\tfor (; particleCount--; ) {\n\t\t\t\t(n = {\n\t\t\t\t\tx: this.x,\n\t\t\t\t\ty: this.y,\n\t\t\t\t\tangle: this.angle,\n\t\t\t\t\tspread: this.spread,\n\t\t\t\t\tstartVelocity: this.startVelocity,\n\t\t\t\t\tcolor: this.colors[particleCount % this.colors.length],\n\t\t\t\t\tticks: this.ticks,\n\t\t\t\t\tdecay: this.decay\n\t\t\t\t}),\n\t\t\t\t\t(i = 0),\n\t\t\t\t\t(r = n.angle * (Math.PI / 180)),\n\t\t\t\t\t(a = n.spread * (Math.PI / 180));\n\t\t\t\tribbon.push({\n\t\t\t\t\t// 菜单位置初始化\n\t\t\t\t\tx: n.x,\n\t\t\t\t\ty: n.y,\n\t\t\t\t\tdepth: 0.5 * Math.random() + 0.6,\n\t\t\t\t\twobble: 10 * Math.random(),\n\t\t\t\t\tvelocity: 0.5 * n.startVelocity + Math.random() * n.startVelocity,\n\t\t\t\t\tangle2D: -r + (0.5 * a - Math.random() * a),\n\t\t\t\t\ttiltAngle: Math.random() * Math.PI,\n\t\t\t\t\tcolor:\n\t\t\t\t\t\t((i = (n.color + '').replace(/[^0-9a-f]/gi, '')),\n\t\t\t\t\t\ti.length < 6 && (i = i[0] + i[0] + i[1] + i[1] + i[2] + i[2]),\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t// 生成随机颜色\n\t\t\t\t\t\t\tr: this.parseInt16(i.substring(0, 2)),\n\t\t\t\t\t\t\tg: this.parseInt16(i.substring(2, 4)),\n\t\t\t\t\t\t\tb: this.parseInt16(i.substring(4, 6))\n\t\t\t\t\t\t}),\n\t\t\t\t\ttick: 0,\n\t\t\t\t\ttotalTicks: n.ticks,\n\t\t\t\t\tdecay: n.decay,\n\t\t\t\t\trandom: Math.random() + 5,\n\t\t\t\t\ttiltSin: 0,\n\t\t\t\t\ttiltCos: 0,\n\t\t\t\t\twobbleX: 0,\n\t\t\t\t\twobbleY: 0\n\t\t\t\t});\n\t\t\t}\n\t\t\tlet this_ = this;\n\t\t\tminBrowserRefreshTime(function drawRibbon() {\n\t\t\t\tif (!fireCanvasBox) return;\n\n\t\t\t\ttry {\n\t\t\t\t\tfireCanvasBox.draw(),\n\t\t\t\t\t\tfireCanvasBox.restore(),\n\t\t\t\t\t\t(ribbon = ribbon.filter((e) => {\n\t\t\t\t\t\t\t(e.x += Math.cos(e.angle2D) * e.velocity),\n\t\t\t\t\t\t\t\t(e.y += Math.sin(e.angle2D) * e.velocity + 5 * e.depth),\n\t\t\t\t\t\t\t\t(e.wobble += 0.1),\n\t\t\t\t\t\t\t\t(e.velocity *= e.decay),\n\t\t\t\t\t\t\t\t(e.tiltAngle += 0.02 * Math.random() + 0.12),\n\t\t\t\t\t\t\t\t(e.tiltSin = Math.sin(e.tiltAngle)),\n\t\t\t\t\t\t\t\t(e.tiltCos = Math.cos(e.tiltAngle)),\n\t\t\t\t\t\t\t\t(e.random = Math.random() + 4),\n\t\t\t\t\t\t\t\t(e.wobbleX = e.x + 20 * Math.cos(e.wobble) * e.depth),\n\t\t\t\t\t\t\t\t(e.wobbleY = e.y + 20 * Math.sin(e.wobble) * e.depth);\n\t\t\t\t\t\t\t// 开始画图\n\t\t\t\t\t\t\t(fireCanvasBox.fillStyle = 'rgba(' + e.color.r + ', ' + e.color.g + ', ' + e.color.b + ', ' + (1 - e.tick++ / e.totalTicks) + ')'),\n\t\t\t\t\t\t\t\tfireCanvasBox.beginPath(),\n\t\t\t\t\t\t\t\tfireCanvasBox.moveTo(Math.floor(e.x), Math.floor(e.y)),\n\t\t\t\t\t\t\t\tfireCanvasBox.lineTo(Math.floor(e.wobbleX), Math.floor(e.y + e.random * e.tiltSin)),\n\t\t\t\t\t\t\t\tfireCanvasBox.lineTo(Math.floor(e.wobbleX + e.random * e.tiltCos), Math.floor(e.wobbleY + e.random * e.tiltSin)),\n\t\t\t\t\t\t\t\tfireCanvasBox.lineTo(Math.floor(e.x + e.random * e.tiltCos), Math.floor(e.wobbleY)),\n\t\t\t\t\t\t\t\tfireCanvasBox.closePath(),\n\t\t\t\t\t\t\t\tfireCanvasBox.fill();\n\t\t\t\t\t\t\treturn e.tick < e.totalTicks;\n\t\t\t\t\t\t}));\n\t\t\t\t\tribbon.length ? minBrowserRefreshTime(drawRibbon) : ((fireCanvasBox = null), (this_.show_canvas = false), console.log('结束')); // 轮询调用或者释放掉\n\t\t\t\t} catch (e) {\n\t\t\t\t\t//TODO handle the exception\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t}\n};\n</script>\n\n<style>\n.fire-canvas {\n\tposition: fixed;\n\ttop: 0px;\n\tleft: 0px;\n\tpointer-events: none;\n}\n</style>\n", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-screen-animation-lihua.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-screen-animation-lihua.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755048919888\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}