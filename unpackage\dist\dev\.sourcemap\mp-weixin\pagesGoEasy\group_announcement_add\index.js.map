{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/group_announcement_add/index.vue?e585", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/group_announcement_add/index.vue?55c8", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/group_announcement_add/index.vue?aab3", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/group_announcement_add/index.vue?8dc9", "uni-app:///pagesGoEasy/group_announcement_add/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/group_announcement_add/index.vue?ffb4", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/group_announcement_add/index.vue?311d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "emoji", "data", "b", "isFocus", "isKeyboard", "is<PERSON><PERSON><PERSON>", "text", "keyboardHeight", "computed", "renderTextMessage", "onLoad", "group_id", "group_to", "console", "methods", "keyboardheightchange", "<PERSON><PERSON><PERSON><PERSON>", "on<PERSON><PERSON><PERSON>", "focus", "lineBreak", "deleteFn", "del", "xstr", "sendingText", "uni", "sendingEmojiPack", "groupId", "senderData", "senderId", "messageId", "payload", "timestamp", "type", "recalled", "status", "isHide"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACwL;AACxL,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAA2sB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC0D/tB;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AAEA;AACA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACAC;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;IACAC;IACA;IACAC;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;IAIA;IACAC;MACA;IACA;IACA;IACA;IACAC;MACA;MACA;IAIA;IACA;IACAC;MAAA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QAAA,IAOAC;UACA;QACA;QARA;QACA;QACA;UACAC;QACA;QACA;QAIA;MACA;QACA;MACA;IACA;IAEA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAEA;gBACA;gBACAC;gBACAA;gBAAA;gBAAA,OACA;cAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACAC;QACAC;QACAC;QACAC;QACAC;UACAxB;QACA;QACAyB;QACAC;QACAC;QACAC;QACAC;MACA;MAGAX;MACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvLA;AAAA;AAAA;AAAA;AAAk2C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAt3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesGoEasy/group_announcement_add/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesGoEasy/group_announcement_add/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=1ab28e3b&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=1ab28e3b&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1ab28e3b\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesGoEasy/group_announcement_add/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=1ab28e3b&scoped=true&\"", "var components\ntry {\n  components = {\n    mBottomPaceholder: function () {\n      return import(\n        /* webpackChunkName: \"components/m-bottom-paceholder/m-bottom-paceholder\" */ \"@/components/m-bottom-paceholder/m-bottom-paceholder.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.isFocus = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"flex_c_c page\">\n\t\t<view class=\"flex1 box\">\n\t\t\t<view class=\"text_30 box-title\">预览：</view>\n\t\t\t<view class=\"text_32\" :style=\"{ whiteSpace: 'pre-wrap' }\" v-html=\"renderTextMessage\"></view>\n\t\t</view>\n\t\t<view class=\"bottom-operation-box\">\n\t\t\t<view class=\"flex_r line-break\" v-show=\"keyboardHeight\">\n\t\t\t\t<view class=\"icon_ text_28 color__ line-break-box\" @click=\"lineBreak\">\n\t\t\t\t\t<view class=\"icon_ line-break-icon\">\n\t\t\t\t\t\t<image\n\t\t\t\t\t\t\tclass=\"img\"\n\t\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTAgMGgxMDI0djEwMjRIMHoiIGZpbGw9IiMyYzJjMmMiIGZpbGwtb3BhY2l0eT0iLjAxIi8+PHBhdGggZD0iTTY4Mi42NjcgMTI4YTI5OC42NjcgMjk4LjY2NyAwIDAgMSAxMC4yNCA1OTcuMTYzbC0xMC4yNC4xN2gtNTEyYTQyLjY2NyA0Mi42NjcgMCAwIDEtNC45OTItODUuMDM0bDQuOTkyLS4yOTloNTEyYTIxMy4zMzMgMjEzLjMzMyAwIDAgMCA5LjI1OC00MjYuNDUzbC05LjI1OC0uMjE0aC01MTJhNDIuNjY3IDQyLjY2NyAwIDAgMS00Ljk5Mi04NS4wMzRsNC45OTItLjI5OWg1MTJ6IiBmaWxsPSIjMmMyYzJjIi8+PHBhdGggZD0iTTI0Ny4xNjggNDYwLjUwMWE0Mi42NjcgNDIuNjY3IDAgMCAxIDYzLjg3MiA1Ni4zMmwtMy41NDEgNC4wMTEtMTYwIDE2MCAxNTguMTY1IDE0MC42M2E0Mi42NjcgNDIuNjY3IDAgMCAxIDYuODI3IDU1Ljk3OGwtMy4yODYgNC4yNjdhNDIuNjY3IDQyLjY2NyAwIDAgMS01NS45NzggNi44MjZsLTQuMjY3LTMuMzI4LTE5Mi0xNzAuNjY2YTQyLjY2NyA0Mi42NjcgMCAwIDEtNS4yNDgtNTguMTU1bDMuNDEzLTMuODgzIDE5Mi0xOTJ6IiBmaWxsPSIjMmMyYzJjIi8+PC9zdmc+\"\n\t\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t换行\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"flex_r bottom-operation\">\n\t\t\t\t<view style=\"width: 10rpx\"></view>\n\t\t\t\t<view class=\"flex_c_c flex1\">\n\t\t\t\t\t<view class=\"bottom-operation-input\">\n\t\t\t\t\t\t<textarea\n\t\t\t\t\t\t\tclass=\"input\"\n\t\t\t\t\t\t\tauto-height=\"true\"\n\t\t\t\t\t\t\tconfirm-type=\"done\"\n\t\t\t\t\t\t\ttype=\"text\"\n\t\t\t\t\t\t\t:maxlength=\"-1\"\n\t\t\t\t\t\t\t:focus=\"isFocus\"\n\t\t\t\t\t\t\t:adjust-position=\"false\"\n\t\t\t\t\t\t\tv-model=\"text\"\n\t\t\t\t\t\t\tconfirm-hold\n\t\t\t\t\t\t\t@confirm=\"sendingText\"\n\t\t\t\t\t\t\t@focus=\"focus\"\n\t\t\t\t\t\t\t@blur=\"isFocus = false\"\n\t\t\t\t\t\t\t@keyboardheightchange=\"keyboardheightchange\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view style=\"width: 10rpx\"></view>\n\t\t\t\t<view class=\"icon_ bottom-operation-icon\" @click=\"tapEmoji\">\n\t\t\t\t\t<image class=\"img\" :src=\"b\" mode=\"aspectFill\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view>\n\t\t\t\t<emoji v-model=\"isEmoji\" @onEmoji=\"onEmoji\" @deleteFn=\"deleteFn\" @sendingText=\"sendingText\" @sendingEmojiPack=\"sendingEmojiPack\"></emoji>\n\t\t\t</view>\n\t\t\t<!-- 键盘高度 -->\n\t\t\t<view class=\"keyboard\" :style=\"{ height: keyboardHeight + 'px' }\"></view>\n\t\t\t<view v-if=\"keyboardHeight === 0\">\n\t\t\t\t<m-bottom-paceholder></m-bottom-paceholder>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { show, to, jsonUrl } from '@/utils/index.js';\nimport emoji from './emoji.vue';\n\nimport { EmojiDecoder, emojiMap } from '../lib/EmojiDecoder.js';\nconst emojiUrl = 'https://imgcache.qq.com/open/qcloud/tim/assets/emoji/';\nconst decoder = new EmojiDecoder(emojiUrl, emojiMap);\n\nlet group_id = '';\nlet group_to = {};\nexport default {\n\tcomponents: {\n\t\temoji\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tb: 'data:image/svg+xml;base64,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',\n\t\t\tisFocus: true, //键盘焦点\n\t\t\tisKeyboard: true,\n\t\t\tisEmoji: false,\n\t\t\ttext: '',\n\t\t\tkeyboardHeight: 0\n\t\t};\n\t},\n\tcomputed: {\n\t\t//渲染文本消息，如果包含表情，替换为图片\n\t\t//todo:本不需要该方法，可以在标签里完成，但小程序有兼容性问题，被迫这样实现\n\t\trenderTextMessage() {\n\t\t\tif (!this.text) return '';\n\t\t\treturn '<span>' + decoder.decode(this.text) + '</span>';\n\t\t}\n\t},\n\tonLoad(e) {\n\t\tconst data = jsonUrl(e);\n\t\tgroup_id = data.group_id;\n\t\tgroup_to = data.group_to;\n\t\tthis.text = data.text || '';\n\t\tconsole.log(data);\n\t},\n\tmethods: {\n\t\tkeyboardheightchange(e) {\n\t\t\tthis.keyboardHeight = e.detail.height;\n\t\t\tthis.isEmoji = false;\n\t\t},\n\t\ttapEmoji() {\n\t\t\tthis.isEmoji = !this.isEmoji;\n\t\t\tif (this.isEmoji) {\n\t\t\t\tthis.isKeyboard = true;\n\t\t\t}\n\t\t\t// #ifdef H5\n\t\t\tthis.keyboardHeight = 0;\n\t\t\t// #endif\n\t\t},\n\t\tonEmoji(key) {\n\t\t\tthis.text = `${this.text}${key}`;\n\t\t},\n\t\t// ===========================\n\t\t// 获取焦点\n\t\tfocus(e) {\n\t\t\tthis.isFocus = true;\n\t\t\tthis.isEmoji = false;\n\t\t\t// #ifdef H5\n\t\t\tthis.keyboardHeight = 300;\n\t\t\t// #endif\n\t\t},\n\t\t// 插入换行符合\n\t\tlineBreak() {\n\t\t\tthis.text = `${this.text}\\r\\n`;\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tthis.isFocus = true;\n\t\t\t});\n\t\t},\n\t\t// 删除表情\n\t\tdeleteFn() {\n\t\t\tconst str = this.text.charAt(this.text.length - 1);\n\t\t\tif (str === ']') {\n\t\t\t\tlet metaChars = /\\[.*?(\\u4e00*\\u597d*)\\]/g;\n\t\t\t\tlet xstr = '';\n\t\t\t\tthis.text.replace(metaChars, (match) => {\n\t\t\t\t\txstr = match;\n\t\t\t\t});\n\t\t\t\tvar text = this.text;\n\t\t\t\tfunction del(str) {\n\t\t\t\t\treturn text.slice(0, text.length - str.length);\n\t\t\t\t}\n\t\t\t\tthis.text = del(xstr);\n\t\t\t} else {\n\t\t\t\tthis.text = this.text.substring(0, this.text.length - 1);\n\t\t\t}\n\t\t},\n\n\t\t// =====================\n\t\t// 创建发送输入框内容\n\t\tasync sendingText() {\r\n\t\t\t\r\n\t\t\t\tthis.sendingEmojiPack();\r\n\t\t\t\t// 更新上一个页面\r\n\t\t\t\tuni.$emit('getNotice');\r\n\t\t\t\tuni.$off('getNotice');\r\n\t\t\t\tawait show('提交成功', 1500, 'success');\r\n\t\t\t\tto();\r\n\t\t\t\r\n\t\t},\n\n\t\t// 创建自定义表情包\n\t\tsendingEmojiPack() {\r\n\t\t\tconst message = {\r\n\t\t\t\tgroupId: '22',\r\n\t\t\t\tsenderData: {},\r\n\t\t\t\tsenderId:'8888',\r\n\t\t\t\tmessageId: Date.now(),\r\n\t\t\t\tpayload: {\r\n\t\t\t\t\ttext: this.text\r\n\t\t\t\t},\r\n\t\t\t\ttimestamp: Date.now(),\r\n\t\t\t\ttype: 'group_notice',\r\n\t\t\t\trecalled: false,\r\n\t\t\t\tstatus: 'success',\r\n\t\t\t\tisHide: 0\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tuni.$emit('getNoticeSendMessage', message);\r\n\t\t\tuni.$off('getNoticeSendMessage');\r\n\t\t}\n\t}\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.page {\n\twidth: 100vw;\n\theight: 100vh;\n\t.box {\n\t\tbox-sizing: border-box;\n\t\tpadding-top: 10rpx;\n\t\twidth: calc(100% - 40rpx);\n\t\tmargin: 0 auto;\n\t}\n}\n.bottom-operation-box {\n\tposition: relative;\n\tz-index: 99;\n\twidth: 100vw;\n\tbackground-color: #f6f6f6;\n\t.line-break {\n\t\tposition: absolute;\n\t\tz-index: 99;\n\t\tleft: 0;\n\t\ttop: -58rpx;\n\t\twidth: 100%;\n\t\theight: 60rpx;\n\t\tflex-direction: row-reverse;\n\t\t.line-break-box {\n\t\t\tposition: relative;\n\t\t\twidth: 160rpx;\n\t\t\theight: 100%;\n\t\t\tcolor: #2c2c2c;\n\t\t\tborder-radius: 20rpx 0 0 0;\n\t\t\tbackground-color: #f6f6f6;\n\t\t\t.line-break-icon {\n\t\t\t\twidth: 36rpx;\n\t\t\t\theight: 36rpx;\n\t\t\t\tmargin-right: 10rpx;\n\t\t\t}\n\t\t}\n\t\t.line-break-box::before {\n\t\t\tposition: absolute;\n\t\t\tleft: -60rpx;\n\t\t\ttop: 0;\n\t\t\tcontent: '';\n\t\t\twidth: 60rpx;\n\t\t\theight: 60rpx;\n\t\t\tdisplay: block;\n\t\t\ttext-align: center;\n\t\t\tbackground-image: radial-gradient(240rpx at 2rpx 0px, rgba(168, 195, 59, 0) 60rpx, #f6f6f6 60rpx);\n\t\t}\n\t}\n}\n.bottom-operation {\n\tbox-sizing: border-box;\n\tpadding: 14rpx 10rpx;\n\twidth: 100%;\n\talign-items: flex-end;\n\t.bottom-operation-icon {\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\t.img {\n\t\t\twidth: 80%;\n\t\t\theight: 80%;\n\t\t}\n\t}\n\t.bottom-operation-input {\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t\tpadding: 10rpx 14rpx;\n\t\tmin-height: 84rpx;\n\t\tmax-height: 300rpx;\n\t\toverflow: auto;\n\t\tborder-radius: 10rpx;\n\t\tbackground-color: #fff;\n\t\t.input {\n\t\t\twidth: 100%;\n\t\t\tmargin: 10rpx 0;\n\t\t}\n\t}\n}\n.keyboard {\n\ttransition: all 0.2s;\n}\n\n// 引用\n.quote {\n\tbox-sizing: border-box;\n\tpadding: 0 20rpx;\n\twidth: 100%;\n\theight: 50rpx;\n\tmargin-top: 8rpx;\n\tborder-radius: 10rpx;\n\tbackground-color: #eaeaea;\n\tcolor: #686868;\n\t.quote-row {\n\t\twidth: 200rpx;\n\t\ttext-overflow: ellipsis;\n\t\toverflow: auto;\n\t\twhite-space: nowrap;\n\t\t::v-deep .quote-box {\n\t\t\twidth: 100%;\n\t\t\tbox-sizing: border-box;\n\t\t\tpadding: 0;\n\t\t\tborder-radius: 0;\n\t\t\tmargin-top: 0;\n\t\t\tbackground-color: #eaeaea;\n\t\t\tcolor: #6b6b6b;\n\t\t\t.quote-name {\n\t\t\t}\n\n\t\t\t.m-image {\n\t\t\t\tborder-radius: 6rpx;\n\t\t\t\toverflow: hidden;\n\t\t\t\t.img {\n\t\t\t\t\twidth: 40rpx;\n\t\t\t\t\theight: 40rpx;\n\t\t\t\t\tborder-radius: 6rpx;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\tbackground-color: #fff;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.quote-icon {\n\t\twidth: 40rpx;\n\t\theight: 40rpx;\n\t}\n}\n</style>\n", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=1ab28e3b&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=1ab28e3b&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755048920065\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}