<view class="quote-box data-v-0d2479c2"><view class="flex_r quote-name data-v-0d2479c2"><view class="data-v-0d2479c2">{{value.senderData.name+"："}}</view><view data-event-opts="{{[['tap',[['openimg',['$0'],['value.payload.url']]]]]}}" class="flex_r m-image data-v-0d2479c2" catchtap="__e"><image class="img data-v-0d2479c2" src="{{value.payload.thumbnail||value.payload.url}}" mode="aspectFill"></image></view></view></view>