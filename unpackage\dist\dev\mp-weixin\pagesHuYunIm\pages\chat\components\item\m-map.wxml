<view class="flex_c row data-v-c787624e"><view data-event-opts="{{[['tap',[['onClick',['$event']]]]]}}" class="{{['flex_r','text-box','data-v-c787624e',(isMy)?'text_box':'']}}" catchtap="__e"><view class="{{['text','data-v-c787624e',isMy?'text_r':'text_l']}}"><view class="flex_c_c nowrap_ map data-v-c787624e"><view class="nowrap_ text_32 map-title data-v-c787624e">{{''+value.payload.title+''}}</view><view class="nowrap_ text_22 map-text data-v-c787624e">{{''+value.payload.address+''}}</view><view class="flex1 map-img data-v-c787624e"><view class="str data-v-c787624e"></view><image class="z_index2 img data-v-c787624e" src="{{value.payload.image}}" mode="aspectFill"></image></view></view></view></view></view>