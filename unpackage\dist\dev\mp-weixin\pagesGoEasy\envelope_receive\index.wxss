@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.navigationBar-box.data-v-382407c6 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;
}
.navigationBar-box .navigationBar.data-v-382407c6 {
  width: 100%;
}
.navigationBar-box .navigationBar .navigationBar-icon.data-v-382407c6 {
  width: 44rpx;
  height: 44rpx;
  margin: 0 30rpx;
}
.navigationBar-box .navigationBar .navigationBar-icon .img.data-v-382407c6 {
  width: 90%;
  height: 90%;
}
.top.data-v-382407c6 {
  position: fixed;
  z-index: 3;
  top: 0;
  left: 0;
  width: 100%;
  height: 420rpx;
  background-color: #fff;
}
.top .top-str.data-v-382407c6 {
  box-sizing: border-box;
  position: absolute;
  left: calc(50% - 600rpx);
  bottom: 30rpx;
  width: 1200rpx;
  height: 800rpx;
  border-radius: 50%;
  background-color: #f25745;
  border: 6rpx solid #f8ca75;
  overflow: hidden;
}
.top .top-str .top-str-bc.data-v-382407c6 {
  position: absolute;
  left: calc(50% - 50vw);
  bottom: 0;
  z-index: 2;
  width: 100vw;
  height: 420rpx;
}
.info.data-v-382407c6 {
  box-sizing: border-box;
  padding: 0 40rpx;
  width: 100%;
  min-height: 60rpx;
}
.info .info-img.data-v-382407c6 {
  width: 50rpx;
  height: 50rpx;
  border-radius: 10rpx;
  overflow: hidden;
  margin-right: 10rpx;
  background-color: #f4f4f4;
}
.title.data-v-382407c6 {
  box-sizing: border-box;
  padding: 0 40rpx;
  width: 100%;
  min-height: 60rpx;
}
.money.data-v-382407c6 {
  width: 100%;
  height: 100rpx;
  margin-top: 40rpx;
  color: #c2a26f;
  align-items: flex-end;
  justify-content: center;
}
.money .money-value.data-v-382407c6 {
  position: relative;
}
.money .money-value .money-value-.data-v-382407c6 {
  font-size: 90rpx;
}
.money .money-value .money-text.data-v-382407c6 {
  position: absolute;
  right: -70rpx;
  bottom: 20rpx;
}
.money-illustrate.data-v-382407c6 {
  width: 100%;
  color: #c2a26f;
  height: 40rpx;
  margin-top: 10rpx;
}
.money-illustrate .money-illustrate-icon.data-v-382407c6 {
  width: 30rpx;
  height: 30rpx;
}
.interval.data-v-382407c6 {
  width: 100%;
  height: 16rpx;
  margin-top: 60rpx;
  background-color: #e5e5e5;
}
.list.data-v-382407c6 {
  box-sizing: border-box;
  padding: 0 30rpx;
  width: 100%;
  margin-top: 40rpx;
}
.list .list-title.data-v-382407c6 {
  height: 80rpx;
  line-height: 80rpx;
  color: #b2b2b2;
}
.list .item.data-v-382407c6 {
  width: 100%;
  height: 100rpx;
  margin-bottom: 20rpx;
}
.list .item .item-img.data-v-382407c6 {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  border-radius: 10rpx;
  overflow: hidden;
  background-color: #f1f1f1;
}
.list .item .item-r .item-name.data-v-382407c6 {
  height: 50rpx;
}
.list .item .item-r .item-time.data-v-382407c6 {
  height: 34rpx;
  color: #b2b2b2;
}
.list .item .item-r .item-time .label.data-v-382407c6 {
  color: #edb746;
}
.list .item .item-r .item-time .label .label-icon.data-v-382407c6 {
  width: 34rpx;
  height: 34rpx;
  margin-right: 10rpx;
}
.list .item .item-r .item-r-m-line.data-v-382407c6 {
  width: 100%;
  margin-top: 20rpx;
  margin-bottom: 10rpx;
}
