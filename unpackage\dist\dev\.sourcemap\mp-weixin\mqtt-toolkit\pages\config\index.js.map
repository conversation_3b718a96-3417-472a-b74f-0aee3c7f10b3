{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/mqtt-toolkit/pages/config/index.vue?1e6d", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/mqtt-toolkit/pages/config/index.vue?e151", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/mqtt-toolkit/pages/config/index.vue?2596", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/mqtt-toolkit/pages/config/index.vue?2f8b", "uni-app:///mqtt-toolkit/pages/config/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/mqtt-toolkit/pages/config/index.vue?1a4d", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/mqtt-toolkit/pages/config/index.vue?eb61"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "environments", "currentEnvIndex", "userConfig", "userId", "nickname", "channelCode", "token", "avatar", "connectionOptions", "keepalive", "connectTimeout", "reconnectPeriod", "clean", "autoReconnect", "topicTemplates", "configInfo", "computed", "currentConfig", "wsUrl", "pingInterval", "onLoad", "mqttClient", "methods", "loadConfig", "console", "loadTopicTemplates", "USER_CHANNEL", "PING", "CHAT_ROOM", "PRIVATE_CHAT", "GROUP_CHAT", "SYSTEM_NOTIFY", "ONLINE_STATUS", "onEnvChange", "onCleanChange", "onAutoReconnectChange", "getTemplateName", "testTemplate", "uni", "title", "content", "showCancel", "saveConfig", "icon", "resetConfig", "success", "testConnection", "env", "testClient", "userInfo", "onConnect", "setTimeout", "onError", "updateConfigInfo", "trim"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AAC2L;AAC3L,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxBA;AAAA;AAAA;AAAA;AAA0tB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACiI9uB;AACA;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MAEAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEAC;MACAC;IACA;EACA;EAEAC;IACAC;MACA;MACA;QACAC;QACAC;QACAT;QACAC;MACA;IACA;EACA;EAEAS;IACA;IACA;IACA;;IAEA;IACAC;EACA;EAEAC;IACA;AACA;AACA;IACAC;MACA;QACA;QACA;UACA;UACA;UACA;QACA;MACA;QACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;QACAV;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;IACA;IAEA;AACA;AACA;IACAK;MACA;MACAC;QACAC;QACAC;QACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;QACA;UACAxC;UACAM;UACAP;QACA;QAEAqC;QAEAA;UACAC;UACAI;QACA;QAEA;MACA;QACAnB;QACAc;UACAC;UACAI;QACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MACAN;QACAC;QACAC;QACAK;UACA;YACA;cACA1C;cACAC;cACAC;cACAC;cACAC;YACA;YAEA;cACAE;cACAC;cACAC;cACAC;cACAC;YACA;YAEA;YAEA;cACAyB;cACAA;gBACAC;gBACAI;cACA;cACA;YACA;cACAnB;YACA;UACA;QACA;MACA;IACA;IAEA;AACA;AACA;IACAsB;MACA;QACAR;UACAC;UACAI;QACA;QACA;MACA;MAEAL;QACAC;MACA;MAEA;QACA;QACA,+CACA,wBACA,0BACA,6BACA,uBACA,wBACAQ,IACA;QAEA;QAEAC,mBACAC,UACA;UACAC;YACAZ;YACAA;cACAC;cACAI;YACA;;YAEA;YACAQ;cACAH;YACA;UACA;UAEAI;YACAd;YACAA;cACAC;cACAC;cACAC;YACA;UACA;QACA,GACA,uBACA;MACA;QACAH;QACAA;UACAC;UACAC;UACAC;QACA;MACA;IACA;IAEA;AACA;AACA;IACAY;MACA;MACA,iDACAN,iCACA,uDACA,+DACA,oEACA,2EACA,gFACA,iFACA,iFACA,8DACAO;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnaA;AAAA;AAAA;AAAA;AAAqjC,CAAgB,g9BAAG,EAAC,C;;;;;;;;;;;ACAzkC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "mqtt-toolkit/pages/config/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './mqtt-toolkit/pages/config/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4a93a468&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=4a93a468&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4a93a468\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"mqtt-toolkit/pages/config/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4a93a468&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.topicTemplates, function (template, key) {\n    var $orig = _vm.__get_orig(template)\n    var m0 = _vm.getTemplateName(key)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"config-page\">\n    <!-- 当前配置显示 -->\n    <view class=\"config-section\">\n      <view class=\"section-title\">当前MQTT配置</view>\n\n      <view class=\"config-item\">\n        <text class=\"config-label\">环境:</text>\n        <picker :value=\"currentEnvIndex\" :range=\"environments\" @change=\"onEnvChange\">\n          <view class=\"picker-value\">{{ environments[currentEnvIndex] }}</view>\n        </picker>\n      </view>\n\n      <view class=\"config-item\">\n        <text class=\"config-label\">WebSocket URL:</text>\n        <text class=\"config-value\">{{ currentConfig.wsUrl }}</text>\n      </view>\n\n      <view class=\"config-item\">\n        <text class=\"config-label\">心跳间隔:</text>\n        <text class=\"config-value\">{{ currentConfig.pingInterval }}ms</text>\n      </view>\n\n      <view class=\"config-item\">\n        <text class=\"config-label\">连接超时:</text>\n        <text class=\"config-value\">{{ currentConfig.connectTimeout }}ms</text>\n      </view>\n\n      <view class=\"config-item\">\n        <text class=\"config-label\">重连间隔:</text>\n        <text class=\"config-value\">{{ currentConfig.reconnectPeriod }}ms</text>\n      </view>\n    </view>\n\n    <!-- 用户信息配置 -->\n    <view class=\"config-section\">\n      <view class=\"section-title\">用户信息配置</view>\n\n      <view class=\"form-item\">\n        <text class=\"form-label\">用户ID:</text>\n        <input v-model=\"userConfig.userId\" class=\"form-input\" placeholder=\"请输入用户ID\" />\n      </view>\n\n      <view class=\"form-item\">\n        <text class=\"form-label\">昵称:</text>\n        <input v-model=\"userConfig.nickname\" class=\"form-input\" placeholder=\"请输入昵称\" />\n      </view>\n\n      <view class=\"form-item\">\n        <text class=\"form-label\">频道代码:</text>\n        <input v-model=\"userConfig.channelCode\" class=\"form-input\" placeholder=\"请输入频道代码\" />\n      </view>\n\n      <view class=\"form-item\">\n        <text class=\"form-label\">认证Token:</text>\n        <textarea v-model=\"userConfig.token\" class=\"form-textarea\" placeholder=\"请输入认证Token\" />\n      </view>\n\n      <view class=\"form-item\">\n        <text class=\"form-label\">头像URL:</text>\n        <input v-model=\"userConfig.avatar\" class=\"form-input\" placeholder=\"请输入头像URL（可选）\" />\n      </view>\n    </view>\n\n    <!-- 连接选项配置 -->\n    <view class=\"config-section\">\n      <view class=\"section-title\">连接选项配置</view>\n\n      <view class=\"form-item\">\n        <text class=\"form-label\">心跳间隔(ms):</text>\n        <input v-model.number=\"connectionOptions.keepalive\" class=\"form-input\" type=\"number\" placeholder=\"60000\" />\n      </view>\n\n      <view class=\"form-item\">\n        <text class=\"form-label\">连接超时(ms):</text>\n        <input v-model.number=\"connectionOptions.connectTimeout\" class=\"form-input\" type=\"number\" placeholder=\"30000\" />\n      </view>\n\n      <view class=\"form-item\">\n        <text class=\"form-label\">重连间隔(ms):</text>\n        <input v-model.number=\"connectionOptions.reconnectPeriod\" class=\"form-input\" type=\"number\" placeholder=\"1000\" />\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"switch-item\">\n          <text class=\"form-label\">清理会话:</text>\n          <switch :checked=\"connectionOptions.clean\" @change=\"onCleanChange\" />\n        </view>\n      </view>\n\n      <view class=\"form-item\">\n        <view class=\"switch-item\">\n          <text class=\"form-label\">自动重连:</text>\n          <switch :checked=\"connectionOptions.autoReconnect\" @change=\"onAutoReconnectChange\" />\n        </view>\n      </view>\n    </view>\n\n    <!-- 主题模板 -->\n    <view class=\"config-section\">\n      <view class=\"section-title\">主题模板</view>\n\n      <view class=\"template-item\" v-for=\"(template, key) in topicTemplates\" :key=\"key\">\n        <view class=\"template-header\">\n          <text class=\"template-name\">{{ getTemplateName(key) }}</text>\n          <button class=\"test-btn\" @click=\"testTemplate(key)\">测试</button>\n        </view>\n        <text class=\"template-value\">{{ template }}</text>\n      </view>\n    </view>\n\n    <!-- 操作按钮 -->\n    <view class=\"action-section\">\n      <button class=\"action-btn save-btn\" @click=\"saveConfig\">保存配置</button>\n      <button class=\"action-btn reset-btn\" @click=\"resetConfig\">重置配置</button>\n      <button class=\"action-btn test-btn\" @click=\"testConnection\">测试连接</button>\n    </view>\n\n    <!-- 配置信息展示 -->\n    <view class=\"info-section\">\n      <view class=\"section-title\">配置信息</view>\n      <view class=\"info-content\">\n        <text class=\"info-text\">{{ configInfo }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { MQTT_CONFIG, TOPIC_TEMPLATES, getWsUrl, createUserInfo } from '../../utils/mqttConfig.js'\nimport mqttClient from '../../utils/mqttClient.js'\nimport mqtt from '../../lib/mqtt.min.js'\n\nexport default {\n  data() {\n    return {\n      environments: ['DEV', 'TEST', 'PROD'],\n      currentEnvIndex: 0,\n\n      userConfig: {\n        userId: 'config-user-' + Date.now(),\n        nickname: '配置测试用户',\n        channelCode: 'config-channel',\n        token: 'config-test-token',\n        avatar: ''\n      },\n\n      connectionOptions: {\n        keepalive: 60000,\n        connectTimeout: 30000,\n        reconnectPeriod: 1000,\n        clean: true,\n        autoReconnect: true\n      },\n\n      topicTemplates: {},\n      configInfo: ''\n    }\n  },\n\n  computed: {\n    currentConfig() {\n      const env = this.environments[this.currentEnvIndex]\n      return {\n        wsUrl: getWsUrl(env),\n        pingInterval: MQTT_CONFIG.PING.INTERVAL,\n        connectTimeout: MQTT_CONFIG.DEFAULT_OPTIONS.connectTimeout,\n        reconnectPeriod: MQTT_CONFIG.DEFAULT_OPTIONS.reconnectPeriod\n      }\n    }\n  },\n\n  onLoad() {\n    this.loadConfig()\n    this.loadTopicTemplates()\n    this.updateConfigInfo()\n\n    // 设置MQTT库\n    mqttClient.setMqttLib(mqtt)\n  },\n\n  methods: {\n    /**\n     * 加载配置\n     */\n    loadConfig() {\n      try {\n        const savedConfig = uni.getStorageSync('mqtt-toolkit-config')\n        if (savedConfig) {\n          this.userConfig = { ...this.userConfig, ...savedConfig.userConfig }\n          this.connectionOptions = { ...this.connectionOptions, ...savedConfig.connectionOptions }\n          this.currentEnvIndex = savedConfig.currentEnvIndex || 0\n        }\n      } catch (error) {\n        console.error('加载配置失败:', error)\n      }\n    },\n\n    /**\n     * 加载主题模板\n     */\n    loadTopicTemplates() {\n      this.topicTemplates = {\n        USER_CHANNEL: TOPIC_TEMPLATES.USER_CHANNEL('用户ID'),\n        PING: TOPIC_TEMPLATES.PING('用户ID'),\n        CHAT_ROOM: TOPIC_TEMPLATES.CHAT_ROOM('房间ID'),\n        PRIVATE_CHAT: TOPIC_TEMPLATES.PRIVATE_CHAT('发送者ID', '接收者ID'),\n        GROUP_CHAT: TOPIC_TEMPLATES.GROUP_CHAT('群组ID'),\n        SYSTEM_NOTIFY: TOPIC_TEMPLATES.SYSTEM_NOTIFY('用户ID'),\n        ONLINE_STATUS: TOPIC_TEMPLATES.ONLINE_STATUS('用户ID')\n      }\n    },\n\n    /**\n     * 环境变化\n     */\n    onEnvChange(e) {\n      this.currentEnvIndex = e.detail.value\n      this.updateConfigInfo()\n    },\n\n    /**\n     * 清理会话开关变化\n     */\n    onCleanChange(e) {\n      this.connectionOptions.clean = e.detail.value\n    },\n\n    /**\n     * 自动重连开关变化\n     */\n    onAutoReconnectChange(e) {\n      this.connectionOptions.autoReconnect = e.detail.value\n    },\n\n    /**\n     * 获取模板名称\n     */\n    getTemplateName(key) {\n      const names = {\n        USER_CHANNEL: '用户频道',\n        PING: '心跳主题',\n        CHAT_ROOM: '聊天室',\n        PRIVATE_CHAT: '私聊',\n        GROUP_CHAT: '群聊',\n        SYSTEM_NOTIFY: '系统通知',\n        ONLINE_STATUS: '在线状态'\n      }\n      return names[key] || key\n    },\n\n    /**\n     * 测试主题模板\n     */\n    testTemplate(key) {\n      const template = this.topicTemplates[key]\n      uni.showModal({\n        title: '主题模板',\n        content: `${this.getTemplateName(key)}: ${template}`,\n        showCancel: false\n      })\n    },\n\n    /**\n     * 保存配置\n     */\n    saveConfig() {\n      try {\n        const config = {\n          userConfig: this.userConfig,\n          connectionOptions: this.connectionOptions,\n          currentEnvIndex: this.currentEnvIndex\n        }\n\n        uni.setStorageSync('mqtt-toolkit-config', config)\n\n        uni.showToast({\n          title: '配置保存成功',\n          icon: 'success'\n        })\n\n        this.updateConfigInfo()\n      } catch (error) {\n        console.error('保存配置失败:', error)\n        uni.showToast({\n          title: '保存失败',\n          icon: 'none'\n        })\n      }\n    },\n\n    /**\n     * 重置配置\n     */\n    resetConfig() {\n      uni.showModal({\n        title: '确认重置',\n        content: '确定要重置所有配置吗？',\n        success: (res) => {\n          if (res.confirm) {\n            this.userConfig = {\n              userId: 'config-user-' + Date.now(),\n              nickname: '配置测试用户',\n              channelCode: 'config-channel',\n              token: 'config-test-token',\n              avatar: ''\n            }\n\n            this.connectionOptions = {\n              keepalive: 60000,\n              connectTimeout: 30000,\n              reconnectPeriod: 1000,\n              clean: true,\n              autoReconnect: true\n            }\n\n            this.currentEnvIndex = 0\n\n            try {\n              uni.removeStorageSync('mqtt-toolkit-config')\n              uni.showToast({\n                title: '配置已重置',\n                icon: 'success'\n              })\n              this.updateConfigInfo()\n            } catch (error) {\n              console.error('重置配置失败:', error)\n            }\n          }\n        }\n      })\n    },\n\n    /**\n     * 测试连接\n     */\n    testConnection() {\n      if (!this.userConfig.userId || !this.userConfig.channelCode || !this.userConfig.token) {\n        uni.showToast({\n          title: '请填写完整的用户信息',\n          icon: 'none'\n        })\n        return\n      }\n\n      uni.showLoading({\n        title: '测试连接中...'\n      })\n\n      try {\n        const env = this.environments[this.currentEnvIndex]\n        const userInfo = createUserInfo(\n          this.userConfig.userId,\n          this.userConfig.nickname,\n          this.userConfig.channelCode,\n          this.userConfig.token,\n          this.userConfig.avatar,\n          env\n        )\n\n        const testClient = mqttClient\n\n        testClient.connect(\n          userInfo,\n          {\n            onConnect: () => {\n              uni.hideLoading()\n              uni.showToast({\n                title: '连接测试成功',\n                icon: 'success'\n              })\n\n              // 断开测试连接\n              setTimeout(() => {\n                testClient.disconnect()\n              }, 1000)\n            },\n\n            onError: (error) => {\n              uni.hideLoading()\n              uni.showModal({\n                title: '连接测试失败',\n                content: error.message,\n                showCancel: false\n              })\n            }\n          },\n          this.connectionOptions\n        )\n      } catch (error) {\n        uni.hideLoading()\n        uni.showModal({\n          title: '连接测试失败',\n          content: error.message,\n          showCancel: false\n        })\n      }\n    },\n\n    /**\n     * 更新配置信息\n     */\n    updateConfigInfo() {\n      const env = this.environments[this.currentEnvIndex]\n      const info = `\n当前环境: ${env}\nWebSocket URL: ${this.currentConfig.wsUrl}\n用户ID: ${this.userConfig.userId}\n频道代码: ${this.userConfig.channelCode}\n心跳间隔: ${this.connectionOptions.keepalive}ms\n连接超时: ${this.connectionOptions.connectTimeout}ms\n重连间隔: ${this.connectionOptions.reconnectPeriod}ms\n清理会话: ${this.connectionOptions.clean ? '是' : '否'}\n自动重连: ${this.connectionOptions.autoReconnect ? '是' : '否'}\n      `.trim()\n\n      this.configInfo = info\n    }\n  }\n}\n</script>\n\n<style scoped>\n.config-page {\n  padding: 20rpx;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.config-section,\n.info-section {\n  background: white;\n  margin-bottom: 20rpx;\n  border-radius: 10rpx;\n  padding: 20rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n  border-bottom: 2rpx solid #eee;\n  padding-bottom: 10rpx;\n}\n\n.config-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.config-label {\n  font-size: 28rpx;\n  color: #666;\n  width: 200rpx;\n}\n\n.config-value {\n  font-size: 26rpx;\n  color: #333;\n  flex: 1;\n  text-align: right;\n}\n\n.picker-value {\n  font-size: 26rpx;\n  color: #007aff;\n  padding: 10rpx;\n  border: 1rpx solid #ddd;\n  border-radius: 6rpx;\n}\n\n.form-item {\n  margin-bottom: 20rpx;\n}\n\n.form-label {\n  font-size: 28rpx;\n  color: #333;\n  margin-bottom: 10rpx;\n  display: block;\n}\n\n.form-input {\n  width: 100%;\n  padding: 20rpx;\n  border: 1rpx solid #ddd;\n  border-radius: 8rpx;\n  font-size: 26rpx;\n  box-sizing: border-box;\n}\n\n.form-textarea {\n  width: 100%;\n  height: 120rpx;\n  padding: 20rpx;\n  border: 1rpx solid #ddd;\n  border-radius: 8rpx;\n  font-size: 26rpx;\n  box-sizing: border-box;\n  resize: none;\n}\n\n.switch-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.template-item {\n  margin-bottom: 20rpx;\n  padding: 15rpx;\n  background-color: #f9f9f9;\n  border-radius: 8rpx;\n}\n\n.template-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10rpx;\n}\n\n.template-name {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.template-value {\n  font-size: 24rpx;\n  color: #666;\n  font-family: monospace;\n  word-break: break-all;\n}\n\n.test-btn {\n  padding: 8rpx 16rpx;\n  background-color: #2196f3;\n  color: white;\n  border: none;\n  border-radius: 6rpx;\n  font-size: 24rpx;\n}\n\n.action-section {\n  display: flex;\n  gap: 20rpx;\n  margin-bottom: 20rpx;\n}\n\n.action-btn {\n  flex: 1;\n  padding: 25rpx;\n  border: none;\n  border-radius: 10rpx;\n  font-size: 28rpx;\n  color: white;\n}\n\n.save-btn {\n  background-color: #4caf50;\n}\n\n.reset-btn {\n  background-color: #f44336;\n}\n\n.test-btn {\n  background-color: #2196f3;\n}\n\n.info-content {\n  background-color: #f9f9f9;\n  padding: 20rpx;\n  border-radius: 8rpx;\n}\n\n.info-text {\n  font-size: 24rpx;\n  color: #666;\n  line-height: 1.6;\n  white-space: pre-line;\n  font-family: monospace;\n}\n</style>\n", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4a93a468&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4a93a468&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754969675949\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}