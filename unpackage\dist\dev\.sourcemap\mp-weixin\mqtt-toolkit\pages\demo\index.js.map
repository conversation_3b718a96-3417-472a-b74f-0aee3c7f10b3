{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/mqtt-toolkit/pages/demo/index.vue?2444", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/mqtt-toolkit/pages/demo/index.vue?b77a", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/mqtt-toolkit/pages/demo/index.vue?23fb", "uni-app:///mqtt-toolkit/pages/demo/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/mqtt-toolkit/pages/demo/index.vue?6502", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/mqtt-toolkit/pages/demo/index.vue?a4cb"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isConnected", "messageInput", "topicInput", "messages", "subscribedTopics", "onLoad", "mqttClient", "onUnload", "methods", "connectMqtt", "onConnect", "console", "onMessage", "onReconnect", "onError", "onEnd", "uni", "title", "icon", "disconnectMqtt", "sendMessage", "subscribeTopic", "unsubscribeTopic", "autoSubscribeTopics", "TOPIC_TEMPLATES", "demoTopics", "handleMessage", "content", "addMessage", "topic", "type", "timestamp", "clearMessages", "loadSubscribedTopics", "formatTime"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AAC2L;AAC3L,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9BA;AAAA;AAAA;AAAA;AAA0tB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC+G9uB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAHA;AAAA,eAKA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACAC;;IAEA;IACA;;IAEA;IACA;MACA;IACA;EACA;EAEAC;IACA;IACAD;EACA;EAEAE;IACA;AACA;AACA;IACAC;MAAA;MACA;MACA,+CACA,2BACA,YACA,qBACA,yDACA,IACA,MACA;MAEA;QACAC;UACAC;UACA;UACA;;UAEA;UACA;QACA;QAEAC;UACAD;UACA;QACA;QAEAE;UACAF;UACA;QACA;QAEAG;UACAH;UACA;UACA;QACA;QAEAI;UACAJ;UACA;UACA;QACA;MACA;;MAEA;MACA;QACAL;MACA;QACAK;QACAK;UACAC;UACAC;QACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MACAb;QACAK;QACA;QACA;QACA;MACA;IACA;IAEA;AACA;AACA;IACAS;MACA;QACAJ;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA,iDACA,mBACA,aACA,YACA,YACA;;MAEA;MACA;MACA;MAEA;QACA;QACA;QAEAF;UACAC;UACAC;QACA;MACA;QACAF;UACAC;UACAC;QACA;MACA;IACA;IAEA;AACA;AACA;IACAG;MAAA;MACA;QACAL;UACAC;UACAC;QACA;QACA;MACA;MAEA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACA;UACA;UACA;UACA;UAEAF;YACAC;YACAC;UACA;QACA;UACAP;UACAK;YACAC;YACAC;UACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;MACA;IACA;IAEA;AACA;AACA;IACAI;MAAA;MACA;QACA;UACA;UACA;YACA;UACA;UACA;UAEAN;YACAC;YACAC;UACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;MACA;IACA;IAEA;AACA;AACA;IACAK;MAAA;MACA,kBACAC,+CACA,sBACA,cACA;MAEAC;QACAnB;UACA;YACA;YACA;UACA;QACA;MACA;IACA;IAEA;AACA;AACA;IACAoB;MACA;MAEA;QACA;QACAC;MACA;QACAA;MACA;MAEA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MACA;QACAC;QACAF;QACAG;QACAC;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;MAAA,CACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACAhB;QACAC;QACAC;MACA;IACA;IAEA;AACA;AACA;IACAe;MACA;MACA;IAAA,CACA;IAEA;AACA;AACA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjaA;AAAA;AAAA;AAAA;AAAqjC,CAAgB,g9BAAG,EAAC,C;;;;;;;;;;;ACAzkC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "mqtt-toolkit/pages/demo/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './mqtt-toolkit/pages/demo/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=1d0b7a26&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=1d0b7a26&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1d0b7a26\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"mqtt-toolkit/pages/demo/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=1d0b7a26&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.messages.length\n  var g1 = !_vm.isConnected || !_vm.messageInput.trim()\n  var g2 = !_vm.isConnected || !_vm.topicInput.trim()\n  var l0 = _vm.__map(_vm.messages, function (msg, index) {\n    var $orig = _vm.__get_orig(msg)\n    var m0 = _vm.formatTime(msg.timestamp)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"mqtt-demo\">\n    <!-- 状态显示 -->\n    <view class=\"status-section\">\n      <view class=\"status-card\">\n        <text class=\"status-label\">连接状态:</text>\n        <text :class=\"['status-value', isConnected ? 'connected' : 'disconnected']\">\n          {{ isConnected ? '已连接' : '未连接' }}\n        </text>\n      </view>\n      <view class=\"status-card\">\n        <text class=\"status-label\">消息数量:</text>\n        <text class=\"status-value\">{{ messages.length }}</text>\n      </view>\n    </view>\n\n    <!-- 控制按钮 -->\n    <view class=\"control-section\">\n      <button \n        class=\"control-btn connect-btn\" \n        @click=\"connectMqtt\" \n        :disabled=\"isConnected\"\n      >\n        连接MQTT\n      </button>\n      <button \n        class=\"control-btn disconnect-btn\" \n        @click=\"disconnectMqtt\" \n        :disabled=\"!isConnected\"\n      >\n        断开连接\n      </button>\n    </view>\n\n    <!-- 消息发送 -->\n    <view class=\"message-section\">\n      <view class=\"input-group\">\n        <input \n          v-model=\"messageInput\" \n          class=\"message-input\"\n          placeholder=\"输入消息内容\"\n          @confirm=\"sendMessage\"\n        />\n        <button \n          class=\"send-btn\" \n          @click=\"sendMessage\" \n          :disabled=\"!isConnected || !messageInput.trim()\"\n        >\n          发送\n        </button>\n      </view>\n    </view>\n\n    <!-- 主题订阅 -->\n    <view class=\"topic-section\">\n      <view class=\"section-title\">主题订阅</view>\n      <view class=\"input-group\">\n        <input \n          v-model=\"topicInput\" \n          class=\"topic-input\"\n          placeholder=\"输入要订阅的主题\"\n        />\n        <button \n          class=\"subscribe-btn\" \n          @click=\"subscribeTopic\" \n          :disabled=\"!isConnected || !topicInput.trim()\"\n        >\n          订阅\n        </button>\n      </view>\n      <view class=\"subscribed-topics\">\n        <view class=\"topic-title\">已订阅主题:</view>\n        <view \n          v-for=\"(topic, index) in subscribedTopics\" \n          :key=\"index\"\n          class=\"topic-item\"\n        >\n          <text class=\"topic-name\">{{ topic }}</text>\n          <button \n            class=\"unsubscribe-btn\" \n            @click=\"unsubscribeTopic(topic)\"\n          >\n            取消\n          </button>\n        </view>\n      </view>\n    </view>\n\n    <!-- 消息列表 -->\n    <view class=\"messages-section\">\n      <view class=\"section-title\">消息列表</view>\n      <scroll-view class=\"messages-container\" scroll-y>\n        <view \n          v-for=\"(msg, index) in messages\" \n          :key=\"index\" \n          :class=\"['message-item', msg.type]\"\n        >\n          <view class=\"message-header\">\n            <text class=\"message-time\">{{ formatTime(msg.timestamp) }}</text>\n            <text class=\"message-topic\">{{ msg.topic }}</text>\n          </view>\n          <view class=\"message-content\">{{ msg.content }}</view>\n        </view>\n      </scroll-view>\n      <button class=\"clear-btn\" @click=\"clearMessages\">清空消息</button>\n    </view>\n  </view>\n</template>\n\n<script>\n// 导入MQTT工具\nimport mqttClient from '../../utils/mqttClient.js'\nimport { createUserInfo, createChatMessage, MESSAGE_TYPES, TOPIC_TEMPLATES } from '../../utils/mqttConfig.js'\nimport mqtt from '../../lib/mqtt.min.js'\n\nexport default {\n  data() {\n    return {\n      isConnected: false,\n      messageInput: '',\n      topicInput: '',\n      messages: [],\n      subscribedTopics: []\n    }\n  },\n  \n  onLoad() {\n    // 设置MQTT库\n    mqttClient.setMqttLib(mqtt)\n    \n    // 检查连接状态\n    this.isConnected = mqttClient.getConnectStatus()\n    \n    // 如果已连接，获取已订阅的主题\n    if (this.isConnected) {\n      this.loadSubscribedTopics()\n    }\n  },\n  \n  onUnload() {\n    // 页面卸载时断开连接\n    mqttClient.disconnect()\n  },\n  \n  methods: {\n    /**\n     * 连接MQTT\n     */\n    connectMqtt() {\n      // 创建用户信息\n      const userInfo = createUserInfo(\n        'demo-user-' + Date.now(),\n        'MQTT演示用户',\n        'mqtt-toolkit-demo',\n        'demo-token-' + Math.random().toString(36).substr(2, 9),\n        '',\n        'DEV'\n      )\n      \n      const callbacks = {\n        onConnect: () => {\n          console.log('MQTT连接成功')\n          this.isConnected = true\n          this.addMessage('系统', '连接成功', 'system')\n          \n          // 自动订阅演示主题\n          this.autoSubscribeTopics()\n        },\n        \n        onMessage: (topic, mqttMsg) => {\n          console.log('收到消息:', topic, mqttMsg)\n          this.handleMessage(topic, mqttMsg)\n        },\n        \n        onReconnect: () => {\n          console.log('MQTT重连中...')\n          this.addMessage('系统', '正在重连...', 'system')\n        },\n        \n        onError: (error) => {\n          console.error('MQTT连接错误:', error)\n          this.isConnected = false\n          this.addMessage('系统', '连接错误: ' + error.message, 'error')\n        },\n        \n        onEnd: () => {\n          console.log('MQTT连接已断开')\n          this.isConnected = false\n          this.addMessage('系统', '连接已断开', 'system')\n        }\n      }\n      \n      // 连接MQTT\n      try {\n        mqttClient.connect(userInfo, callbacks)\n      } catch (error) {\n        console.error('连接失败:', error)\n        uni.showToast({\n          title: '连接失败: ' + error.message,\n          icon: 'none'\n        })\n      }\n    },\n    \n    /**\n     * 断开MQTT连接\n     */\n    disconnectMqtt() {\n      mqttClient.disconnect(false, () => {\n        console.log('已断开MQTT连接')\n        this.isConnected = false\n        this.subscribedTopics = []\n        this.addMessage('系统', '主动断开连接', 'system')\n      })\n    },\n    \n    /**\n     * 发送消息\n     */\n    sendMessage() {\n      if (!this.messageInput.trim()) {\n        uni.showToast({\n          title: '请输入消息内容',\n          icon: 'none'\n        })\n        return\n      }\n      \n      // 创建聊天消息\n      const message = createChatMessage(\n        this.messageInput,\n        'demo-user',\n        'MQTT演示用户',\n        'demo-room'\n      )\n      \n      // 发布到演示主题\n      const topic = TOPIC_TEMPLATES.CHAT_ROOM('demo')\n      const success = mqttClient.publish(topic, message)\n      \n      if (success) {\n        this.addMessage(topic, this.messageInput, 'sent')\n        this.messageInput = ''\n        \n        uni.showToast({\n          title: '消息发送成功',\n          icon: 'success'\n        })\n      } else {\n        uni.showToast({\n          title: '发送失败，请检查连接',\n          icon: 'none'\n        })\n      }\n    },\n    \n    /**\n     * 订阅主题\n     */\n    subscribeTopic() {\n      if (!this.topicInput.trim()) {\n        uni.showToast({\n          title: '请输入主题名称',\n          icon: 'none'\n        })\n        return\n      }\n      \n      const topic = this.topicInput.trim()\n      \n      if (this.subscribedTopics.includes(topic)) {\n        uni.showToast({\n          title: '该主题已订阅',\n          icon: 'none'\n        })\n        return\n      }\n      \n      const success = mqttClient.subscribe(topic, {}, (err) => {\n        if (!err) {\n          this.subscribedTopics.push(topic)\n          this.addMessage('系统', `订阅主题成功: ${topic}`, 'system')\n          this.topicInput = ''\n          \n          uni.showToast({\n            title: '订阅成功',\n            icon: 'success'\n          })\n        } else {\n          console.error('订阅失败:', err)\n          uni.showToast({\n            title: '订阅失败',\n            icon: 'none'\n          })\n        }\n      })\n      \n      if (!success) {\n        uni.showToast({\n          title: '订阅失败，请检查连接',\n          icon: 'none'\n        })\n      }\n    },\n    \n    /**\n     * 取消订阅主题\n     */\n    unsubscribeTopic(topic) {\n      const success = mqttClient.unsubscribe(topic, (err) => {\n        if (!err) {\n          const index = this.subscribedTopics.indexOf(topic)\n          if (index > -1) {\n            this.subscribedTopics.splice(index, 1)\n          }\n          this.addMessage('系统', `取消订阅: ${topic}`, 'system')\n          \n          uni.showToast({\n            title: '取消订阅成功',\n            icon: 'success'\n          })\n        }\n      })\n      \n      if (!success) {\n        uni.showToast({\n          title: '取消订阅失败',\n          icon: 'none'\n        })\n      }\n    },\n    \n    /**\n     * 自动订阅演示主题\n     */\n    autoSubscribeTopics() {\n      const demoTopics = [\n        TOPIC_TEMPLATES.CHAT_ROOM('demo'),\n        '/mqtt-toolkit/demo',\n        '/test/topic'\n      ]\n      \n      demoTopics.forEach(topic => {\n        mqttClient.subscribe(topic, {}, (err) => {\n          if (!err) {\n            this.subscribedTopics.push(topic)\n            this.addMessage('系统', `自动订阅: ${topic}`, 'system')\n          }\n        })\n      })\n    },\n    \n    /**\n     * 处理接收到的消息\n     */\n    handleMessage(topic, mqttMsg) {\n      let content = ''\n      \n      if (mqttMsg.command === MESSAGE_TYPES.CHAT_MSG) {\n        const chatMsg = mqttMsg.data\n        content = `[${chatMsg.nickname}]: ${chatMsg.content}`\n      } else {\n        content = JSON.stringify(mqttMsg)\n      }\n      \n      this.addMessage(topic, content, 'received')\n    },\n    \n    /**\n     * 添加消息到列表\n     */\n    addMessage(topic, content, type = 'info') {\n      this.messages.push({\n        topic,\n        content,\n        type,\n        timestamp: Date.now()\n      })\n      \n      // 限制消息数量\n      if (this.messages.length > 100) {\n        this.messages.splice(0, this.messages.length - 100)\n      }\n      \n      // 滚动到底部\n      this.$nextTick(() => {\n        // 这里可以添加滚动到底部的逻辑\n      })\n    },\n    \n    /**\n     * 清空消息\n     */\n    clearMessages() {\n      this.messages = []\n      uni.showToast({\n        title: '消息已清空',\n        icon: 'success'\n      })\n    },\n    \n    /**\n     * 加载已订阅的主题\n     */\n    loadSubscribedTopics() {\n      // 这里可以从存储中加载已订阅的主题\n      // 暂时为空实现\n    },\n    \n    /**\n     * 格式化时间\n     */\n    formatTime(timestamp) {\n      const date = new Date(timestamp)\n      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`\n    }\n  }\n}\n</script>\n\n<style scoped>\n.mqtt-demo {\n  padding: 20rpx;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.status-section {\n  display: flex;\n  gap: 20rpx;\n  margin-bottom: 20rpx;\n}\n\n.status-card {\n  flex: 1;\n  background: white;\n  padding: 20rpx;\n  border-radius: 10rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.status-label {\n  font-size: 24rpx;\n  color: #666;\n  margin-bottom: 10rpx;\n}\n\n.status-value {\n  font-size: 28rpx;\n  font-weight: bold;\n}\n\n.connected {\n  color: #4CAF50;\n}\n\n.disconnected {\n  color: #f44336;\n}\n\n.control-section {\n  display: flex;\n  gap: 20rpx;\n  margin-bottom: 20rpx;\n}\n\n.control-btn {\n  flex: 1;\n  padding: 20rpx;\n  border-radius: 10rpx;\n  font-size: 28rpx;\n  border: none;\n}\n\n.connect-btn {\n  background-color: #4CAF50;\n  color: white;\n}\n\n.disconnect-btn {\n  background-color: #f44336;\n  color: white;\n}\n\n.control-btn:disabled {\n  background-color: #ccc;\n  color: #999;\n}\n\n.message-section, .topic-section {\n  background: white;\n  padding: 20rpx;\n  border-radius: 10rpx;\n  margin-bottom: 20rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  margin-bottom: 20rpx;\n  color: #333;\n}\n\n.input-group {\n  display: flex;\n  gap: 20rpx;\n  margin-bottom: 20rpx;\n}\n\n.message-input, .topic-input {\n  flex: 1;\n  padding: 20rpx;\n  border: 1px solid #ddd;\n  border-radius: 10rpx;\n  font-size: 28rpx;\n}\n\n.send-btn, .subscribe-btn {\n  padding: 20rpx 30rpx;\n  background-color: #2196F3;\n  color: white;\n  border: none;\n  border-radius: 10rpx;\n  font-size: 28rpx;\n}\n\n.send-btn:disabled, .subscribe-btn:disabled {\n  background-color: #ccc;\n}\n\n.subscribed-topics {\n  margin-top: 20rpx;\n}\n\n.topic-title {\n  font-size: 28rpx;\n  color: #666;\n  margin-bottom: 10rpx;\n}\n\n.topic-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15rpx;\n  background-color: #f9f9f9;\n  border-radius: 8rpx;\n  margin-bottom: 10rpx;\n}\n\n.topic-name {\n  font-size: 26rpx;\n  color: #333;\n}\n\n.unsubscribe-btn {\n  padding: 10rpx 20rpx;\n  background-color: #ff9800;\n  color: white;\n  border: none;\n  border-radius: 6rpx;\n  font-size: 24rpx;\n}\n\n.messages-section {\n  background: white;\n  padding: 20rpx;\n  border-radius: 10rpx;\n}\n\n.messages-container {\n  height: 400rpx;\n  border: 1px solid #eee;\n  border-radius: 8rpx;\n  padding: 10rpx;\n  margin-bottom: 20rpx;\n}\n\n.message-item {\n  margin-bottom: 20rpx;\n  padding: 15rpx;\n  border-radius: 8rpx;\n  border-left: 4rpx solid #ddd;\n}\n\n.message-item.sent {\n  background-color: #e3f2fd;\n  border-left-color: #2196F3;\n}\n\n.message-item.received {\n  background-color: #f3e5f5;\n  border-left-color: #9c27b0;\n}\n\n.message-item.system {\n  background-color: #fff3e0;\n  border-left-color: #ff9800;\n}\n\n.message-item.error {\n  background-color: #ffebee;\n  border-left-color: #f44336;\n}\n\n.message-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 10rpx;\n}\n\n.message-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.message-topic {\n  font-size: 24rpx;\n  color: #666;\n  font-weight: bold;\n}\n\n.message-content {\n  font-size: 26rpx;\n  color: #333;\n  word-break: break-all;\n}\n\n.clear-btn {\n  width: 100%;\n  padding: 20rpx;\n  background-color: #607d8b;\n  color: white;\n  border: none;\n  border-radius: 10rpx;\n  font-size: 28rpx;\n}\n</style>\n", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=1d0b7a26&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=1d0b7a26&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754969675947\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}