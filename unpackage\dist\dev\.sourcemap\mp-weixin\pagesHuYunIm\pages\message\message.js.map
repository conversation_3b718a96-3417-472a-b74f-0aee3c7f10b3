{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?40e7", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?6f9d", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?7aaf", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?ba47", "uni-app:///pagesHuYunIm/pages/message/message.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?a4db"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "navigation", "bottomOperation", "item", "name", "data", "isHistoryGet", "reserveHeight", "keyboardheightchangeValue", "myid", "scroll_top", "userList", "groupCount", "groupInfo", "id", "to", "history", "messages", "allLoaded", "page", "pageSize", "groupId", "loading", "loadend", "list", "messageId", "senderId", "senderData", "avatar", "type", "payload", "text", "timestamp", "recalled", "status", "isHide", "url", "userMap", "mqttClient", "mqttPingInterval", "groupIdNew", "computed", "watch", "handler", "deep", "onLoad", "console", "mounted", "methods", "initData", "touchmove", "onPage", "scroll", "scrolltoupper", "scrolltolower", "isSelf", "renderMessageDate", "formatTime", "onItem", "onLongpress", "uni", "itemList", "success", "copyMessage", "title", "icon", "deleteMessage", "content", "recallMessage", "recalledEdit", "mention", "imgLoad", "pushList", "initMessageItem", "message", "onBottom", "bottomOperationScrollToBottom", "focus", "keyboardheightchange", "scrollToBottom", "options"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AAC2L;AAC3L,gBAAgB,yLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA4tB,CAAgB,6qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACsGhvB;AAAA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;EAGAC;IACAC;IACAC;IACAC;EACA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;QACAC;QACAV;MACA;MACAW;MACA;MACAC;QACAC;QACAC;MACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,OACA;QACAV;QACAW;QACAC;QACAC;UACAvB;UACAwB;QACA;QACAC;QACAC;UACAC;QACA;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACArB;QACAW;QACAC;QACAC;UACAvB;UACAwB;QACA;QACAC;QACAC;UACAC;QACA;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACArB;QACAW;QACAC;QACAC;UACAvB;UACAwB;QACA;QACAC;QACAC;UACAC;QACA;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACArB;QACAW;QACAC;QACAC;UACAvB;UACAwB;QACA;QACAC;QACAC;UACAM;QACA;QACAJ;QACAC;QACAC;QACAC;MACA,GACA;QACArB;QACAW;QACAC;QACAC;UACAvB;UACAwB;QACA;QACAC;QACAC;UACAC;QACA;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MACAE;MACAC;MACAC;MACAC;IACA;EACA;EAEAC,4BACA,mCACA;EAEAC;IACAlB;MACAmB;QAAA;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EAEAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACAC;cACA;cACA;cACA;;cAEA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EAEAC;IACAD;IACA;EACA;EAEAE;IACA;IACAC;MAAA;MACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IAAA,CACA;IAEA;IACAC;MACA;IAAA,CACA;IAEA;IACAC;MACA;IAAA,CACA;IAEA;IACAC;MACAP;MACA;IACA;IAEA;IACAQ;MACAR;IACA;IAEA;IACAS;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;;MAEA;MACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;MACA;MAEA;MAEA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACAZ;IACA;IAEA;IACAa;MAAA;MACAb;MACA;MACAc;QACAC;QACAC;UACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;cACA;cACA;UAAA;QAEA;MACA;IACA;IAEA;IACAC;MACA;QACAH;UACAvD;UACAyD;YACAF;cACAI;cACAC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACAN;QACAI;QACAG;QACAL;UACA;YACA;cAAA;YAAA;YACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAM;MACA;QACAR;UACAI;UACAC;QACA;QACA;MACA;;MAEA;MACA;MACA;QACAL;UACAI;UACAC;QACA;QACA;MACA;MAEAL;QACAI;QACAG;QACAL;UACA;YACA3D;YACAyD;cACAI;cACAC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAI;MACAvB;MACA;IACA;IAEA;IACAwB;MACAxB;IACA;IAEA;IACAyB;MACAzB;IACA;IAEA;IACA0B;MACA1B;MACA;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IAEA;IACA2B;MACAC;MACA;MACA;QACAA;MACA;IACA;IAEA;IACAC;MACA7B;IACA;IAEA;IACA8B;MACA;IACA;IAEA;IACAC;MAAA;MACA/B;MACA;QACA;MACA;IACA;IAEA;IACAgC;MAAA;MACAhC;MACA;MACA;QACA;MACA;IACA;IAEA;IACAiC;MAAA;MACA;QACA;MACA;IACA;EACA;AAAA,kFAEAC;EACA;EACA;IACA;EACA;EACA;IACA;EACA;AACA,mFAEAA;EACA;EACA;IACA;EACA;EACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnhBA;AAAA;AAAA;AAAA;AAA+3C,CAAgB,ouCAAG,EAAC,C", "file": "pagesHuYunIm/pages/message/message.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesHuYunIm/pages/message/message.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./message.vue?vue&type=template&id=0b926c58&scoped=true&\"\nvar renderjs\nimport script from \"./message.vue?vue&type=script&lang=js&\"\nexport * from \"./message.vue?vue&type=script&lang=js&\"\nimport style0 from \"./message.vue?vue&type=style&index=0&id=0b926c58&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0b926c58\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/message/message.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=template&id=0b926c58&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = !item.isHide ? _vm.renderMessageDate(item, index) : null\n    var m1 = !item.isHide && !item.recalled ? _vm.isSelf(item.senderId) : null\n    var m2 = !item.isHide && !!item.recalled ? _vm.isSelf(item.senderId) : null\n    var m3 =\n      !item.isHide && !!item.recalled\n        ? item.type === \"text\" && _vm.isSelf(item.senderId)\n        : null\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n      m2: m2,\n      m3: m3,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"flex_c page\" @touchmove=\"touchmove\">\n    <!-- 导航栏 -->\n    <view class=\"navigationRef\">\n      <navigation ref=\"navigationRef\" :groupCount=\"groupCount\" :title=\"groupInfo.name\" :group_id=\"groupInfo.id\"></navigation>\n    </view>\n    <!-- 消息列表 -->\n    <scroll-view\n      class=\"flex1 scroll-Y\"\n      @tap.stop=\"onPage\"\n      id=\"scroll-view\"\n      lower-threshold=\"100\"\n      scroll-y\n      scroll-with-animation\n      :scroll-top=\"scroll_top\"\n      @scroll=\"scroll\"\n      @scrolltoupper=\"scrolltoupper\"\n      @scrolltolower=\"scrolltolower\"\n    >\n      <view class=\"scroll-view-str\" :style=\"{ height: `${reserveHeight}px` }\" v-if=\"reserveHeight > 0\"></view>\n      <view class=\"messageList_\">\n        <template v-for=\"(item, index) in list\">\n          <!-- #ifdef APP || H5 -->\n          <view\n            class=\"z_index2\"\n            :class=\"`oneheight_${index}`\"\n            style=\"transform: rotate(-180deg)\"\n            :key=\"item.id + index\"\n            v-if=\"!item.isHide\"\n          >\n            <view class=\"icon_ text_26 color__ time\">\n              {{ renderMessageDate(item, index) }}\n            </view>\n            <view :key=\"item.messageId + index\" v-if=\"!item.recalled\">\n              <item\n                :isMy=\"isSelf(item.senderId)\"\n                :myid=\"myid\"\n                :item=\"item\"\n                @onClick=\"onItem\"\n                @onLongpress=\"onLongpress\"\n                @mention=\"mention\"\n                @imgLoad=\"imgLoad\"\n              ></item>\n            </view>\n            <view class=\"icon_ text_26 recalled\" v-else>\n              <view class=\"\">\n                <text v-if=\"isSelf(item.senderId)\">你</text>\n                <text v-else>{{ item.senderData.name }}</text>\n                撤回了一条消息\n              </view>\n              <view class=\"recalled-edit\" v-if=\"item.type === 'text' && isSelf(item.senderId)\" @click=\"recalledEdit(item)\">重新编辑</view>\n            </view>\n          </view>\n          <!-- #endif -->\n          <!-- #ifdef MP -->\n          <view class=\"z_index2\" style=\"transform: rotate(-180deg)\" :key=\"item.id\" v-if=\"!item.isHide\">\n            <view class=\"icon_ text_26 color__ time\">\n              {{ renderMessageDate(item, index) }}\n            </view>\n            <view :key=\"item.id\" v-if=\"!item.recalled\">\n              <item\n                :isMy=\"isSelf(item.senderId)\"\n                :myid=\"myid\"\n                :item=\"item\"\n                @onClick=\"onItem\"\n                @onLongpress=\"onLongpress\"\n                @mention=\"mention\"\n              ></item>\n            </view>\n            <view class=\"icon_ text_26 recalled\" v-else>\n              <view class=\"\">\n                <text v-if=\"isSelf(item.senderId)\">你</text>\n                <text v-else>{{ item.senderData.name }}</text>\n                撤回了一条消息\n              </view>\n              <view class=\"recalled-edit\" v-if=\"item.type === 'text' && isSelf(item.senderId)\" @click=\"recalledEdit(item)\">重新编辑</view>\n            </view>\n          </view>\n          <!-- #endif -->\n        </template>\n      </view>\n      <view :style=\"{ height: $store.state.StatusBar.customBar - 8 + 4 + 'px' }\"></view>\n    </scroll-view>\n    <!-- 底部操作区域 -->\n    <view class=\"bottomOperationRef\">\n      <bottom-operation\n        ref=\"bottomOperationRef\"\n        :to=\"to\"\n        :userList=\"userList\"\n        @pushList=\"pushList\"\n        @onBottom=\"onBottom\"\n        @backToBottom=\"bottomOperationScrollToBottom\"\n        @focus=\"focus\"\n        @keyboardheightchange=\"keyboardheightchange\"\n      ></bottom-operation>\n    </view>\n  </view>\n</template>\n<script>\nimport navigation from '../chat/components/navigation/index.vue'\nimport bottomOperation from '../chat/components/bottom-operation/index.vue'\nimport item from '../chat/components/item/index.vue'\nimport { mapState } from 'vuex'\n\n// 是否是手动触发的列表滑动\nlet isBottomOperationScrollToBottom = false\n\nconst IMAGE_MAX_WIDTH = 200\nconst IMAGE_MAX_HEIGHT = 150\nlet scroll_top = 0\nlet reserveHeightRef = 0\nlet bottomOperationRefHeight = 0\n\nexport default {\n  components: {\n    navigation,\n    bottomOperation,\n    item\n  },\n  name: 'groupChat',\n  data() {\n    return {\n      isHistoryGet: false,\n      reserveHeight: 0,\n      keyboardheightchangeValue: 0,\n      myid: null,\n      scroll_top,\n      userList: [], //群成员列表\n      groupCount: '',\n      groupInfo: {\n        id: '1921827090039152642',\n        name: '项目讨论组'\n      },\n      to: {},\n      // 历史数据\n      history: {\n        messages: [],\n        allLoaded: false\n      },\n      // 添加缺失的数据属性\n      page: 1,\n      pageSize: 50,\n      groupId: '',\n      loading: false,\n      loadend: false,\n      list: [\n        {\n          id: '1921827090039152642_000001',\n          messageId: '1921827090039152642_000001',\n          senderId: '1921822887908581377',\n          senderData: {\n            name: '张三',\n            avatar: 'https://via.placeholder.com/96x96/2196F3/FFFFFF?text=张'\n          },\n          type: 'text',\n          payload: {\n            text: '大家好，今天的项目进度怎么样？'\n          },\n          timestamp: Date.now() - 300000,\n          recalled: false,\n          status: 'success',\n          isHide: false\n        },\n        {\n          id: '1921827090039152642_000002',\n          messageId: '1921827090039152642_000002',\n          senderId: '1921822887908581378',\n          senderData: {\n            name: '李四',\n            avatar: 'https://via.placeholder.com/96x96/4CAF50/FFFFFF?text=李'\n          },\n          type: 'text',\n          payload: {\n            text: '我这边基本完成了，正在测试'\n          },\n          timestamp: Date.now() - 240000,\n          recalled: false,\n          status: 'success',\n          isHide: false\n        },\n        {\n          id: '1921827090039152642_000003',\n          messageId: '1921827090039152642_000003',\n          senderId: '1921822887908581379',\n          senderData: {\n            name: '王五',\n            avatar: 'https://via.placeholder.com/96x96/FF9800/FFFFFF?text=王'\n          },\n          type: 'text',\n          payload: {\n            text: '我还需要一点时间，预计明天完成'\n          },\n          timestamp: Date.now() - 180000,\n          recalled: false,\n          status: 'success',\n          isHide: false\n        },\n        {\n          id: '1921827090039152642_000004',\n          messageId: '1921827090039152642_000004',\n          senderId: '1921822887908581380',\n          senderData: {\n            name: '赵六',\n            avatar: 'https://via.placeholder.com/96x96/9C27B0/FFFFFF?text=赵'\n          },\n          type: 'image',\n          payload: {\n            url: 'https://via.placeholder.com/200x150/FF5722/FFFFFF?text=截图'\n          },\n          timestamp: Date.now() - 120000,\n          recalled: false,\n          status: 'success',\n          isHide: false\n        },\n        {\n          id: '1921827090039152642_000005',\n          messageId: '1921827090039152642_000005',\n          senderId: '1921822887908581378',\n          senderData: {\n            name: '李四',\n            avatar: 'https://via.placeholder.com/96x96/4CAF50/FFFFFF?text=李'\n          },\n          type: 'text',\n          payload: {\n            text: '看起来不错！'\n          },\n          timestamp: Date.now() - 60000,\n          recalled: false,\n          status: 'success',\n          isHide: false\n        }\n      ],\n      userMap: {},\n      mqttClient: null,\n      mqttPingInterval: null,\n      groupIdNew: 0\n    }\n  },\n\n  computed: {\n    ...mapState(['StatusBar'])\n  },\n\n  watch: {\n    list: {\n      handler() {\n        this.$nextTick(() => {\n          this.scrollToBottom()\n        })\n      },\n      deep: true\n    }\n  },\n\n  async onLoad(e) {\n    console.log('🚀 ~ onLoad ~ e:', e)\n    this.groupIdNew = e.groupId || '1921827090039152642'\n    this.groupInfo.id = this.groupIdNew\n    this.myid = '1921822887908581378' // 设置当前用户ID\n\n    // 初始化数据\n    this.initData()\n  },\n\n  mounted() {\n    console.log('页面已挂载，消息列表长度:', this.list.length)\n    this.scrollToBottom()\n  },\n\n  methods: {\n    // 初始化数据\n    initData() {\n      // 设置当前用户ID\n      this.myid = '1921822887908581378'\n\n      // 滚动到底部\n      this.$nextTick(() => {\n        this.scrollToBottom()\n      })\n    },\n\n    // 触摸移动事件\n    touchmove() {\n      // 处理触摸移动\n    },\n\n    // 页面点击事件\n    onPage() {\n      // 处理页面点击\n    },\n\n    // 滚动事件\n    scroll(e) {\n      // 处理滚动\n    },\n\n    // 滚动到顶部\n    scrolltoupper() {\n      console.log('滚动到顶部，加载更多消息')\n      // 加载更多历史消息\n    },\n\n    // 滚动到底部\n    scrolltolower() {\n      console.log('滚动到底部')\n    },\n\n    // 判断是否为自己发送的消息\n    isSelf(senderId) {\n      return senderId === this.myid\n    },\n\n    // 渲染消息日期\n    renderMessageDate(item, index) {\n      if (index === 0) return ''\n\n      const currentTime = new Date(item.timestamp)\n      const prevTime = new Date(this.list[index - 1].timestamp)\n\n      // 如果时间间隔超过5分钟，显示时间\n      if (currentTime - prevTime > 5 * 60 * 1000) {\n        return this.formatTime(currentTime)\n      }\n\n      return ''\n    },\n\n    // 格式化时间\n    formatTime(date) {\n      const now = new Date()\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())\n      const msgDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())\n\n      const diffDays = Math.floor((today - msgDate) / (24 * 60 * 60 * 1000))\n\n      if (diffDays === 0) {\n        // 今天，显示时间\n        return date.toTimeString().slice(0, 5)\n      } else if (diffDays === 1) {\n        // 昨天\n        return `昨天 ${date.toTimeString().slice(0, 5)}`\n      } else {\n        // 更早，显示日期\n        return `${date.getMonth() + 1}月${date.getDate()}日 ${date.toTimeString().slice(0, 5)}`\n      }\n    },\n\n    // 消息项点击事件\n    onItem(item) {\n      console.log('消息项点击:', item)\n    },\n\n    // 消息项长按事件\n    onLongpress(item) {\n      console.log('消息项长按:', item)\n      // 显示消息操作菜单\n      uni.showActionSheet({\n        itemList: ['复制', '删除', '撤回'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0:\n              this.copyMessage(item)\n              break\n            case 1:\n              this.deleteMessage(item)\n              break\n            case 2:\n              this.recallMessage(item)\n              break\n          }\n        }\n      })\n    },\n\n    // 复制消息\n    copyMessage(item) {\n      if (item.type === 'text') {\n        uni.setClipboardData({\n          data: item.payload.text,\n          success: () => {\n            uni.showToast({\n              title: '已复制',\n              icon: 'success'\n            })\n          }\n        })\n      }\n    },\n\n    // 删除消息\n    deleteMessage(item) {\n      uni.showModal({\n        title: '确认删除',\n        content: '确定要删除这条消息吗？',\n        success: (res) => {\n          if (res.confirm) {\n            const index = this.list.findIndex((msg) => msg.id === item.id)\n            if (index > -1) {\n              this.list.splice(index, 1)\n            }\n          }\n        }\n      })\n    },\n\n    // 撤回消息\n    recallMessage(item) {\n      if (!this.isSelf(item.senderId)) {\n        uni.showToast({\n          title: '只能撤回自己的消息',\n          icon: 'none'\n        })\n        return\n      }\n\n      // 检查时间限制（比如2分钟内）\n      const now = new Date().getTime()\n      if (now - item.timestamp > 2 * 60 * 1000) {\n        uni.showToast({\n          title: '超过时间限制，无法撤回',\n          icon: 'none'\n        })\n        return\n      }\n\n      uni.showModal({\n        title: '确认撤回',\n        content: '确定要撤回这条消息吗？',\n        success: (res) => {\n          if (res.confirm) {\n            item.recalled = true\n            uni.showToast({\n              title: '已撤回',\n              icon: 'success'\n            })\n          }\n        }\n      })\n    },\n\n    // 重新编辑撤回的消息\n    recalledEdit(item) {\n      console.log('重新编辑消息:', item)\n      // 这里可以将撤回的消息内容填入输入框\n    },\n\n    // @mention 事件\n    mention(user) {\n      console.log('提及用户:', user)\n    },\n\n    // 图片加载事件\n    imgLoad(e) {\n      console.log('图片加载:', e)\n    },\n\n    // 底部操作相关方法\n    pushList(message) {\n      console.log('推送新消息:', message)\n      // 初始化消息项\n      this.initMessageItem(message)\n\n      // 添加到消息列表\n      this.list.unshift(message)\n\n      // 滚动到底部\n      this.scrollToBottom()\n    },\n\n    // 初始化消息项\n    initMessageItem(message) {\n      message['isHide'] = false\n      // 初始化语音\n      if (message.type === 'audio') {\n        message['pause'] = 4\n      }\n    },\n\n    // 底部操作事件\n    onBottom() {\n      console.log('底部操作')\n    },\n\n    // 滚动到底部（底部操作触发）\n    bottomOperationScrollToBottom() {\n      this.scrollToBottom()\n    },\n\n    // 输入框获得焦点\n    focus() {\n      console.log('输入框获得焦点')\n      this.$nextTick(() => {\n        this.scrollToBottom()\n      })\n    },\n\n    // 键盘高度变化\n    keyboardheightchange(e) {\n      console.log('键盘高度变化:', e)\n      this.keyboardheightchangeValue = e.detail.height\n      this.$nextTick(() => {\n        this.scrollToBottom()\n      })\n    },\n\n    // 滚动到底部\n    scrollToBottom() {\n      this.$nextTick(() => {\n        this.scroll_top = this.list.length * 1000\n      })\n    }\n  },\n\n  onLoad(options) {\n    // 从参数中获取群组信息\n    if (options.groupId) {\n      this.groupInfo.id = options.groupId\n    }\n    if (options.groupName) {\n      this.groupInfo.name = decodeURIComponent(options.groupName)\n    }\n  },\n\n  onLoad(options) {\n    // 从参数中获取群组信息\n    if (options.groupId) {\n      this.groupInfo.id = options.groupId\n    }\n    if (options.groupName) {\n      this.groupInfo.name = decodeURIComponent(options.groupName)\n    }\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n// 引入聊天页面的通用样式\n@import '../../static/style/chatInterface.css';\n\n.page {\n  height: 100vh;\n  background-color: #f8f8f8;\n}\n\n.flex_c {\n  display: flex;\n  flex-direction: column;\n}\n\n.flex1 {\n  flex: 1;\n}\n\n.scroll-Y {\n  overflow-y: auto;\n}\n\n.navigationRef {\n  position: sticky;\n  top: 0;\n  z-index: 100;\n}\n\n.bottomOperationRef {\n  position: sticky;\n  bottom: 0;\n  z-index: 100;\n}\n\n.messageList_ {\n  padding: 20rpx;\n  transform: rotate(180deg);\n}\n\n.z_index2 {\n  position: relative;\n  z-index: 2;\n}\n\n.icon_ {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.text_26 {\n  font-size: 26rpx;\n}\n\n.color__ {\n  color: #999999;\n}\n\n.time {\n  text-align: center;\n  margin: 20rpx 0;\n  padding: 8rpx 16rpx;\n  background-color: rgba(0, 0, 0, 0.1);\n  border-radius: 16rpx;\n  color: #ffffff;\n  font-size: 24rpx;\n  display: inline-block;\n}\n\n.recalled {\n  text-align: center;\n  margin: 20rpx 0;\n  color: #999999;\n  font-size: 26rpx;\n}\n\n.recalled-edit {\n  color: #007aff;\n  text-decoration: underline;\n  margin-left: 10rpx;\n  cursor: pointer;\n}\n\n// 响应式适配\n@media screen and (max-width: 750rpx) {\n  .messageList_ {\n    padding: 16rpx;\n  }\n\n  .time {\n    font-size: 22rpx;\n    padding: 6rpx 12rpx;\n  }\n\n  .recalled {\n    font-size: 24rpx;\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=style&index=0&id=0b926c58&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=style&index=0&id=0b926c58&lang=scss&scoped=true&\""], "sourceRoot": ""}