<template>
	<view class="content">
		<image class="logo" src="/static/logo.png"></image>
		<view class="text-area">
			<text class="title">{{ title }}</text>
		</view>
		<button @click="to('/pagesGoEasy/sessionList/index')">会话页面</button>
		<button @click="to('/pagesHuYunIm/pages/index/index')" class="mqtt-btn">MQTT工具包</button>
	</view>
</template>

<script>
import { to } from '@/utils/index.js'

export default {
	data() {
		return {
			title: 'Hello',
			userMap: {}, // 用户映射表
			users: [] // 用户列表
		}
	},
	onLoad() {
		// this.initMqtt()
	},
	methods: {
		to,

		/**
		 * 初始化MQTT连接
		 */
		initMqtt() {
			// 使用配置工具创建用户信息
			const userInfo = createUserInfo(
				'1921822887908581377', // userId
				'范发发', // nickname
				'hbs119', // channelCode
				'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImhiczExOSIsImNsaWVudGlkIjoiMTkyMTgyMjg4NzkwODU4MTM3NyIsImV4cCI6MTc1NTA1MzA0OH0.mXmgwG-lPKr3Krrva_i9k1B4PN45QCwr_GFkFFjg4hU', // token
				'https://dummyimage.com/200x200/3c9cff/fff', // avatar
				'DEV' // 环境
			)

			const callbacks = {
				onConnect: () => {
					console.log('MQTT连接成功，可以开始聊天了')
				},
				onMessage: (_, mqttMsg) => {
					this.handleMqttMessage(mqttMsg)
				},
				onReconnect: () => {
					console.log('MQTT重连中...')
				},
				onError: (error) => {
					console.error('MQTT连接错误:', error)
				},
				onEnd: () => {
					console.log('MQTT连接已断开')
				}
			}

			// 使用工具类连接MQTT
			mqttClient.connect(userInfo, callbacks)
		},
		/**
		 * 处理MQTT消息
		 * @param {Object} mqttMsg - MQTT消息对象
		 */
		handleMqttMessage(mqttMsg) {
			if (mqttMsg.command === MESSAGE_TYPES.CHAT_MSG) {
				const chatMsg = mqttMsg.data
				// 设置用户昵称
				if (this.userMap.hasOwnProperty(chatMsg.userId)) {
					chatMsg.nickname = this.userMap[chatMsg.userId].nickname
				}
				// 更新用户列表中的消息信息
				for (let i = 0; i < this.users.length; i++) {
					if (this.users[i].id === chatMsg.groupId) {
						this.users[i].lastMsg = chatMsg
						this.users[i].updateTime = chatMsg.createTime
						this.users[i].notReadNum = this.users[i].notReadNum + 1
					}
				}
				// 消息通知
				console.log('收到聊天消息:', chatMsg)
			}
		}
	},

	onUnload() {
		// 页面卸载时断开MQTT连接
		mqttClient.disconnect()
	}
}
</script>

<style>
.content {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.logo {
	height: 200rpx;
	width: 200rpx;
	margin-top: 200rpx;
	margin-left: auto;
	margin-right: auto;
	margin-bottom: 50rpx;
}

.text-area {
	display: flex;
	justify-content: center;
}

.title {
	font-size: 36rpx;
	color: #8f8f94;
}

.mqtt-btn {
	margin-top: 20rpx;
	background-color: #007aff;
	color: white;
	border-radius: 10rpx;
	padding: 20rpx;
}
</style>
