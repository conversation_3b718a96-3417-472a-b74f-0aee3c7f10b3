@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.text_1 {
  font-size: 1rpx;
}
.text_2 {
  font-size: 2rpx;
}
.text_3 {
  font-size: 3rpx;
}
.text_4 {
  font-size: 4rpx;
}
.text_5 {
  font-size: 5rpx;
}
.text_6 {
  font-size: 6rpx;
}
.text_7 {
  font-size: 7rpx;
}
.text_8 {
  font-size: 8rpx;
}
.text_9 {
  font-size: 9rpx;
}
.text_10 {
  font-size: 10rpx;
}
.text_11 {
  font-size: 11rpx;
}
.text_12 {
  font-size: 12rpx;
}
.text_13 {
  font-size: 13rpx;
}
.text_14 {
  font-size: 14rpx;
}
.text_15 {
  font-size: 15rpx;
}
.text_16 {
  font-size: 16rpx;
}
.text_17 {
  font-size: 17rpx;
}
.text_18 {
  font-size: 18rpx;
}
.text_19 {
  font-size: 19rpx;
}
.text_20 {
  font-size: 20rpx;
}
.text_21 {
  font-size: 21rpx;
}
.text_22 {
  font-size: 22rpx;
}
.text_23 {
  font-size: 23rpx;
}
.text_24 {
  font-size: 24rpx;
}
.text_25 {
  font-size: 25rpx;
}
.text_26 {
  font-size: 26rpx;
}
.text_27 {
  font-size: 27rpx;
}
.text_28 {
  font-size: 28rpx;
}
.text_29 {
  font-size: 29rpx;
}
.text_30 {
  font-size: 30rpx;
}
.text_31 {
  font-size: 31rpx;
}
.text_32 {
  font-size: 32rpx;
}
.text_33 {
  font-size: 33rpx;
}
.text_34 {
  font-size: 34rpx;
}
.text_35 {
  font-size: 35rpx;
}
.text_36 {
  font-size: 36rpx;
}
.text_37 {
  font-size: 37rpx;
}
.text_38 {
  font-size: 38rpx;
}
.text_39 {
  font-size: 39rpx;
}
.text_40 {
  font-size: 40rpx;
}
.text_41 {
  font-size: 41rpx;
}
.text_42 {
  font-size: 42rpx;
}
.text_43 {
  font-size: 43rpx;
}
.text_44 {
  font-size: 44rpx;
}
.text_45 {
  font-size: 45rpx;
}
.text_46 {
  font-size: 46rpx;
}
.text_47 {
  font-size: 47rpx;
}
.text_48 {
  font-size: 48rpx;
}
.text_49 {
  font-size: 49rpx;
}
.text_50 {
  font-size: 50rpx;
}
.text_51 {
  font-size: 51rpx;
}
.text_52 {
  font-size: 52rpx;
}
.text_53 {
  font-size: 53rpx;
}
.text_54 {
  font-size: 54rpx;
}
.text_55 {
  font-size: 55rpx;
}
.text_56 {
  font-size: 56rpx;
}
.text_57 {
  font-size: 57rpx;
}
.text_58 {
  font-size: 58rpx;
}
.text_59 {
  font-size: 59rpx;
}
/* 小号 */
#page_font_size_min .text_1 {
  font-size: calc(1rpx - 2rpx);
}
#page_font_size_min .text_2 {
  font-size: calc(2rpx - 2rpx);
}
#page_font_size_min .text_3 {
  font-size: calc(3rpx - 2rpx);
}
#page_font_size_min .text_4 {
  font-size: calc(4rpx - 2rpx);
}
#page_font_size_min .text_5 {
  font-size: calc(5rpx - 2rpx);
}
#page_font_size_min .text_6 {
  font-size: calc(6rpx - 2rpx);
}
#page_font_size_min .text_7 {
  font-size: calc(7rpx - 2rpx);
}
#page_font_size_min .text_8 {
  font-size: calc(8rpx - 2rpx);
}
#page_font_size_min .text_9 {
  font-size: calc(9rpx - 2rpx);
}
#page_font_size_min .text_10 {
  font-size: calc(10rpx - 2rpx);
}
#page_font_size_min .text_11 {
  font-size: calc(11rpx - 2rpx);
}
#page_font_size_min .text_12 {
  font-size: calc(12rpx - 2rpx);
}
#page_font_size_min .text_13 {
  font-size: calc(13rpx - 2rpx);
}
#page_font_size_min .text_14 {
  font-size: calc(14rpx - 2rpx);
}
#page_font_size_min .text_15 {
  font-size: calc(15rpx - 2rpx);
}
#page_font_size_min .text_16 {
  font-size: calc(16rpx - 2rpx);
}
#page_font_size_min .text_17 {
  font-size: calc(17rpx - 2rpx);
}
#page_font_size_min .text_18 {
  font-size: calc(18rpx - 2rpx);
}
#page_font_size_min .text_19 {
  font-size: calc(19rpx - 2rpx);
}
#page_font_size_min .text_20 {
  font-size: calc(20rpx - 2rpx);
}
#page_font_size_min .text_21 {
  font-size: calc(21rpx - 2rpx);
}
#page_font_size_min .text_22 {
  font-size: calc(22rpx - 2rpx);
}
#page_font_size_min .text_23 {
  font-size: calc(23rpx - 2rpx);
}
#page_font_size_min .text_24 {
  font-size: calc(24rpx - 2rpx);
}
#page_font_size_min .text_25 {
  font-size: calc(25rpx - 2rpx);
}
#page_font_size_min .text_26 {
  font-size: calc(26rpx - 2rpx);
}
#page_font_size_min .text_27 {
  font-size: calc(27rpx - 2rpx);
}
#page_font_size_min .text_28 {
  font-size: calc(28rpx - 2rpx);
}
#page_font_size_min .text_29 {
  font-size: calc(29rpx - 2rpx);
}
#page_font_size_min .text_30 {
  font-size: calc(30rpx - 2rpx);
}
#page_font_size_min .text_31 {
  font-size: calc(31rpx - 2rpx);
}
#page_font_size_min .text_32 {
  font-size: calc(32rpx - 2rpx);
}
#page_font_size_min .text_33 {
  font-size: calc(33rpx - 2rpx);
}
#page_font_size_min .text_34 {
  font-size: calc(34rpx - 2rpx);
}
#page_font_size_min .text_35 {
  font-size: calc(35rpx - 2rpx);
}
#page_font_size_min .text_36 {
  font-size: calc(36rpx - 2rpx);
}
#page_font_size_min .text_37 {
  font-size: calc(37rpx - 2rpx);
}
#page_font_size_min .text_38 {
  font-size: calc(38rpx - 2rpx);
}
#page_font_size_min .text_39 {
  font-size: calc(39rpx - 2rpx);
}
#page_font_size_min .text_40 {
  font-size: calc(40rpx - 2rpx);
}
#page_font_size_min .text_41 {
  font-size: calc(41rpx - 2rpx);
}
#page_font_size_min .text_42 {
  font-size: calc(42rpx - 2rpx);
}
#page_font_size_min .text_43 {
  font-size: calc(43rpx - 2rpx);
}
#page_font_size_min .text_44 {
  font-size: calc(44rpx - 2rpx);
}
#page_font_size_min .text_45 {
  font-size: calc(45rpx - 2rpx);
}
#page_font_size_min .text_46 {
  font-size: calc(46rpx - 2rpx);
}
#page_font_size_min .text_47 {
  font-size: calc(47rpx - 2rpx);
}
#page_font_size_min .text_48 {
  font-size: calc(48rpx - 2rpx);
}
#page_font_size_min .text_49 {
  font-size: calc(49rpx - 2rpx);
}
#page_font_size_min .text_50 {
  font-size: calc(50rpx - 2rpx);
}
#page_font_size_min .text_51 {
  font-size: calc(51rpx - 2rpx);
}
#page_font_size_min .text_52 {
  font-size: calc(52rpx - 2rpx);
}
#page_font_size_min .text_53 {
  font-size: calc(53rpx - 2rpx);
}
#page_font_size_min .text_54 {
  font-size: calc(54rpx - 2rpx);
}
#page_font_size_min .text_55 {
  font-size: calc(55rpx - 2rpx);
}
#page_font_size_min .text_56 {
  font-size: calc(56rpx - 2rpx);
}
#page_font_size_min .text_57 {
  font-size: calc(57rpx - 2rpx);
}
#page_font_size_min .text_58 {
  font-size: calc(58rpx - 2rpx);
}
#page_font_size_min .text_59 {
  font-size: calc(59rpx - 2rpx);
}
/* 标准 */
#page_font_size .text_1 {
  font-size: 1rpx;
}
#page_font_size .text_2 {
  font-size: 2rpx;
}
#page_font_size .text_3 {
  font-size: 3rpx;
}
#page_font_size .text_4 {
  font-size: 4rpx;
}
#page_font_size .text_5 {
  font-size: 5rpx;
}
#page_font_size .text_6 {
  font-size: 6rpx;
}
#page_font_size .text_7 {
  font-size: 7rpx;
}
#page_font_size .text_8 {
  font-size: 8rpx;
}
#page_font_size .text_9 {
  font-size: 9rpx;
}
#page_font_size .text_10 {
  font-size: 10rpx;
}
#page_font_size .text_11 {
  font-size: 11rpx;
}
#page_font_size .text_12 {
  font-size: 12rpx;
}
#page_font_size .text_13 {
  font-size: 13rpx;
}
#page_font_size .text_14 {
  font-size: 14rpx;
}
#page_font_size .text_15 {
  font-size: 15rpx;
}
#page_font_size .text_16 {
  font-size: 16rpx;
}
#page_font_size .text_17 {
  font-size: 17rpx;
}
#page_font_size .text_18 {
  font-size: 18rpx;
}
#page_font_size .text_19 {
  font-size: 19rpx;
}
#page_font_size .text_20 {
  font-size: 20rpx;
}
#page_font_size .text_21 {
  font-size: 21rpx;
}
#page_font_size .text_22 {
  font-size: 22rpx;
}
#page_font_size .text_23 {
  font-size: 23rpx;
}
#page_font_size .text_24 {
  font-size: 24rpx;
}
#page_font_size .text_25 {
  font-size: 25rpx;
}
#page_font_size .text_26 {
  font-size: 26rpx;
}
#page_font_size .text_27 {
  font-size: 27rpx;
}
#page_font_size .text_28 {
  font-size: 28rpx;
}
#page_font_size .text_29 {
  font-size: 29rpx;
}
#page_font_size .text_30 {
  font-size: 30rpx;
}
#page_font_size .text_31 {
  font-size: 31rpx;
}
#page_font_size .text_32 {
  font-size: 32rpx;
}
#page_font_size .text_33 {
  font-size: 33rpx;
}
#page_font_size .text_34 {
  font-size: 34rpx;
}
#page_font_size .text_35 {
  font-size: 35rpx;
}
#page_font_size .text_36 {
  font-size: 36rpx;
}
#page_font_size .text_37 {
  font-size: 37rpx;
}
#page_font_size .text_38 {
  font-size: 38rpx;
}
#page_font_size .text_39 {
  font-size: 39rpx;
}
#page_font_size .text_40 {
  font-size: 40rpx;
}
#page_font_size .text_41 {
  font-size: 41rpx;
}
#page_font_size .text_42 {
  font-size: 42rpx;
}
#page_font_size .text_43 {
  font-size: 43rpx;
}
#page_font_size .text_44 {
  font-size: 44rpx;
}
#page_font_size .text_45 {
  font-size: 45rpx;
}
#page_font_size .text_46 {
  font-size: 46rpx;
}
#page_font_size .text_47 {
  font-size: 47rpx;
}
#page_font_size .text_48 {
  font-size: 48rpx;
}
#page_font_size .text_49 {
  font-size: 49rpx;
}
#page_font_size .text_50 {
  font-size: 50rpx;
}
#page_font_size .text_51 {
  font-size: 51rpx;
}
#page_font_size .text_52 {
  font-size: 52rpx;
}
#page_font_size .text_53 {
  font-size: 53rpx;
}
#page_font_size .text_54 {
  font-size: 54rpx;
}
#page_font_size .text_55 {
  font-size: 55rpx;
}
#page_font_size .text_56 {
  font-size: 56rpx;
}
#page_font_size .text_57 {
  font-size: 57rpx;
}
#page_font_size .text_58 {
  font-size: 58rpx;
}
#page_font_size .text_59 {
  font-size: 59rpx;
}
/* 大 */
#page_font_size_max .text_1 {
  font-size: calc(1rpx + 6rpx);
}
#page_font_size_max .text_2 {
  font-size: calc(2rpx + 6rpx);
}
#page_font_size_max .text_3 {
  font-size: calc(3rpx + 6rpx);
}
#page_font_size_max .text_4 {
  font-size: calc(4rpx + 6rpx);
}
#page_font_size_max .text_5 {
  font-size: calc(5rpx + 6rpx);
}
#page_font_size_max .text_6 {
  font-size: calc(6rpx + 6rpx);
}
#page_font_size_max .text_7 {
  font-size: calc(7rpx + 6rpx);
}
#page_font_size_max .text_8 {
  font-size: calc(8rpx + 6rpx);
}
#page_font_size_max .text_9 {
  font-size: calc(9rpx + 6rpx);
}
#page_font_size_max .text_10 {
  font-size: calc(10rpx + 6rpx);
}
#page_font_size_max .text_11 {
  font-size: calc(11rpx + 6rpx);
}
#page_font_size_max .text_12 {
  font-size: calc(12rpx + 6rpx);
}
#page_font_size_max .text_13 {
  font-size: calc(13rpx + 6rpx);
}
#page_font_size_max .text_14 {
  font-size: calc(14rpx + 6rpx);
}
#page_font_size_max .text_15 {
  font-size: calc(15rpx + 6rpx);
}
#page_font_size_max .text_16 {
  font-size: calc(16rpx + 6rpx);
}
#page_font_size_max .text_17 {
  font-size: calc(17rpx + 6rpx);
}
#page_font_size_max .text_18 {
  font-size: calc(18rpx + 6rpx);
}
#page_font_size_max .text_19 {
  font-size: calc(19rpx + 6rpx);
}
#page_font_size_max .text_20 {
  font-size: calc(20rpx + 6rpx);
}
#page_font_size_max .text_21 {
  font-size: calc(21rpx + 6rpx);
}
#page_font_size_max .text_22 {
  font-size: calc(22rpx + 6rpx);
}
#page_font_size_max .text_23 {
  font-size: calc(23rpx + 6rpx);
}
#page_font_size_max .text_24 {
  font-size: calc(24rpx + 6rpx);
}
#page_font_size_max .text_25 {
  font-size: calc(25rpx + 6rpx);
}
#page_font_size_max .text_26 {
  font-size: calc(26rpx + 6rpx);
}
#page_font_size_max .text_27 {
  font-size: calc(27rpx + 6rpx);
}
#page_font_size_max .text_28 {
  font-size: calc(28rpx + 6rpx);
}
#page_font_size_max .text_29 {
  font-size: calc(29rpx + 6rpx);
}
#page_font_size_max .text_30 {
  font-size: calc(30rpx + 6rpx);
}
#page_font_size_max .text_31 {
  font-size: calc(31rpx + 6rpx);
}
#page_font_size_max .text_32 {
  font-size: calc(32rpx + 6rpx);
}
#page_font_size_max .text_33 {
  font-size: calc(33rpx + 6rpx);
}
#page_font_size_max .text_34 {
  font-size: calc(34rpx + 6rpx);
}
#page_font_size_max .text_35 {
  font-size: calc(35rpx + 6rpx);
}
#page_font_size_max .text_36 {
  font-size: calc(36rpx + 6rpx);
}
#page_font_size_max .text_37 {
  font-size: calc(37rpx + 6rpx);
}
#page_font_size_max .text_38 {
  font-size: calc(38rpx + 6rpx);
}
#page_font_size_max .text_39 {
  font-size: calc(39rpx + 6rpx);
}
#page_font_size_max .text_40 {
  font-size: calc(40rpx + 6rpx);
}
#page_font_size_max .text_41 {
  font-size: calc(41rpx + 6rpx);
}
#page_font_size_max .text_42 {
  font-size: calc(42rpx + 6rpx);
}
#page_font_size_max .text_43 {
  font-size: calc(43rpx + 6rpx);
}
#page_font_size_max .text_44 {
  font-size: calc(44rpx + 6rpx);
}
#page_font_size_max .text_45 {
  font-size: calc(45rpx + 6rpx);
}
#page_font_size_max .text_46 {
  font-size: calc(46rpx + 6rpx);
}
#page_font_size_max .text_47 {
  font-size: calc(47rpx + 6rpx);
}
#page_font_size_max .text_48 {
  font-size: calc(48rpx + 6rpx);
}
#page_font_size_max .text_49 {
  font-size: calc(49rpx + 6rpx);
}
#page_font_size_max .text_50 {
  font-size: calc(50rpx + 6rpx);
}
#page_font_size_max .text_51 {
  font-size: calc(51rpx + 6rpx);
}
#page_font_size_max .text_52 {
  font-size: calc(52rpx + 6rpx);
}
#page_font_size_max .text_53 {
  font-size: calc(53rpx + 6rpx);
}
#page_font_size_max .text_54 {
  font-size: calc(54rpx + 6rpx);
}
#page_font_size_max .text_55 {
  font-size: calc(55rpx + 6rpx);
}
#page_font_size_max .text_56 {
  font-size: calc(56rpx + 6rpx);
}
#page_font_size_max .text_57 {
  font-size: calc(57rpx + 6rpx);
}
#page_font_size_max .text_58 {
  font-size: calc(58rpx + 6rpx);
}
#page_font_size_max .text_59 {
  font-size: calc(59rpx + 6rpx);
}
/* 偏大 */
#page_font_size_max_plus .text_1 {
  font-size: calc(1rpx + 10rpx);
}
#page_font_size_max_plus .text_2 {
  font-size: calc(2rpx + 10rpx);
}
#page_font_size_max_plus .text_3 {
  font-size: calc(3rpx + 10rpx);
}
#page_font_size_max_plus .text_4 {
  font-size: calc(4rpx + 10rpx);
}
#page_font_size_max_plus .text_5 {
  font-size: calc(5rpx + 10rpx);
}
#page_font_size_max_plus .text_6 {
  font-size: calc(6rpx + 10rpx);
}
#page_font_size_max_plus .text_7 {
  font-size: calc(7rpx + 10rpx);
}
#page_font_size_max_plus .text_8 {
  font-size: calc(8rpx + 10rpx);
}
#page_font_size_max_plus .text_9 {
  font-size: calc(9rpx + 10rpx);
}
#page_font_size_max_plus .text_10 {
  font-size: calc(10rpx + 10rpx);
}
#page_font_size_max_plus .text_11 {
  font-size: calc(11rpx + 10rpx);
}
#page_font_size_max_plus .text_12 {
  font-size: calc(12rpx + 10rpx);
}
#page_font_size_max_plus .text_13 {
  font-size: calc(13rpx + 10rpx);
}
#page_font_size_max_plus .text_14 {
  font-size: calc(14rpx + 10rpx);
}
#page_font_size_max_plus .text_15 {
  font-size: calc(15rpx + 10rpx);
}
#page_font_size_max_plus .text_16 {
  font-size: calc(16rpx + 10rpx);
}
#page_font_size_max_plus .text_17 {
  font-size: calc(17rpx + 10rpx);
}
#page_font_size_max_plus .text_18 {
  font-size: calc(18rpx + 10rpx);
}
#page_font_size_max_plus .text_19 {
  font-size: calc(19rpx + 10rpx);
}
#page_font_size_max_plus .text_20 {
  font-size: calc(20rpx + 10rpx);
}
#page_font_size_max_plus .text_21 {
  font-size: calc(21rpx + 10rpx);
}
#page_font_size_max_plus .text_22 {
  font-size: calc(22rpx + 10rpx);
}
#page_font_size_max_plus .text_23 {
  font-size: calc(23rpx + 10rpx);
}
#page_font_size_max_plus .text_24 {
  font-size: calc(24rpx + 10rpx);
}
#page_font_size_max_plus .text_25 {
  font-size: calc(25rpx + 10rpx);
}
#page_font_size_max_plus .text_26 {
  font-size: calc(26rpx + 10rpx);
}
#page_font_size_max_plus .text_27 {
  font-size: calc(27rpx + 10rpx);
}
#page_font_size_max_plus .text_28 {
  font-size: calc(28rpx + 10rpx);
}
#page_font_size_max_plus .text_29 {
  font-size: calc(29rpx + 10rpx);
}
#page_font_size_max_plus .text_30 {
  font-size: calc(30rpx + 10rpx);
}
#page_font_size_max_plus .text_31 {
  font-size: calc(31rpx + 10rpx);
}
#page_font_size_max_plus .text_32 {
  font-size: calc(32rpx + 10rpx);
}
#page_font_size_max_plus .text_33 {
  font-size: calc(33rpx + 10rpx);
}
#page_font_size_max_plus .text_34 {
  font-size: calc(34rpx + 10rpx);
}
#page_font_size_max_plus .text_35 {
  font-size: calc(35rpx + 10rpx);
}
#page_font_size_max_plus .text_36 {
  font-size: calc(36rpx + 10rpx);
}
#page_font_size_max_plus .text_37 {
  font-size: calc(37rpx + 10rpx);
}
#page_font_size_max_plus .text_38 {
  font-size: calc(38rpx + 10rpx);
}
#page_font_size_max_plus .text_39 {
  font-size: calc(39rpx + 10rpx);
}
#page_font_size_max_plus .text_40 {
  font-size: calc(40rpx + 10rpx);
}
#page_font_size_max_plus .text_41 {
  font-size: calc(41rpx + 10rpx);
}
#page_font_size_max_plus .text_42 {
  font-size: calc(42rpx + 10rpx);
}
#page_font_size_max_plus .text_43 {
  font-size: calc(43rpx + 10rpx);
}
#page_font_size_max_plus .text_44 {
  font-size: calc(44rpx + 10rpx);
}
#page_font_size_max_plus .text_45 {
  font-size: calc(45rpx + 10rpx);
}
#page_font_size_max_plus .text_46 {
  font-size: calc(46rpx + 10rpx);
}
#page_font_size_max_plus .text_47 {
  font-size: calc(47rpx + 10rpx);
}
#page_font_size_max_plus .text_48 {
  font-size: calc(48rpx + 10rpx);
}
#page_font_size_max_plus .text_49 {
  font-size: calc(49rpx + 10rpx);
}
#page_font_size_max_plus .text_50 {
  font-size: calc(50rpx + 10rpx);
}
#page_font_size_max_plus .text_51 {
  font-size: calc(51rpx + 10rpx);
}
#page_font_size_max_plus .text_52 {
  font-size: calc(52rpx + 10rpx);
}
#page_font_size_max_plus .text_53 {
  font-size: calc(53rpx + 10rpx);
}
#page_font_size_max_plus .text_54 {
  font-size: calc(54rpx + 10rpx);
}
#page_font_size_max_plus .text_55 {
  font-size: calc(55rpx + 10rpx);
}
#page_font_size_max_plus .text_56 {
  font-size: calc(56rpx + 10rpx);
}
#page_font_size_max_plus .text_57 {
  font-size: calc(57rpx + 10rpx);
}
#page_font_size_max_plus .text_58 {
  font-size: calc(58rpx + 10rpx);
}
#page_font_size_max_plus .text_59 {
  font-size: calc(59rpx + 10rpx);
}
/* 特大 */
#page_font_size_max_plus_pro .text_1 {
  font-size: calc(1rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_2 {
  font-size: calc(2rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_3 {
  font-size: calc(3rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_4 {
  font-size: calc(4rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_5 {
  font-size: calc(5rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_6 {
  font-size: calc(6rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_7 {
  font-size: calc(7rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_8 {
  font-size: calc(8rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_9 {
  font-size: calc(9rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_10 {
  font-size: calc(10rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_11 {
  font-size: calc(11rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_12 {
  font-size: calc(12rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_13 {
  font-size: calc(13rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_14 {
  font-size: calc(14rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_15 {
  font-size: calc(15rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_16 {
  font-size: calc(16rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_17 {
  font-size: calc(17rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_18 {
  font-size: calc(18rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_19 {
  font-size: calc(19rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_20 {
  font-size: calc(20rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_21 {
  font-size: calc(21rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_22 {
  font-size: calc(22rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_23 {
  font-size: calc(23rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_24 {
  font-size: calc(24rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_25 {
  font-size: calc(25rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_26 {
  font-size: calc(26rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_27 {
  font-size: calc(27rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_28 {
  font-size: calc(28rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_29 {
  font-size: calc(29rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_30 {
  font-size: calc(30rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_31 {
  font-size: calc(31rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_32 {
  font-size: calc(32rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_33 {
  font-size: calc(33rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_34 {
  font-size: calc(34rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_35 {
  font-size: calc(35rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_36 {
  font-size: calc(36rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_37 {
  font-size: calc(37rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_38 {
  font-size: calc(38rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_39 {
  font-size: calc(39rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_40 {
  font-size: calc(40rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_41 {
  font-size: calc(41rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_42 {
  font-size: calc(42rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_43 {
  font-size: calc(43rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_44 {
  font-size: calc(44rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_45 {
  font-size: calc(45rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_46 {
  font-size: calc(46rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_47 {
  font-size: calc(47rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_48 {
  font-size: calc(48rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_49 {
  font-size: calc(49rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_50 {
  font-size: calc(50rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_51 {
  font-size: calc(51rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_52 {
  font-size: calc(52rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_53 {
  font-size: calc(53rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_54 {
  font-size: calc(54rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_55 {
  font-size: calc(55rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_56 {
  font-size: calc(56rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_57 {
  font-size: calc(57rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_58 {
  font-size: calc(58rpx + 16rpx);
}
#page_font_size_max_plus_pro .text_59 {
  font-size: calc(59rpx + 16rpx);
}
.flex_r {
  display: flex;
  flex-direction: row;
}
.flex_c {
  display: flex;
  flex-direction: column;
}
.flex1 {
  flex: 1;
}
.fj_b {
  justify-content: space-between;
}
.fj_a {
  justify-content: space-around;
}
.fj_c {
  justify-content: center;
}
.fa_c {
  align-items: center;
}
.flex_c_c {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.icon_ {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.z_index2 {
  position: relative;
  z-index: 2;
}
.img {
  width: 100%;
  height: 100%;
}
.rotate_45 {
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}
.rotate_90 {
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
}
.rotate_180 {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.text-indent {
  text-indent: 2em;
  text-align: justify;
}
/* 文本css */
.nowrap_ {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.ellipsis_2 {
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.hover_class::after {
  position: absolute;
  z-index: 2;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
}
.size_white {
  color: #fff;
}
.color_ {
  color: #b5b5b6;
}
.color___ {
  color: #7fade8;
}
.color__ {
  color: #7e7e7e;
}
.color_4a {
  color: #4a4a4a;
}
.color_rot {
  color: #ff0000;
}
.bold_ {
  font-weight: bold;
}
/*每个页面公共css */
