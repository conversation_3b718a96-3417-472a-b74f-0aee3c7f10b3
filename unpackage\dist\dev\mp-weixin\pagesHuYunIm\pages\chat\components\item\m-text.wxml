<view class="flex_c row data-v-b1219e00"><view data-event-opts="{{[['tap',[['onClick',['$event']]]]]}}" class="{{['flex_r','text-box','data-v-b1219e00',(isMy)?'text_box':'']}}" catchtap="__e"><view class="{{['text_32','text','data-v-b1219e00',isMy?'text_r':'text_l']}}" style="{{'white-space:'+('pre-wrap')+';'}}" hover-class="{{isMy?'hover_classr':'hover_classl'}}" hover-stay-time="{{60}}"><rich-text nodes="{{renderTextMessage}}"></rich-text></view></view><block wx:if="{{value.type==='text_quote'}}"><view class="{{['text_26','flex_r','data-v-b1219e00',(isMy)?'row_':'']}}"><block wx:if="{{value.payload.quoteSource.type==='image'||value.payload.quoteSource.type==='image_transmit'}}"><view class="data-v-b1219e00"><m-image vue-id="46777006-1" value="{{value.payload.quoteSource}}" class="data-v-b1219e00" bind:__l="__l"></m-image></view></block><block wx:else><block wx:if="{{value.payload.quoteSource.type==='audio'||value.payload.quoteSource.type==='audio_quote'}}"><view class="data-v-b1219e00"><m-audio vue-id="46777006-2" value="{{value.payload.quoteSource}}" class="data-v-b1219e00" bind:__l="__l"></m-audio></view></block><block wx:else><block wx:if="{{value.payload.quoteSource.type==='text'||value.payload.quoteSource.type==='text_quote'}}"><view class="data-v-b1219e00"><m-text vue-id="46777006-3" value="{{value.payload.quoteSource}}" class="data-v-b1219e00" bind:__l="__l"></m-text></view></block><block wx:else><view class="data-v-b1219e00"><m-other vue-id="46777006-4" value="{{value.payload.quoteSource}}" class="data-v-b1219e00" bind:__l="__l"></m-other></view></block></block></block></view></block></view>