{"version": 3, "sources": ["webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/m-redPacket.vue?0544", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/m-redPacket.vue?b264", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/m-redPacket.vue?7ae7", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/m-redPacket.vue?f54e", "uni-app:///pagesHuYunIm/pages/chat/components/item/m-redPacket.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/m-redPacket.vue?74ba", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/m-redPacket.vue?dfc6"], "names": ["props", "myid", "type", "default", "isMy", "value", "created", "data", "item", "pageObj", "watch", "handler", "immediate", "methods", "onClick", "infr", "res", "API_red_view", "http", "message_id"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACiM;AACjM,gBAAgB,yLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA8vB,CAAgB,irBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACoElxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;EACA;EACAG;IACA;IACA;EAAA,CACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAL;MACAM;QACA;QACA;MACA;;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACA;kBACAT;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAU;MACA;QACAC,SACA,kBACA;UACAC;QACA,GACA,MACA;UACA;UACA;QACA,EACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AClIA;AAAA;AAAA;AAAA;AAAy7C,CAAgB,wuCAAG,EAAC,C;;;;;;;;;;;ACA78C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/pages/chat/components/item/m-redPacket.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./m-redPacket.vue?vue&type=template&id=339653b6&scoped=true&\"\nvar renderjs\nimport script from \"./m-redPacket.vue?vue&type=script&lang=js&\"\nexport * from \"./m-redPacket.vue?vue&type=script&lang=js&\"\nimport style0 from \"./m-redPacket.vue?vue&type=style&index=0&id=339653b6&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"339653b6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/chat/components/item/m-redPacket.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-redPacket.vue?vue&type=template&id=339653b6&scoped=true&\"", "var components\ntry {\n  components = {\n    mLine: function () {\n      return import(\n        /* webpackChunkName: \"components/m-line/m-line\" */ \"@/components/m-line/m-line.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-redPacket.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-redPacket.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"flex_r flex_c text-box\">\n\t\t<view>\n\t\t\t<view class=\"text_30 size_white text\" :class=\"[{ text_: item.had_draw || item.isClick }, { text_r: isMy }, { text_l: !isMy }]\" @tap.stop=\"onClick\">\n\t\t\t\t<!-- <view class=\"red_packet_bg_mini\">\n\t\t\t\t\t<image class=\"img\" :src=\"item.payload.red_packet_bg_mini\" mode=\"aspectFill\"></image>\n\t\t\t\t</view> -->\n\t\t\t\t<view class=\"flex_r z_index2 redPacket-row\">\n\t\t\t\t\t<view class=\"redPacket-icon\">\n\t\t\t\t\t\t<!-- 开启 -->\n\t\t\t\t\t\t<image\n\t\t\t\t\t\t\tclass=\"img\"\n\t\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTkxNS4wNzkgNTcwLjIwN3YtNDQ4LjdjMC0xMC4yMTQtNS41NTItMTkuNzUzLTE0LjU1Mi0yNC41NUE4MjUuMzk3IDgyNS4zOTcgMCAwIDAgNTExLjQzNC4wMDFhODI1LjI5IDgyNS4yOSAwIDAgMC0zODkuMDY2IDk2Ljk1NiAyNy43ODMgMjcuNzgzIDAgMCAwLTE0LjU3OCAyNC41NXY0NDguN2MwIDE0Ljg3NCAxMi4wNDUgMjYuOTIgMjYuOTIgMjYuOTJoNzUzLjQ0OGEyNi45MiAyNi45MiAwIDAgMCAyNi45Mi0yNi45MiIgZmlsbD0iI2ZmZjNlNyIgZGF0YS1zcG0tYW5jaG9yLWlkPSJhMzEzeC5zZWFyY2hfaW5kZXguMC5pNS41ZmM4M2E4MVJVTGFkOSIvPjxwYXRoIGQ9Ik0xMDcuNzkgMzQ3LjYyMXY2NDguMDNjMCAxNC44NzUgMTIuMDQ1IDI2Ljk0OCAyNi45MiAyNi45NDhoNzUzLjQ0OGEyNi45MiAyNi45MiAwIDAgMCAyNi45Mi0yNi45NDh2LTY0OC4wM2E4MjUuMzE3IDgyNS4zMTcgMCAwIDEtNDAzLjY0NCAxMDQuOTA2QTgyNS4zMTcgODI1LjMxNyAwIDAgMSAxMDcuNzkgMzQ3LjYyMSIgZmlsbD0iI2VmN2I2NCIgZGF0YS1zcG0tYW5jaG9yLWlkPSJhMzEzeC5zZWFyY2hfaW5kZXguMC5pNi41ZmM4M2E4MVJVTGFkOSIvPjxwYXRoIGQ9Ik0zNzUuNjQ2IDYxMC41NzRhMTM0LjUyMSAxMzQuNTIxIDAgMSAwIDI2OS4xMjQgMCAxMzQuNTQ4IDEzNC41NDggMCAwIDAtMjY5LjA5NyAwIiBmaWxsPSIjZjJkMzljIiBkYXRhLXNwbS1hbmNob3ItaWQ9ImEzMTN4LnNlYXJjaF9pbmRleC4wLmk5LjVmYzgzYTgxUlVMYWQ5Ii8+PHBhdGggZD0iTTU2Ni4yOTkgNjEzLjg4OHYtMjEuODgxaC0zNC44NDNsMzEuMjMyLTMxLjU4Mi0yMC41MzQtMTguODEtMzIuMDEzIDI5LjIxMS0zMi4wNjgtMjkuMjEtMjAuNTA3IDE4Ljc1NSAzMS4yNiAzMS42MDloLTM0LjY4MnYyMS44MjdoNDAuMDQ0djE0LjU3OWgtNDAuMDQ0djI5LjE4NGg0MC4wNDR2MjEuOTM1aDIzLjk4M3YtMjkuMTg0aDQ4LjA3NFY2MjguNDRoLTQ4LjA0N3YtMTQuNTUyeiIgZmlsbD0iI2ViYjM3NyIgZGF0YS1zcG0tYW5jaG9yLWlkPSJhMzEzeC5zZWFyY2hfaW5kZXguMC5pMTEuNWZjODNhODFSVUxhZDkiIGNsYXNzPSJzZWxlY3RlZCIvPjwvc3ZnPg==\"\n\t\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t\tv-if=\"item.had_draw || item.isClick\"\n\t\t\t\t\t\t></image>\n\n\t\t\t\t\t\t<!-- 未开启 -->\n\t\t\t\t\t\t<image\n\t\t\t\t\t\t\tclass=\"img\"\n\t\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTg3OC41OTIgMTAyNEgxNDUuNDA4Yy0yNC4wNjQgMC00My41Mi0xOS40NTYtNDMuNTItNDMuNTJWNDMuNTJjMC0yNC4wNjQgMTkuNDU2LTQzLjUyIDQzLjUyLTQzLjUyaDczMy4xODRjMjQuMDY0IDAgNDMuNTIgMTkuNDU2IDQzLjUyIDQzLjUydjkzNi40NDhjMCAyNC41NzYtMTkuNDU2IDQ0LjAzMi00My41MiA0NC4wMzJ6IiBmaWxsPSIjREY0OTQ5Ii8+PHBhdGggZD0iTTg3OC41OTIgMEgxNDUuNDA4Yy0yNC4wNjQgMC00NC4wMzIgMTkuNDU2LTQ0LjAzMiA0My41MnYzNzMuMjQ4QzIxMC45NDQgNDUzLjEyIDMzNS44NzIgNDczLjYgNDY4Ljk5MiA0NzMuNmMxNjguOTYgMCAzMjUuNjMyLTMzLjI4IDQ1My4xMi05MC4xMTJWNDMuNTJjMC0yNC4wNjQtMTkuNDU2LTQzLjUyLTQzLjUyLTQzLjUyeiIgZmlsbD0iI0ZCNTM1MiIvPjxwYXRoIGQ9Ik0zNzUuMjk2IDQ4OS45ODRjMCA3NS4yNjQgNjEuNDQgMTM2LjcwNCAxMzYuNzA0IDEzNi43MDRzMTM2LjcwNC02MS40NCAxMzYuNzA0LTEzNi43MDRTNTg3LjI2NCAzNTMuMjggNTEyIDM1My4yOHMtMTM2LjcwNCA2MS40NC0xMzYuNzA0IDEzNi43MDR6IiBmaWxsPSIjRkNDRTNFIi8+PHBhdGggZD0iTTU2MS42NjQgNDgzLjg0aDMuMDcydi0uNTEyYzQuMDk2LTEuNTM2IDcuMTY4LTUuMTIgNy4xNjgtOS43MjggMC01LjYzMi00LjYwOC0xMC4yNC0xMC4yNC0xMC4yNEg1MjIuMjR2LS41MTJsNDEuNDcyLTQwLjQ0OCAyLjA0OC0yLjA0OGMxLjAyNC0xLjUzNiAxLjUzNi0zLjA3MiAxLjUzNi00LjYwOCAwLTIuNTYtLjUxMi01LjEyLTIuNTYtNy42OC0zLjU4NC00LjA5Ni0xMC4yNC00LjYwOC0xNC4zMzYtLjUxMmwtMzcuODg4IDM3LjM3Ni0zOS45MzYtMzguOTEyYy00LjA5Ni0zLjU4NC0xMC43NTItMy4wNzItMTQuMzM2IDEuMDI0LTMuMDcyIDMuNTg0LTMuNTg0IDguMTkyLTEuMDI0IDEyLjI4OHYuNTEybDQ1LjA1NiA0NC4wMzJ2MS4wMjRoLTQyLjQ5NnYuNTEyYy00LjA5NiAxLjUzNi03LjE2OCA1LjEyLTcuMTY4IDkuNzI4czMuMDcyIDguMTkyIDcuMTY4IDkuNzI4di41MTJoNDEuOTg0djMxLjIzMmgtNDMuMDA4di41MTJjLTQuMDk2IDEuNTM2LTcuMTY4IDUuMTItNy4xNjggOS43MjhzMy4wNzIgOC4xOTIgNy4xNjggOS43Mjh2LjUxMmg0My4wMDh2MzQuMzA0aC41MTJjMS41MzYgNC4wOTYgNS4xMiA3LjE2OCA5LjcyOCA3LjE2OHM4LjE5Mi0zLjA3MiA5LjcyOC03LjE2OGguNTEydi0zNC4zMDRoNDIuNDk2di0uNTEyYzQuMDk2LTEuNTM2IDcuMTY4LTUuMTIgNy4xNjgtOS43MjggMC01LjYzMi00LjYwOC0xMC4yNC0xMC4yNC0xMC4yNEg1MjIuMjR2LTMxLjIzMmwzOS40MjQtMS41MzZ6IiBmaWxsPSIjRDg4NjE5Ii8+PC9zdmc+\"\n\t\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t\tv-else\n\t\t\t\t\t\t></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex1 flex_c fj_a z_index2 nowrap_ redPacket-title\">\n\t\t\t\t\t\t<view class=\"nowrap_ text_34 bold_\">{{ item.payload.remark }}</view>\n\t\t\t\t\t\t<view class=\"text_24\" style=\"margin-top: 6rpx\" v-if=\"item.had_draw && item.payload.type !== 'exclusive'\">已领取</view>\n\n\t\t\t\t\t\t<view class=\"text_24\" style=\"margin-top: 6rpx\" v-if=\"item.payload.type === 'exclusive'\">\n\t\t\t\t\t\t\t{{ item.payload.exclusive.name || item.payload.exclusive.realname }}\n\t\t\t\t\t\t\t{{ item.had_draw ? '已领取' : '的专属红包' }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"m-line\">\n\t\t\t\t\t<m-line color=\"rgba(255, 255, 255, 0.4)\" width=\"100%\" margin=\"20rpx auto 6rpx auto\" :hairline=\"true\"></m-line>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"text_24 redPacket-text\">{{ item.payload.account_type_text }}</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 你的专属红包 -->\n\t\t<view class=\"text_22 color__ flex_r fa_c exclusive\" v-if=\"item.payload.type === 'exclusive' && item.payload.exclusive.member_id === myid\">\n\t\t\t这是你的专属红包\n\t\t\t<view class=\"label-icon\">\n\t\t\t\t<image\n\t\t\t\t\tclass=\"img\"\n\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTAgMGgxMDI0djEwMjRIMHoiIGZpbGw9Im5vbmUiLz48cGF0aCBkPSJNOTg1LjYgMzUyYzAtNDAuMzItMzItNzIuMzItNzEuMDQtNzIuMzItMzkuNjggMC03MS4wNCAzMi42NC03MS4wNCA3Mi4zMiAwIDE1LjM2IDQuNDggMjkuNDQgMTIuOCA0MS42LTQzLjUyIDc0LjI0LTc0Ljg4IDE3NC43Mi0xMzUuNjggMTc0LjcyLTkuNiAwLTE5LjItLjY0LTI4LjE2LTIuNTYtOTYtMTcuMjgtMTQ3LjItMjUzLjQ0LTE1Mi4zMi0yODYuNzIgMjMuMDQtMTIuMTYgMzkuNjgtMzcuMTIgMzkuNjgtNjUuOTIgMC00MC4zMi0zMi03Mi4zMi03MS4wNC03Mi4zMmE3Mi41MTIgNzIuNTEyIDAgMCAwLTI0Ljk2IDE0MC4xNmMtNS4xMiAzNy4xMi01My43NiAyNzYuNDgtMTQ3LjIgMjg5LjkyLTcuNjggMS4yOC0xNS4zNiAxLjkyLTIzLjY4IDEuOTItNjAuMTYgMC0xMDIuNC0xMDQuOTYtMTM3LjYtMTc0LjcyIDMuODQtOC4zMiA1Ljc2LTE3LjkyIDUuNzYtMjguMTYgMC00MC4zMi0zMi03Mi4zMi03MS4wNC03Mi4zMi0xOS4yIDAtMzYuNDggNy42OC00OS4yOCAyMC40OC0xNC4wOCAxMy40NC0yMi40IDMyLTIyLjQgNTEuODQgMCA0MC4zMiAzMiA3Mi4zMiA3MS4wNCA3Mi4zMiAxMjQuMTYgMzEwLjQgMTg1LjYgNDY1LjkyIDE4NS42IDQ2NS45MlM0MDEuOTIgOTYwIDUxMS4zNiA5NjBzMjE3LjYtNTEuODQgMjE3LjYtNTEuODQgNjQuNjQtMTYxLjI4IDE5My4yOC00ODQuNDhjMzUuMi0zLjg0IDYzLjM2LTM0LjU2IDYzLjM2LTcxLjY4eiIgZmlsbD0iI0ZCRDMwRiIvPjxwYXRoIGQ9Ik0yOTQuNCA5MDQuMzJjMCAyOS40NCAxMDYuODggNTkuNTIgMjE1LjA0IDU5LjUyUzcyOS42IDkzNC40IDcyOS42IDkwNC4zMmMwLTI5LjQ0LTExMi42NC01OS41Mi0yMjAuMTYtNTkuNTJTMjk0LjQgODc0LjI0IDI5NC40IDkwNC4zMnoiIGZpbGw9IiNGRkE3MDYiLz48L3N2Zz4=\"\n\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t></image>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"icon_ redPacket\" v-if=\"item.had_draw\">\n\t\t\t<view class=\"redPacket-icon\">\n\t\t\t\t<image\n\t\t\t\t\tclass=\"img\"\n\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTg3OC41OTIgMTAyNEgxNDUuNDA4Yy0yNC4wNjQgMC00My41Mi0xOS40NTYtNDMuNTItNDMuNTJWNDMuNTJjMC0yNC4wNjQgMTkuNDU2LTQzLjUyIDQzLjUyLTQzLjUyaDczMy4xODRjMjQuMDY0IDAgNDMuNTIgMTkuNDU2IDQzLjUyIDQzLjUydjkzNi40NDhjMCAyNC41NzYtMTkuNDU2IDQ0LjAzMi00My41MiA0NC4wMzJ6IiBmaWxsPSIjREY0OTQ5Ii8+PHBhdGggZD0iTTg3OC41OTIgMEgxNDUuNDA4Yy0yNC4wNjQgMC00NC4wMzIgMTkuNDU2LTQ0LjAzMiA0My41MnYzNzMuMjQ4QzIxMC45NDQgNDUzLjEyIDMzNS44NzIgNDczLjYgNDY4Ljk5MiA0NzMuNmMxNjguOTYgMCAzMjUuNjMyLTMzLjI4IDQ1My4xMi05MC4xMTJWNDMuNTJjMC0yNC4wNjQtMTkuNDU2LTQzLjUyLTQzLjUyLTQzLjUyeiIgZmlsbD0iI0ZCNTM1MiIvPjxwYXRoIGQ9Ik0zNzUuMjk2IDQ4OS45ODRjMCA3NS4yNjQgNjEuNDQgMTM2LjcwNCAxMzYuNzA0IDEzNi43MDRzMTM2LjcwNC02MS40NCAxMzYuNzA0LTEzNi43MDRTNTg3LjI2NCAzNTMuMjggNTEyIDM1My4yOHMtMTM2LjcwNCA2MS40NC0xMzYuNzA0IDEzNi43MDR6IiBmaWxsPSIjRkNDRTNFIi8+PHBhdGggZD0iTTU2MS42NjQgNDgzLjg0aDMuMDcydi0uNTEyYzQuMDk2LTEuNTM2IDcuMTY4LTUuMTIgNy4xNjgtOS43MjggMC01LjYzMi00LjYwOC0xMC4yNC0xMC4yNC0xMC4yNEg1MjIuMjR2LS41MTJsNDEuNDcyLTQwLjQ0OCAyLjA0OC0yLjA0OGMxLjAyNC0xLjUzNiAxLjUzNi0zLjA3MiAxLjUzNi00LjYwOCAwLTIuNTYtLjUxMi01LjEyLTIuNTYtNy42OC0zLjU4NC00LjA5Ni0xMC4yNC00LjYwOC0xNC4zMzYtLjUxMmwtMzcuODg4IDM3LjM3Ni0zOS45MzYtMzguOTEyYy00LjA5Ni0zLjU4NC0xMC43NTItMy4wNzItMTQuMzM2IDEuMDI0LTMuMDcyIDMuNTg0LTMuNTg0IDguMTkyLTEuMDI0IDEyLjI4OHYuNTEybDQ1LjA1NiA0NC4wMzJ2MS4wMjRoLTQyLjQ5NnYuNTEyYy00LjA5NiAxLjUzNi03LjE2OCA1LjEyLTcuMTY4IDkuNzI4czMuMDcyIDguMTkyIDcuMTY4IDkuNzI4di41MTJoNDEuOTg0djMxLjIzMmgtNDMuMDA4di41MTJjLTQuMDk2IDEuNTM2LTcuMTY4IDUuMTItNy4xNjggOS43MjhzMy4wNzIgOC4xOTIgNy4xNjggOS43Mjh2LjUxMmg0My4wMDh2MzQuMzA0aC41MTJjMS41MzYgNC4wOTYgNS4xMiA3LjE2OCA5LjcyOCA3LjE2OHM4LjE5Mi0zLjA3MiA5LjcyOC03LjE2OGguNTEydi0zNC4zMDRoNDIuNDk2di0uNTEyYzQuMDk2LTEuNTM2IDcuMTY4LTUuMTIgNy4xNjgtOS43MjggMC01LjYzMi00LjYwOC0xMC4yNC0xMC4yNC0xMC4yNEg1MjIuMjR2LTMxLjIzMmwzOS40MjQtMS41MzZ6IiBmaWxsPSIjRDg4NjE5Ii8+PC9zdmc+\"\n\t\t\t\t\tmode=\"heightFix\"\n\t\t\t\t></image>\n\t\t\t</view>\n\t\t\t<view class=\"text_24 color__\" v-if=\"item.senderData.name\">你领取了{{ item.senderData.name }}的红包</view>\r\n\t\t\t<view class=\"text_24 color__\" v-else>你领取了红包</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { show } from '@/utils/index.js';\nexport default {\n\tprops: {\n\t\tmyid: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: null\n\t\t},\n\t\tisMy: {\n\t\t\ttype: [Boolean, Number],\n\t\t\tdefault: false\n\t\t},\n\t\tvalue: {\n\t\t\ttype: Object,\n\t\t\tdefault: {}\n\t\t}\n\t},\n\tcreated() {\n\t\t// console.log(this.value);\n\t\t// this.infr();\n\t},\n\tdata() {\n\t\treturn {\n\t\t\titem: {},\n\t\t\tpageObj: {}\n\t\t};\n\t},\n\twatch: {\n\t\tvalue: {\n\t\t\thandler: function (newV) {\n\t\t\t\tthis.item = newV;\n\t\t\t\t// this.infr();\n\t\t\t},\n\t\t\timmediate: true\n\t\t}\n\t},\n\tmethods: {\n\t\tonClick() {\n\t\t\tthis.$emit('onClick');\n\t\t},\n\t\tasync infr() {\n\t\t\tconst res = await this.API_red_view(this.value.payload.message_id);\n\t\t\tif (res) {\n\t\t\t\tconst data = res.data.data;\n\t\t\t\tthis.item.had_draw = data.had_draw;\n\t\t\t}\n\t\t},\n\t\tAPI_red_view(message_id) {\n\t\t\treturn new Promise((res) => {\n\t\t\t\thttp.get(\n\t\t\t\t\t'Group/red_view',\n\t\t\t\t\t{\n\t\t\t\t\t\tmessage_id\n\t\t\t\t\t},\n\t\t\t\t\ttrue,\n\t\t\t\t\t(r) => {\n\t\t\t\t\t\tif (r.data.code == 0) return res(r);\n\t\t\t\t\t\treturn res(false);\n\t\t\t\t\t}\n\t\t\t\t);\n\t\t\t});\n\t\t}\n\t}\n};\n</script>\n\n<style scoped lang=\"scss\">\n.text {\n\tposition: relative;\n\tz-index: 99;\n\tbox-sizing: border-box;\n\twidth: 490rpx;\n\tmin-height: 180rpx;\n\tpadding: 22rpx 30rpx 0 30rpx;\n\tborder-radius: 10rpx;\n\tbackground-color: #fa9e3b;\n\t.red_packet_bg_mini {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tz-index: 0;\n\t\toverflow: hidden;\n\t\topacity: 0.2;\n\t\tborder-radius: 10rpx;\n\t}\n\n\t.redPacket-row {\n\t\twidth: 100%;\n\t\tmin-height: 94rpx;\n\t\t.redPacket-icon {\n\t\t\twidth: 76rpx;\n\t\t\theight: 88rpx;\n\t\t\tborder-radius: 10rpx;\n\t\t\toverflow: hidden;\n\t\t\tmargin-right: 26rpx;\n\t\t}\n\t\t.redPacket-title {\n\t\t}\n\t}\n\t.m-line {\n\t}\n\t.redPacket-text {\n\t\twidth: 100%;\n\t\tmin-height: 30rpx;\n\t\tpadding: 4rpx 0 6rpx 0;\n\t\tfont-weight: 300;\n\t}\n}\n\n.text_r {\n\tposition: relative;\n}\n.text_l {\n\tposition: relative;\n}\n\n.text_r::after {\n\tposition: absolute;\n\tz-index: -1;\n\tcontent: '';\n\ttop: 26rpx;\n\tright: -8rpx;\n\twidth: 18rpx;\n\theight: 18rpx;\n\tborder-radius: 2px;\n\ttransform: rotate(45deg);\n\tbackground-color: #fa9e3b;\n}\n.text_l::after {\n\tposition: absolute;\n\tz-index: -1;\n\tcontent: '';\n\ttop: 26rpx;\n\tleft: -8rpx;\n\twidth: 18rpx;\n\theight: 18rpx;\n\tborder-radius: 2px;\n\ttransform: rotate(45deg);\n\tbackground-color: #fa9e3b;\n}\n.text_ {\n\tbackground-color: #fde2c4;\n}\n\n.text_::after {\n\tbackground-color: #fde2c4;\n}\n\n.text-box {\n}\n\n.exclusive {\n\twidth: 100%;\n\tflex-direction: row-reverse;\n\tmargin-top: 2rpx;\n\t.label-icon {\n\t\twidth: 34rpx;\n\t\theight: 34rpx;\n\t\tmargin-right: 10rpx;\n\t}\n}\n\n.redPacket {\n\twidth: 100%;\n\theight: 80rpx;\n\n\t.redPacket-icon {\n\t\theight: 34rpx;\n\t\tborder-radius: 4px;\n\t\tmargin: 0 12rpx 0 0;\n\t\toverflow: hidden;\n\t}\n}\n</style>\n", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-redPacket.vue?vue&type=style&index=0&id=339653b6&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-redPacket.vue?vue&type=style&index=0&id=339653b6&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755067560171\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}