<template>
	<view class="page">
		<m-return></m-return>
		<view class="search">
			<view class="search_input">
				<input
					type="number"
					class=""
					v-model="keyword"
					placeholder="ID号或者手机号码"
					@confirm="search_member"
				/>
				<image
					src="/pagesGoEasy/static/icon/search_member.png"
					mode=""
					class="search_member"
				></image>
			</view>
		</view>
		<view class="search_members">
			<view class="container">
				<view class="members">
					<view class="member_item" v-for="(item, index) in group_members" :key="index">
						<image
							:src="item.member_info.member_avatar"
							mode=""
							class="member_img fl"
						></image>
						<view class="member_item_l fl">
							<view class="member_info fl">
								<view class="member_name">
									{{
										item.member_info.realname
											? item.member_info.realname
											: item.member_info.member_name
									}}
								</view>
								<view class="member_id">ID: {{ item.member_id }}</view>
							</view>
						</view>
						<view
							class="member_item_r"
							@click="choose_member(item, index)"
							v-if="group_master != item.member_id"
						>
							<block v-if="item.choose_status == '0'">
								<image
									src="/pagesGoEasy/static/icon/choose_member.png"
									mode=""
									class="choose_member"
								></image>
							</block>
							<block v-else>
								<image
									src="/pagesGoEasy/static/icon/choose_member_icon.png"
									mode=""
									class="choose_member"
								></image>
							</block>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="empty_view"></view>
		<view class="footer">
			<view
				class="choose_member_btn"
				:class="is_choose == 0 ? 'choose_member_btn_no_choose ' : ''"
				@click="kick"
			>
				移出群聊
			</view>
		</view>
		<view class="m-loading" v-if="bIsShowLoading">
			<text>加载中</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			group_members: [],
			group_id: 0,
			bIsShowLoading: false,
			keyword: '',
			is_choose: 0,
			kick_members: [],
			pageCount: 1,
			page: 1,
			group_master: 0,
		}
	},
	onLoad(options) {
		let _this = this
		_this.group_id = options.group_id
		this.init()
	},
	//上滑加载
	onReachBottom() {
		this.page++
		this.groupMember('add')
	},
	watch: {
		keyword(value) {
			if (value == '') {
				this.init()
			}
		},
	},
	methods: {
		kick() {
			if (this.kick_members.length > 0) {
				let _this = this
				uni.showModal({
					title: '提示',
					content: '确定要移除用户出群吗？',
					success: function (res) {
						if (res.confirm) {
							_this.bIsShowLoading = true
							var group_members = _this.group_members
							henglang.get(
								'Group/kick',
								{ group_id: _this.group_id, member_id: _this.kick_members },
								false,
								function (res) {
									if (res.data.code == '0') {
										// 把踢出去的用户从原数组中删除
										group_members = group_members.filter((item) => {
											return (
												_this.kick_members.indexOf(item.member_id) == '-1'
											)
										})
										_this.group_members = group_members
									} else {
										henglang.showToast(res.data.msg, 1500)
									}
									_this.bIsShowLoading = false
								},
							)
						}
					},
				})
			}
		},
		init() {
			this.page = this.pageCount = 1
			this.groupMember()
		},
		search_member() {
			this.init()
		},
		groupMember(type = 'normal') {
			let _this = this
			if (_this.page <= _this.pageCount) {
				_this.bIsShowLoading = true
				henglang.get(
					'Group/groupMember2',
					{ group_id: this.group_id, page: _this.page, keywords: this.keyword },
					false,
					function (res) {
						_this.pageCount = res.data.total_pages
						_this.group_master = res.data.group_master
						res.data.data.forEach((item) => {
							item.choose_status = 0
							let key = _this.kick_members.indexOf(item.member_id)
							if (key != '-1') {
								item.choose_status = 1
							}
						})
						if (type == 'normal') {
							_this.group_members = res.data.data
						} else {
							_this.group_members = _this.group_members.concat(res.data.data)
						}
						_this.bIsShowLoading = false
						uni.stopPullDownRefresh()
					},
				)
			}
		},
		choose_member(item, index) {
			let choose_status = this.group_members[index].choose_status
			if (choose_status == 1) {
				let key = this.kick_members.indexOf(item.member_id)
				this.kick_members.splice(key, 1)
			} else {
				this.kick_members.push(item.member_id)
			}
			this.is_choose = this.kick_members.length > 0 ? 1 : 0
			this.group_members[index].choose_status = choose_status == 1 ? 0 : 1
		},
	},
}
</script>

<style>
.page {
	height: 100%;
	width: 100%;
}
.container {
	width: 690rpx;
	height: 100%;
	margin: 0 auto;
}
.fl {
	float: left;
}
.search {
	height: 99rpx;
	width: 100%;
}
.search_input {
	width: 690rpx;
	margin: 0 auto;
	height: 68rpx;
	background-color: #f4f4f4;
	color: #4d4d4d;
	border-radius: 50rpx;
	margin-top: 32rpx;
	overflow: hidden;
	position: relative;
}
.search input {
	width: 100%;
	height: 100%;
	font-size: 34rpx;
	padding-left: 34rpx;
}
.search_members {
	height: 100%;
	width: 100%;
}
.members {
	margin-top: 30rpx;
	height: 100%;
	overflow-y: auto;
}
.member_item {
	height: 100rpx;
	width: 100%;
	border-bottom: 1rpx solid #eee;
	padding-top: 16rpx;
}
.member_img {
	width: 80rpx;
	height: 80rpx;
	border-radius: 10rpx;
	margin-right: 30rpx;
}
.member_item_l {
	width: 460rpx;
	height: 100%;
	overflow: hidden;
}
.member_info {
	width: 300rpx;
	height: 100%;
	overflow: hidden;
}
.member_info view {
	height: 40rpx;
	line-height: 40rpx;
}
.member_info .member_name {
	font-size: 34rpx;
}
.member_info .member_id {
	font-size: 28rpx;
	color: #999;
	line-height: 56rpx;
}
.member_item_r {
	float: right;
	width: 120rpx;
	height: 100%;
	text-align: right;
}
.choose_member {
	width: 56rpx;
	height: 56rpx;
	margin-top: 20rpx;
	margin-right: 10rpx;
}
.footer {
	height: 138rpx;
	width: 100%;
	background-color: #fff;
	position: fixed;
	bottom: 0;
	border-top: 1rpx solid #eee;
}
.choose_member_btn {
	width: 690rpx;
	height: 88rpx;
	margin: 0 auto;
	background: linear-gradient(to right, #50c0bb, #5dd7d2);
	border-radius: 8rpx;
	line-height: 88rpx;
	text-align: center;
	color: #fff;
	font-size: 34rpx;
	margin-top: 28rpx;
}
.choose_member_btn_no_choose {
	background: #ccc !important;
}
.empty_view {
	width: 690rpx;
	height: 160rpx;
}
</style>
