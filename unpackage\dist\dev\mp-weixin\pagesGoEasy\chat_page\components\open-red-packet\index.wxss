@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.popup-box.data-v-6471714e {
  position: relative;
  margin-bottom: 200rpx;
}
.popup-box .close.data-v-6471714e {
  position: absolute;
  bottom: -130rpx;
  left: calc(50% - 35rpx);
  width: 70rpx;
  height: 70rpx;
}
.popup.data-v-6471714e {
  width: 580rpx;
  height: 1000rpx;
  border-radius: 20rpx;
  overflow: hidden;
  background-color: #f35d4c;
}
.popup .popup-top.data-v-6471714e {
  position: relative;
  width: 100%;
  height: 800rpx;
  border-radius: 20rpx;
}
.popup .popup-top .info.data-v-6471714e {
  margin: 0 auto;
  width: calc(100% - 40rpx);
}
.popup .popup-top .info .info-img.data-v-6471714e {
  width: 40rpx;
  height: 40rpx;
  background-color: #fff;
  margin-right: 10rpx;
  border-radius: 6rpx;
  overflow: hidden;
}
.popup .popup-top .info .info-name.data-v-6471714e {
  color: #fae1aa;
}
.popup .popup-top .popup-title.data-v-6471714e {
  margin-top: 50rpx;
  text-align: center;
  color: #fae1aa;
}
.popup .popup-top .popup-top-str.data-v-6471714e {
  position: absolute;
  left: calc(50% - 600rpx);
  bottom: 0rpx;
  width: 1200rpx;
  height: 1100rpx;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0rpx 4rpx 10rpx rgba(0, 0, 0, 0.2);
}
.popup .popup-top .popup-top-str .popup-top-str-img.data-v-6471714e {
  position: absolute;
  left: calc(50% - 290rpx);
  bottom: 0;
  width: 580rpx;
  height: 800rpx;
}
.popup .popup-top .popup-top-icon.data-v-6471714e {
  position: absolute;
  bottom: -90rpx;
  left: calc(50% - 90rpx);
  width: 180rpx;
  height: 180rpx;
  background-color: #eccc99;
  border-radius: 50%;
  box-shadow: 0rpx 2rpx 6rpx rgba(0, 0, 0, 0.2);
}
.popup .popup-top .popup-top-icon .img.data-v-6471714e {
  width: 40%;
  height: 40%;
}
.popup .popup-top .popup_top_icon.data-v-6471714e {
  -webkit-animation: animateName-data-v-6471714e 1.5s infinite;
          animation: animateName-data-v-6471714e 1.5s infinite;
}
.popup .popup-button.data-v-6471714e {
  position: relative;
  z-index: 1;
  width: 100%;
  height: 200rpx;
  border-radius: 0 0 20rpx 20rpx;
}
.popup .popup-button .popup-button-text.data-v-6471714e {
  position: absolute;
  height: 40rpx;
  width: 100%;
  bottom: 40rpx;
  left: 0;
  color: #fae1aa;
}
.popup .popup-button .popup-button-text .popup-button-icon.data-v-6471714e {
  width: 40rpx;
  height: 40rpx;
}
@-webkit-keyframes animateName-data-v-6471714e {
0% {
    -webkit-transform: rotateY(0deg);
            transform: rotateY(0deg);
}
50% {
    -webkit-transform: rotateY(180deg);
            transform: rotateY(180deg);
}
100% {
    -webkit-transform: rotateY(0deg);
            transform: rotateY(0deg);
}
}
@keyframes animateName-data-v-6471714e {
0% {
    -webkit-transform: rotateY(0deg);
            transform: rotateY(0deg);
}
50% {
    -webkit-transform: rotateY(180deg);
            transform: rotateY(180deg);
}
100% {
    -webkit-transform: rotateY(0deg);
            transform: rotateY(0deg);
}
}
