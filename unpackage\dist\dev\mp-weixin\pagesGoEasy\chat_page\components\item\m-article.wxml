<view class="flex_c row data-v-4766a8bb"><view data-event-opts="{{[['tap',[['onClick',['$event']]]]]}}" class="{{['flex_r','text-box','data-v-4766a8bb',(isMy)?'text_box':'']}}" catchtap="__e"><view class="{{['text','data-v-4766a8bb',isMy?'text_r':'text_l']}}"><view class="flex_c_c article data-v-4766a8bb"><view class="text_32 ellipsis_2 article-title data-v-4766a8bb">{{value.payload.title}}</view><view class="flex_r text_26 color__ article-box data-v-4766a8bb"><view class="flex1 article-box-text data-v-4766a8bb">{{''+value.payload.short_title+''}}</view><view class="article-box-img data-v-4766a8bb"><image class="img data-v-4766a8bb" src="{{value.payload.share_image}}" mode="aspectFill"></image></view></view><view class="m-line data-v-4766a8bb"><m-line vue-id="b52220aa-1" color="#f0f0f0" length="100%" hairline="{{true}}" class="data-v-4766a8bb" bind:__l="__l"></m-line></view><view class="flex_r fa_c article-b data-v-4766a8bb"><view class="article-b-icon data-v-4766a8bb"></view><view class="text_20 color__ article-b-text data-v-4766a8bb">xxxx</view></view></view></view></view></view>