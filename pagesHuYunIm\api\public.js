import request from '../utils/request'
import { pretty } from '../utils/index.js'

// export function getUserInfo() {
// 	return pretty(request.get('/jeecg-boot/huyun/front/chat/myGroupList'));
// }

/**
 * 用户列表，也有群组，其实都是以群组形式呈现的。只是好友关系的群组，就刚好是两个人
 */
export function listUser() {
  return pretty(request.get('/jeecg-boot/huyun/front/chat/myGroupList'))
}
export function msglist(params) {
  return pretty(request.get('/jeecg-boot/huyun/front/chat/myMsglist', params))
}
