/**
 * 微信风格聊天页面演示脚本
 * 展示新MQTT工具包的使用方法和优化效果
 */

// 导入新的MQTT工具包
import mqttClient from './utils/mqttClient.js'
import { createUserInfo, createChatMessage, MESSAGE_TYPES, TOPIC_TEMPLATES } from './utils/mqttConfig.js'
import mqtt from './lib/mqtt.min.js'

/**
 * 演示：基本连接使用
 */
function demoBasicConnection() {
  console.log('=== 演示：基本MQTT连接 ===')
  
  // 1. 设置MQTT库
  mqttClient.setMqttLib(mqtt)
  
  // 2. 创建用户信息
  const userInfo = createUserInfo(
    'demo-user-001',
    '演示用户',
    'demo-channel',
    'demo-token-123',
    'https://dummyimage.com/100x100/007AFF/ffffff?text=Demo',
    'DEV'
  )
  
  // 3. 设置回调函数
  const callbacks = {
    onConnect: () => {
      console.log('✅ MQTT连接成功')
      demoSendMessage()
    },
    onMessage: (topic, message) => {
      console.log('📨 收到消息:', topic, message)
    },
    onReconnect: () => {
      console.log('🔄 MQTT重连中...')
    },
    onError: (error) => {
      console.error('❌ MQTT连接错误:', error)
    },
    onEnd: () => {
      console.log('🔌 MQTT连接已断开')
    }
  }
  
  // 4. 连接MQTT
  mqttClient.connect(userInfo, callbacks)
}

/**
 * 演示：发送消息
 */
function demoSendMessage() {
  console.log('=== 演示：发送消息 ===')
  
  // 创建聊天消息
  const message = createChatMessage(
    'Hello, 这是一条演示消息！',
    'demo-user-001',
    '演示用户',
    'demo-group-001'
  )
  
  // 发布到群组主题
  const topic = TOPIC_TEMPLATES.GROUP_CHAT('demo-group-001')
  const success = mqttClient.publish(topic, message)
  
  if (success) {
    console.log('✅ 消息发送成功')
  } else {
    console.log('❌ 消息发送失败')
  }
}

/**
 * 演示：订阅主题
 */
function demoSubscribeTopics() {
  console.log('=== 演示：订阅主题 ===')
  
  // 订阅多个主题
  const topics = [
    TOPIC_TEMPLATES.GROUP_CHAT('demo-group-001'),
    TOPIC_TEMPLATES.GROUP_CHAT('demo-group-002'),
    TOPIC_TEMPLATES.SYSTEM_NOTIFY('demo-user-001')
  ]
  
  topics.forEach(topic => {
    mqttClient.subscribe(topic, { qos: 0 }, (err) => {
      if (!err) {
        console.log(`✅ 订阅成功: ${topic}`)
      } else {
        console.error(`❌ 订阅失败: ${topic}`, err)
      }
    })
  })
}

/**
 * 演示：消息处理
 */
function demoMessageHandling() {
  console.log('=== 演示：消息处理 ===')
  
  // 模拟接收到的消息
  const mockMessages = [
    {
      command: MESSAGE_TYPES.CHAT_MSG,
      data: {
        content: '大家好！',
        userId: 'user-001',
        nickname: '张三',
        groupId: 'demo-group-001',
        messageType: 'text',
        createTime: Date.now()
      }
    },
    {
      command: MESSAGE_TYPES.CHAT_MSG,
      data: {
        content: '[图片]',
        userId: 'user-002',
        nickname: '李四',
        groupId: 'demo-group-001',
        messageType: 'image',
        createTime: Date.now()
      }
    },
    {
      command: MESSAGE_TYPES.SYSTEM_MSG,
      data: {
        content: '系统维护通知',
        type: 'info',
        createTime: Date.now()
      }
    }
  ]
  
  // 处理消息
  mockMessages.forEach((message, index) => {
    setTimeout(() => {
      console.log(`📨 处理消息 ${index + 1}:`, message)
      handleMessage(message)
    }, index * 1000)
  })
}

/**
 * 消息处理函数
 */
function handleMessage(mqttMsg) {
  switch (mqttMsg.command) {
    case MESSAGE_TYPES.CHAT_MSG:
      const chatMsg = mqttMsg.data
      console.log(`💬 聊天消息: [${chatMsg.nickname}] ${chatMsg.content}`)
      break
      
    case MESSAGE_TYPES.SYSTEM_MSG:
      const sysMsg = mqttMsg.data
      console.log(`🔔 系统消息: ${sysMsg.content}`)
      break
      
    default:
      console.log('❓ 未知消息类型:', mqttMsg.command)
  }
}

/**
 * 演示：连接状态管理
 */
function demoConnectionStatus() {
  console.log('=== 演示：连接状态管理 ===')
  
  // 检查连接状态
  const isConnected = mqttClient.getConnectStatus()
  console.log('🔗 当前连接状态:', isConnected ? '已连接' : '未连接')
  
  // 获取客户端实例
  const client = mqttClient.getClient()
  console.log('📱 客户端实例:', client ? '存在' : '不存在')
  
  // 获取用户信息
  const userInfo = mqttClient.getUserInfo()
  console.log('👤 用户信息:', userInfo)
}

/**
 * 演示：错误处理
 */
function demoErrorHandling() {
  console.log('=== 演示：错误处理 ===')
  
  try {
    // 尝试在未连接时发送消息
    if (!mqttClient.getConnectStatus()) {
      const result = mqttClient.publish('/test/topic', 'test message')
      console.log('📤 发送结果:', result)
    }
    
    // 尝试订阅无效主题
    mqttClient.subscribe('', {}, (err) => {
      if (err) {
        console.log('❌ 订阅错误:', err.message)
      }
    })
    
  } catch (error) {
    console.error('💥 捕获异常:', error.message)
  }
}

/**
 * 演示：性能对比
 */
function demoPerformanceComparison() {
  console.log('=== 演示：性能对比 ===')
  
  console.log('📊 代码行数对比:')
  console.log('  原始代码: 70+ 行复杂的MQTT连接代码')
  console.log('  优化后:   10+ 行简洁的工具包调用')
  
  console.log('🚀 功能对比:')
  console.log('  原始代码: 基础连接，缺乏错误处理')
  console.log('  优化后:   自动重连、心跳保活、完善错误处理')
  
  console.log('🎨 UI对比:')
  console.log('  原始代码: 简单列表，用户体验差')
  console.log('  优化后:   微信风格，现代化界面')
}

/**
 * 运行所有演示
 */
function runAllDemos() {
  console.log('🎯 开始运行MQTT工具包演示...\n')
  
  // 依次运行各个演示
  demoPerformanceComparison()
  
  setTimeout(() => {
    demoBasicConnection()
  }, 1000)
  
  setTimeout(() => {
    demoSubscribeTopics()
  }, 3000)
  
  setTimeout(() => {
    demoMessageHandling()
  }, 5000)
  
  setTimeout(() => {
    demoConnectionStatus()
  }, 8000)
  
  setTimeout(() => {
    demoErrorHandling()
  }, 10000)
  
  setTimeout(() => {
    console.log('\n🎉 演示完成！')
    console.log('📚 更多信息请查看: pagesHuYunIm/README.md')
  }, 12000)
}

// 导出演示函数
export {
  demoBasicConnection,
  demoSendMessage,
  demoSubscribeTopics,
  demoMessageHandling,
  demoConnectionStatus,
  demoErrorHandling,
  demoPerformanceComparison,
  runAllDemos
}

// 如果直接运行此文件，则执行所有演示
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.mqttDemo = { runAllDemos }
} else if (typeof global !== 'undefined') {
  // Node.js环境
  global.mqttDemo = { runAllDemos }
}

export default {
  runAllDemos
}
