@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.row_.data-v-2523fca1 {
  flex-direction: row-reverse;
}
.text_box.data-v-2523fca1 {
  flex-direction: row-reverse;
}
.text.data-v-2523fca1 {
  position: relative;
  z-index: 99;
  box-sizing: border-box;
}
.text_r.data-v-2523fca1 {
  position: relative;
}
.text_l.data-v-2523fca1 {
  position: relative;
}
.text_r.data-v-2523fca1::after {
  position: absolute;
  z-index: -1;
  content: "";
  top: 26rpx;
  right: -8rpx;
  width: 18rpx;
  height: 18rpx;
  border-radius: 2px;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  background-color: #fff;
}
.text_l.data-v-2523fca1::after {
  position: absolute;
  z-index: -1;
  content: "";
  top: 26rpx;
  left: -8rpx;
  width: 18rpx;
  height: 18rpx;
  border-radius: 2px;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  background-color: #fff;
}
.map.data-v-2523fca1 {
  width: 490rpx;
  height: 300rpx;
  border-radius: 10rpx;
  overflow: hidden;
  background-color: #fff;
  border: 0.5px solid #fff;
}
.map .map-title.data-v-2523fca1 {
  box-sizing: border-box;
  width: 100%;
  padding: 14rpx 20rpx 4rpx 20rpx;
}
.map .map-text.data-v-2523fca1 {
  box-sizing: border-box;
  width: 100%;
  color: #afafaf;
  padding: 0rpx 20rpx 6rpx 20rpx;
}
.map .map-img.data-v-2523fca1 {
  position: relative;
  width: 100%;
}
.map .map-img .str.data-v-2523fca1 {
  box-sizing: border-box;
  position: absolute;
  z-index: 3;
  top: calc(50% - 60rpx);
  left: calc(50% - 25rpx);
  width: 50rpx;
  height: 50rpx;
  border: 12rpx solid #07be5b;
  border-radius: 50%;
  background-color: #fff;
}
.map .map-img .str.data-v-2523fca1::before {
  position: absolute;
  z-index: 0;
  content: "";
  bottom: -40rpx;
  left: 12rpx;
  width: 6rpx;
  height: 40rpx;
  border-radius: 3rpx;
  background-color: #07be5b;
}
