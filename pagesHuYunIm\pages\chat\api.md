订阅： /chat/client/1911713314310377473    1911713314310377473是获取登录参数时返回的client。
发布：/chat/server/1911713314310377473/msg   1911713314310377473是获取登录参数时返回的client。


### 发布消息：  topic: /chat/server/1911713314310377473/msg 
##### 文本消息：
 ``` 
 {
  "groupId": "1911787038405492737", //群组ID，通过HTTP获取群组列表的时候返回的
  "msgType": "text", //消息类型 (text、image、voice、vedio、music、news)
  "content": "大家好才是真的好",
  "localMsgId":"11111111111",//本地消息的唯一ID，可能用于匹配回推消息
} 

 ```
 
#####  图片消息：
 ``` 
 {
  "groupId": "1911787038405492737", //群组ID，通过HTTP获取群组列表的时候返回的
  "msgType": "image", //消息类型 (text、image、voice、vedio、music、news)
  "content": "https://huyun-chat.oss-cn-chengdu.aliyuncs.com/upload/wx_camera_1748990547000_1749008140790.jpg",
  "localMsgId":"11111111111",//本地消息的唯一ID，可能用于匹配回推消息
} 

 ```
 
 #####  语音消息：
 ``` 
 {
  "groupId": "1911787038405492737", //群组ID，通过HTTP获取群组列表的时候返回的
  "msgType": "voice", //消息类型 (text、image、voice、vedio、music、news)
  "content": '{"msg":"语音 6\"","audioSrc":"https://huyun-chat.oss-cn-chengdu.aliyuncs.com/upload/recorder_1749008159383.mp3"}',
  "localMsgId":"11111111111",//本地消息的唯一ID，可能用于匹配回推消息
} 

 ```
 
 
 
###  Ping
发布：/chat/server/1911713314310377473/ping   1911713314310377473是获取登录参数时返回的client。
```
{}//ping的时候发一个空的就好了
```

### 撤回消息（用户撤回自己的消息）：  topic: /chat/server/1911713314310377473/withdraw 
```
{
	"id":"1911787038405492737_000015",//这是消息的ID
	"localMsgId":"******"//本地消息的唯一ID，id和localMsgId 二选一，因为用户刚发完消息，没有id，所以只有localMsgId, 如果是历史消息，可能就没有localMsgId。所以是二选一
} 
```
### 已读消息（用户收到消息后反馈）：  topic: /chat/server/1911713314310377473/read
把收到的消息data部分的id和groupId反馈回来； 
```
 {
			"id":"1911787038405492737_000015",//这是消息的ID
		  "groupId": "1911787038405492737", //群组ID，通过HTTP获取群组列表的时候返回的
		} 
```
 

### 订阅： /chat/client/1911713314310377473 
##### 聊天消息-文本消息
```
{
	"data": {
			"id":"1911787038405492737_000015",//这是消息的ID
		  "groupId": "1911787038405492737", //群组ID，通过HTTP获取群组列表的时候返回的
		  "msgType": "text", //消息类型 (text、image、voice、vedio、music、news)
		  "content": "大家好才是真的好", //如果是其他类型的消息，参考上面发送
		  "localMsgId":"11111111111",//本地消息的唯一ID，可能用于匹配回推消息
		} 
	"command":"chatMsg" //chatMsg 为聊天内容, withdraw为撤回消息的推送
}
```
##### 撤回消息-其他用户的撤回动作，同步到群里其他人
```
{
	"data": {
			"id":"1911787038405492737_000015",//这是消息的ID
			"groupId":"1911787038405492738 ",//群ID
		} 
	"command":"withdraw" // withdraw为撤回消息的推送
}
```

##### 禁言/恢复禁言通知,管理员对用户禁言/恢复禁言后，通知群里所有人
```
{
	"data": {
			"userId":"1911787038405492737_000015",//被禁言/恢复禁言的用户ID
			"groupId":"1911787038405492738 ",//群ID
			"status":0,//0被禁言了，1恢复正常了
		} 
	"command":"forbidden" // forbidden 为禁言/恢复禁言
}
```

##### 禁言/恢复禁言通知,管理员对群禁言/恢复禁言后，通知群里所有人
```
{
	"data": {
			"groupId":"1911787038405492738 ",//群ID
			"status":0,//0被禁言了，1恢复正常了
		} 
	"command":"forbidden" // forbidden 为禁言/恢复禁言
}
```

#### 踢人-群主踢人后，通知群里所有人
```
{
	"data": {
			"groupId":"1911787038405492738 ",//群ID
			"userId":"1911713372078526466",//被踢的用户
		} 
	"command":"kickOut" // kickOut 为踢人
}
```



