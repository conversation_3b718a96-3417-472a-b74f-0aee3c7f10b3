<template>
	<view>
		<view class="color__ title">请选择投诉该群的原因</view>
		<view class="icon_ row" v-for="(item, index) in list" :key="index" @click="onClick(item)">
			<view class="row-title flex1">{{ item.title }}</view>
			<view class="row-icon">
				<image
					class="img"
					src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTMyMS4xMTggNjUuNjEybC02NC4wMzIgNjMuMzk3IDM4MC45OTMgMzgzLjYwNi0zODQuMTk2IDM4MC4zNzcgNjMuNDkgNjMuOTI1IDQ0OC4yMjEtNDQzLjc4eiIgZmlsbD0iI2I0YjRiNCIvPjwvc3ZnPg=="
					mode="aspectFill"
				></image>
			</view>
			<view class="m-line">
				<m-line color="#B8B8B8" length="100%" :hairline="true"></m-line>
			</view>
		</view>
	</view>
</template>

<script>
import { show, to, jsonUrl } from '@/utils/index.js';
let group_id = null;
export default {
	data() {
		return {
			list: [
				{
					title: '该群存在赌博行为'
				},
				{
					title: '该群存在欺诈骗钱行为'
				},
				{
					title: '该群发布不适当的信息对我造成骚扰'
				},
				{
					title: '该群传播谣言信息'
				},

				{
					title: '该群在发布仿冒品信息'
				},
				{
					title: '该群侵犯未成年人权益'
				},
				{
					title: '该群存在粉丝无底线追星行为'
				}
			]
		};
	},
	onLoad(e) {
		group_id = jsonUrl(e).group_id;
	},
	methods: {
		async onClick(item) {},
	}
};
</script>

<style scoped lang="scss">
.title {
	box-sizing: border-box;
	padding: 0 30rpx;
	width: 100%;
	height: 100rpx;
	line-height: 100rpx;
	margin: 0 auto;
	background-color: #f7f7f7;
}
.row {
	position: relative;
	box-sizing: border-box;
	padding-left: 30rpx;
	width: 100%;
	height: 100rpx;
	.row-title {
		height: 100%;
		line-height: 100rpx;
	}
	.row-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 20rpx;
	}
	.m-line {
		position: absolute;
		left: 30rpx;
		right: 0;
		bottom: 0;
	}
}
</style>
