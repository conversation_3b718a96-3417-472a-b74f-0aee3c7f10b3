{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/mqtt-toolkit/pages/index/index.vue?a9b8", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/mqtt-toolkit/pages/index/index.vue?4d7d", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/mqtt-toolkit/pages/index/index.vue?1b1c", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/mqtt-toolkit/pages/index/index.vue?de6c", "uni-app:///mqtt-toolkit/pages/index/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/mqtt-toolkit/pages/index/index.vue?67e2", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/mqtt-toolkit/pages/index/index.vue?1e3d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isConnected", "onLoad", "onShow", "methods", "checkConnectionStatus", "goToDemo", "uni", "url", "goToConfig"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AAC2L;AAC3L,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0tB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC6H9uB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;AACA;AACA;IACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MACAC;QACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MACAF;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1KA;AAAA;AAAA;AAAA;AAAqjC,CAAgB,g9BAAG,EAAC,C;;;;;;;;;;;ACAzkC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "mqtt-toolkit/pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './mqtt-toolkit/pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=3500499e&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=3500499e&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3500499e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"mqtt-toolkit/pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=3500499e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"toolkit-home\">\n    <!-- 头部介绍 -->\n    <view class=\"header-section\">\n      <image class=\"toolkit-logo\" src=\"/static/logo.png\"></image>\n      <view class=\"header-content\">\n        <text class=\"toolkit-title\">MQTT Toolkit</text>\n        <text class=\"toolkit-subtitle\">专业的MQTT工具包</text>\n        <text class=\"toolkit-description\">提供完整的MQTT连接、消息处理、配置管理功能</text>\n      </view>\n    </view>\n\n    <!-- 功能特性 -->\n    <view class=\"features-section\">\n      <view class=\"section-title\">核心特性</view>\n      <view class=\"features-grid\">\n        <view class=\"feature-item\">\n          <view class=\"feature-icon\">🔄</view>\n          <text class=\"feature-title\">自动重连</text>\n          <text class=\"feature-desc\">智能重连机制</text>\n        </view>\n        <view class=\"feature-item\">\n          <view class=\"feature-icon\">💓</view>\n          <text class=\"feature-title\">心跳保活</text>\n          <text class=\"feature-desc\">自动心跳维持</text>\n        </view>\n        <view class=\"feature-item\">\n          <view class=\"feature-icon\">📦</view>\n          <text class=\"feature-title\">单例模式</text>\n          <text class=\"feature-desc\">全局共享连接</text>\n        </view>\n        <view class=\"feature-item\">\n          <view class=\"feature-icon\">⚙️</view>\n          <text class=\"feature-title\">配置管理</text>\n          <text class=\"feature-desc\">可视化配置</text>\n        </view>\n        <view class=\"feature-item\">\n          <view class=\"feature-icon\">🎯</view>\n          <text class=\"feature-title\">主题模板</text>\n          <text class=\"feature-desc\">预定义模板</text>\n        </view>\n        <view class=\"feature-item\">\n          <view class=\"feature-icon\">🛡️</view>\n          <text class=\"feature-title\">错误处理</text>\n          <text class=\"feature-desc\">完善异常处理</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 快速入口 -->\n    <view class=\"quick-access-section\">\n      <view class=\"section-title\">快速入口</view>\n      <view class=\"access-buttons\">\n        <button class=\"access-btn demo-btn\" @click=\"goToDemo\">\n          <view class=\"btn-icon\">🚀</view>\n          <view class=\"btn-content\">\n            <text class=\"btn-title\">功能演示</text>\n            <text class=\"btn-desc\">体验完整MQTT功能</text>\n          </view>\n        </button>\n\n        <button class=\"access-btn config-btn\" @click=\"goToConfig\">\n          <view class=\"btn-icon\">⚙️</view>\n          <view class=\"btn-content\">\n            <text class=\"btn-title\">配置管理</text>\n            <text class=\"btn-desc\">管理MQTT连接配置</text>\n          </view>\n        </button>\n      </view>\n    </view>\n\n    <!-- 状态信息 -->\n    <view class=\"status-section\">\n      <view class=\"section-title\">当前状态</view>\n      <view class=\"status-cards\">\n        <view class=\"status-card\">\n          <text class=\"status-label\">连接状态</text>\n          <text :class=\"['status-value', isConnected ? 'connected' : 'disconnected']\">\n            {{ isConnected ? '已连接' : '未连接' }}\n          </text>\n        </view>\n        <view class=\"status-card\">\n          <text class=\"status-label\">工具版本</text>\n          <text class=\"status-value\">v1.0.0</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 使用说明 -->\n    <view class=\"usage-section\">\n      <view class=\"section-title\">使用说明</view>\n      <view class=\"usage-steps\">\n        <view class=\"usage-step\">\n          <view class=\"step-number\">1</view>\n          <view class=\"step-content\">\n            <text class=\"step-title\">配置连接</text>\n            <text class=\"step-desc\">在配置页面设置MQTT服务器信息</text>\n          </view>\n        </view>\n        <view class=\"usage-step\">\n          <view class=\"step-number\">2</view>\n          <view class=\"step-content\">\n            <text class=\"step-title\">测试连接</text>\n            <text class=\"step-desc\">使用演示页面测试MQTT功能</text>\n          </view>\n        </view>\n        <view class=\"usage-step\">\n          <view class=\"step-number\">3</view>\n          <view class=\"step-content\">\n            <text class=\"step-title\">集成使用</text>\n            <text class=\"step-desc\">在项目中导入并使用MQTT工具</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 底部信息 -->\n    <view class=\"footer-section\">\n      <text class=\"footer-text\">MQTT Toolkit - 让MQTT使用更简单</text>\n      <text class=\"footer-version\">Version 1.0.0</text>\n    </view>\n  </view>\n</template>\n\n<script>\nimport mqttClient from '../../utils/mqttClient.js'\n\nexport default {\n  data() {\n    return {\n      isConnected: false\n    }\n  },\n\n  onLoad() {\n    // 检查MQTT连接状态\n    this.checkConnectionStatus()\n  },\n\n  onShow() {\n    // 页面显示时更新状态\n    this.checkConnectionStatus()\n  },\n\n  methods: {\n    /**\n     * 检查连接状态\n     */\n    checkConnectionStatus() {\n      this.isConnected = mqttClient.getConnectStatus()\n    },\n\n    /**\n     * 跳转到演示页面\n     */\n    goToDemo() {\n      uni.navigateTo({\n        url: '/mqtt-toolkit/pages/demo/index'\n      })\n    },\n\n    /**\n     * 跳转到配置页面\n     */\n    goToConfig() {\n      uni.navigateTo({\n        url: '/mqtt-toolkit/pages/config/index'\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.toolkit-home {\n  padding: 20rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  min-height: 100vh;\n}\n\n.header-section {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20rpx;\n  padding: 40rpx;\n  margin-bottom: 30rpx;\n  display: flex;\n  align-items: center;\n  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);\n}\n\n.toolkit-logo {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 20rpx;\n  margin-right: 30rpx;\n}\n\n.header-content {\n  flex: 1;\n}\n\n.toolkit-title {\n  font-size: 48rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.toolkit-subtitle {\n  font-size: 28rpx;\n  color: #666;\n  display: block;\n  margin-bottom: 15rpx;\n}\n\n.toolkit-description {\n  font-size: 24rpx;\n  color: #999;\n  line-height: 1.5;\n  display: block;\n}\n\n.features-section,\n.quick-access-section,\n.status-section,\n.usage-section {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20rpx;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);\n}\n\n.section-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 30rpx;\n  text-align: center;\n}\n\n.features-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 20rpx;\n}\n\n.feature-item {\n  background: #f8f9fa;\n  border-radius: 15rpx;\n  padding: 25rpx;\n  text-align: center;\n  border: 2rpx solid #e9ecef;\n}\n\n.feature-icon {\n  font-size: 40rpx;\n  margin-bottom: 15rpx;\n}\n\n.feature-title {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 8rpx;\n}\n\n.feature-desc {\n  font-size: 24rpx;\n  color: #666;\n  display: block;\n}\n\n.access-buttons {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n}\n\n.access-btn {\n  display: flex;\n  align-items: center;\n  padding: 30rpx;\n  border-radius: 15rpx;\n  border: none;\n  text-align: left;\n  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.1);\n}\n\n.demo-btn {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n.config-btn {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n  color: white;\n}\n\n.btn-icon {\n  font-size: 50rpx;\n  margin-right: 25rpx;\n}\n\n.btn-content {\n  flex: 1;\n}\n\n.btn-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  display: block;\n  margin-bottom: 8rpx;\n}\n\n.btn-desc {\n  font-size: 26rpx;\n  opacity: 0.9;\n  display: block;\n}\n\n.status-cards {\n  display: flex;\n  gap: 20rpx;\n}\n\n.status-card {\n  flex: 1;\n  background: #f8f9fa;\n  border-radius: 15rpx;\n  padding: 25rpx;\n  text-align: center;\n  border: 2rpx solid #e9ecef;\n}\n\n.status-label {\n  font-size: 24rpx;\n  color: #666;\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.status-value {\n  font-size: 28rpx;\n  font-weight: bold;\n  display: block;\n}\n\n.connected {\n  color: #28a745;\n}\n\n.disconnected {\n  color: #dc3545;\n}\n\n.usage-steps {\n  display: flex;\n  flex-direction: column;\n  gap: 25rpx;\n}\n\n.usage-step {\n  display: flex;\n  align-items: center;\n}\n\n.step-number {\n  width: 60rpx;\n  height: 60rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  font-weight: bold;\n  margin-right: 25rpx;\n}\n\n.step-content {\n  flex: 1;\n}\n\n.step-title {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 8rpx;\n}\n\n.step-desc {\n  font-size: 24rpx;\n  color: #666;\n  display: block;\n}\n\n.footer-section {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 20rpx;\n  padding: 30rpx;\n  text-align: center;\n  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);\n}\n\n.footer-text {\n  font-size: 28rpx;\n  color: #333;\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.footer-version {\n  font-size: 24rpx;\n  color: #666;\n  display: block;\n}\n</style>\n", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=3500499e&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=3500499e&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754969675948\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}