<template>
	<view>
		<view style="height: 130rpx"></view>
		<view class="text_40 bold_ title">我在群里的昵称</view>
		<view class="text_30 text">昵称昵称后，只会在此群显示，群内成员都可见。</view>
		<view class="icon_ row">
			<view class="row-img">
				<image class="img" :src="avatar" mode="aspectFill"></image>
			</view>
			<view class="flex1 row-input">
				<input v-model="name" confirm-type="done" focus hold-keyboard :adjust-position="false" type="text" />
			</view>
			<view class="row-icon" @click="name = ''">
				<image
					class="img"
					src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTUxMi4yIDY0LjRjLTI0Ny40IDAtNDQ3LjkgMjAwLjYtNDQ3LjkgNDQ4czIwMC41IDQ0Ny45IDQ0Ny45IDQ0Ny45IDQ0Ny45LTIwMC41IDQ0Ny45LTQ0Ny45LTIwMC41LTQ0OC00NDcuOS00NDh6bTIwMy41IDYwNS45YzEyLjUgMTIuNSAxMi41IDMzLjEgMCA0NS42cy0zMy4xIDEyLjUtNDUuNiAwbC0xNTgtMTU4LTE1OCAxNThjLTEyLjUgMTIuNS0zMy4xIDEyLjUtNDUuNiAwcy0xMi41LTMzLjEgMC00NS42bDE1OC0xNTgtMTU4LTE1OGMtMTIuNS0xMi41LTEyLjUtMzMuMSAwLTQ1LjZzMzMuMS0xMi41IDQ1LjYgMGwxNTggMTU4IDE1OC0xNThjMTIuNS0xMi41IDMzLjEtMTIuNSA0NS42IDBzMTIuNSAzMy4xIDAgNDUuNmwtMTU4IDE1OCAxNTggMTU4eiIgZmlsbD0iIzcwNzA3MCIvPjwvc3ZnPg=="
					mode="aspectFill"
				></image>
			</view>
		</view>
		<view class="icon_ text_30 bold_ size_white confirm" @click="confirm">确定</view>
	</view>
</template>

<script>
import { show, to, jsonUrl } from '@/utils/index.js';
let group_id = null;
export default {
	components: {},
	data() {
		return {
			avatar: '',
			name: ''
		};
	},
	onLoad(e) {
		const data = jsonUrl(e);
		// console.log(data)
		// group_id = data.group_id;
		// this.name = data.nickname;
		// this.avatar = data.member_avatar;
	},
	methods: {
		// 提交
		async confirm() {
			if (!this.name) return show('昵称不能为空');

				// 更新上一个页面
				uni.$emit('getGroupMemberInfo');
				uni.$off('getGroupMemberInfo');
				await show('提交成功', 1500, 'success');
				to();
			
		}
	}
};
</script>

<style lang="scss" scoped>
.title {
	width: 100%;
	height: 80rpx;
	text-align: center;
	margin: 0 auto;
}
.text {
	width: 100%;
	height: 60rpx;
	text-align: center;
	margin: 0 auto;
}
.row {
	width: calc(100% - 60rpx);
	height: 110rpx;
	margin: 30rpx auto 200rpx auto;
	border-top: 0.5px solid rgba(153, 153, 153, 0.3);
	border-bottom: 0.5px solid rgba(153, 153, 153, 0.3);
	.row-img {
		width: 80rpx;
		height: 80rpx;
		margin-left: 10rpx;
		border-radius: 10rpx;
		overflow: hidden;
		background-color: #f1f1f1;
	}
	.row-input {
		height: 80rpx;
		margin: 0 20rpx;
		input {
			height: 100%;
			line-height: 80rpx;
		}
	}
	.row-icon {
		width: 38rpx;
		height: 38rpx;
		margin-right: 10rpx;
	}
}
.confirm {
	width: 300rpx;
	height: 80rpx;
	border-radius: 10rpx;
	margin: 0 auto;
	background-color: #05c160;
}
</style>
