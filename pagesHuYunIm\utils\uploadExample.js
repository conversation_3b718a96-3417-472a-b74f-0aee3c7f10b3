import request from './request.js'
import store from '../store'

/**
 * 上传图片示例
 * @param {String} filePath 图片文件路径
 * @param {String} groupId 群组ID
 * @param {String} selfUserId 当前用户ID
 * @param {Array} messageList 消息列表
 * @param {Object} mqttClient MQTT客户端
 * @param {Function} scrollToBottom 滚动到底部的方法
 */
export function uploadImageExample(filePath, groupId, selfUserId, messageList, mqttClient, scrollToBottom) {
  // 使用新的上传API
  request.uploadFile(filePath)
    .then(data => {
      // 上传成功，创建聊天消息
      let chatMsg = {
        content: data.message || data, // 根据服务器返回的数据结构调整
        userId: selfUserId,
        groupId: groupId,
        msgType: 'image'
      }
      
      // 添加到消息列表
      messageList.push(chatMsg)
      console.log('chatMsgImage', chatMsg)
      
      // 发布MQTT消息
      mqttClient.publish('/chat/server/' + store.state.app.userInfo.userId + '/msg', JSON.stringify(chatMsg))
      
      // 滚动到底部
      scrollToBottom()
      
      console.log('图片上传成功:', data)
    })
    .catch(error => {
      console.error('图片上传失败:', error)
      uni.showToast({
        title: '上传失败',
        icon: 'none'
      })
    })
}

/**
 * 上传文件示例（通用）
 * @param {String} filePath 文件路径
 * @param {String} fileType 文件类型 ('image', 'video', 'audio', 'file')
 * @param {Object} options 其他选项
 */
export function uploadFileExample(filePath, fileType = 'file', options = {}) {
  const {
    url = '/jeecg-boot/huyun/front/chat/upload',
    name = 'file',
    formData = {},
    onSuccess,
    onError
  } = options
  
  return request.uploadFile(filePath, url, name, formData)
    .then(data => {
      console.log('文件上传成功:', data)
      
      // 显示成功提示
      uni.showToast({
        title: '上传成功',
        icon: 'success'
      })
      
      // 执行成功回调
      if (onSuccess && typeof onSuccess === 'function') {
        onSuccess(data)
      }
      
      return data
    })
    .catch(error => {
      console.error('文件上传失败:', error)
      
      // 显示错误提示
      uni.showToast({
        title: typeof error === 'string' ? error : '上传失败',
        icon: 'none'
      })
      
      // 执行错误回调
      if (onError && typeof onError === 'function') {
        onError(error)
      }
      
      throw error
    })
}

/**
 * 批量上传文件
 * @param {Array} filePaths 文件路径数组
 * @param {Object} options 上传选项
 */
export function uploadMultipleFiles(filePaths, options = {}) {
  const uploadPromises = filePaths.map(filePath => {
    return request.uploadFile(filePath, options.url, options.name, options.formData)
  })
  
  return Promise.all(uploadPromises)
    .then(results => {
      console.log('批量上传成功:', results)
      uni.showToast({
        title: '批量上传成功',
        icon: 'success'
      })
      return results
    })
    .catch(error => {
      console.error('批量上传失败:', error)
      uni.showToast({
        title: '批量上传失败',
        icon: 'none'
      })
      throw error
    })
}
