<template>
	<view class="flex_c_c page">
		<view :style="{ height: $store.state.StatusBar.statusBar + 'px' }"></view>
		<view class="top">
			<view class="icon_ text_32 top-title">
				<view class="top-title-text" @click="to()">取消</view>
				<view class="flex1 bold_ icon_">创建群聊</view>
				<view class="size_white icon_ text_30 top-title-button" :class="{ top_title_button: items.length }" @click="submit">创建({{ items.length }})</view>
			</view>

			<view class="icon_ infr">
				<view class="icon_ infr-img" @click="chooseImage">
					<image class="img" :src="inputObj.avatar" mode="aspectFill" v-if="inputObj.avatar"></image>
					<image
						class="imgx"
						src="data:image/svg+xml;base64,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"
						mode="aspectFill"
						v-else
					></image>
				</view>
				<view class="flex1 infr-name">
					<input v-model="inputObj.name" type="text" placeholder="输入创建群名称" :focus="true" :adjust-position="false" />
				</view>
			</view>

			<view class="icon_ search">
				<view class="flex1 choice-list" v-if="items.length">
					<scroll-view class="screen-scroll-view" scroll-x :show-scrollbar="false">
						<view class="list-box" v-for="(item, index) in items" :key="index" @click="onItems(item)">
							<image class="img" :src="item.head_pic_text" mode="aspectFill"></image>
						</view>
					</scroll-view>
				</view>
				<view class="icon_ search-box">
					<view class="icon_ z_index2" v-if="!focus & !searchStr">
						<view class="search-icon">
							<image
								class="img"
								src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTQ2OS4zMzMgMEMyMDkuMDY3IDAgMCAyMDkuMDY3IDAgNDY5LjMzM3MyMDkuMDY3IDQ2OS4zMzQgNDY5LjMzMyA0NjkuMzM0UzkzOC42NjcgNzI5LjYgOTM4LjY2NyA0NjkuMzMzIDcyOS42IDAgNDY5LjMzMyAwem0wIDg1My4zMzNjLTIxMy4zMzMgMC0zODQtMTcwLjY2Ni0zODQtMzg0czE3MC42NjctMzg0IDM4NC0zODQgMzg0IDE3MC42NjcgMzg0IDM4NC0xNzAuNjY2IDM4NC0zODQgMzg0eiIgZmlsbD0iIzliOWI5YiIgZGF0YS1zcG0tYW5jaG9yLWlkPSJhMzEzeC5zZWFyY2hfaW5kZXguMC5pMS4xMTdjM2E4MVdwVG9pVyIgY2xhc3M9InNlbGVjdGVkIi8+PHBhdGggZD0iTTczOC4xMzMgNzQyLjRjMTcuMDY3LTE3LjA2NyA0Mi42NjctMTcuMDY3IDU5LjczNCAwbDIwOS4wNjYgMjAwLjUzM2MxNy4wNjcgMTcuMDY3IDE3LjA2NyA0Mi42NjcgMCA1OS43MzQtMTcuMDY2IDE3LjA2Ni00Mi42NjYgMTcuMDY2LTU5LjczMyAwTDczOC4xMzMgODAyLjEzM2MtMTcuMDY2LTE3LjA2Ni0xNy4wNjYtNDIuNjY2IDAtNTkuNzMzeiIgZmlsbD0iIzliOWI5YiIgZGF0YS1zcG0tYW5jaG9yLWlkPSJhMzEzeC5zZWFyY2hfaW5kZXguMC5pNC4xMTdjM2E4MVdwVG9pVyIgY2xhc3M9InNlbGVjdGVkIi8+PC9zdmc+"
								mode="aspectFill"
							></image>
						</view>
						<view class="text_32 search-text">搜索</view>
					</view>
					<view class="search-input">
						<input
							@input="search"
							confirm-type="search"
							v-model="searchStr"
							:focus="focus"
							@confirm="getList"
							@focus="focusFn"
							@blur="blurFn"
							:adjust-position="false"
							:maxlength="11"
						/>
					</view>
				</view>
			</view>
		</view>

		<view class="flex1 next-list">
			<scroll-view class="next-scroll-left" scroll-y="true" :scroll-with-animation="true">
				<template v-if="list.length">
					<view class="icon_ item" v-for="(item, index) in list" :key="index" @click="onClick(item)">
						<view class="icon_ choice showChoice" :class="{ choice_: item.isChoice }">
							<image
								class="img"
								src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTM4NCA3NjhjLTEyLjggMC0yMS4zMzMtNC4yNjctMjkuODY3LTEyLjhMMTQwLjggNTQxLjg2N2MtMTcuMDY3LTE3LjA2Ny0xNy4wNjctNDIuNjY3IDAtNTkuNzM0czQyLjY2Ny0xNy4wNjYgNTkuNzMzIDBMMzg0IDY2NS42bDQzOS40NjctNDM5LjQ2N2MxNy4wNjYtMTcuMDY2IDQyLjY2Ni0xNy4wNjYgNTkuNzMzIDBzMTcuMDY3IDQyLjY2NyAwIDU5LjczNEw0MTMuODY3IDc1NS4yQzQwNS4zMzMgNzYzLjczMyAzOTYuOCA3NjggMzg0IDc2OHoiIGZpbGw9IiNmZmYiLz48L3N2Zz4="
								mode="aspectFill"
							></image>
						</view>
						<view class="item-img">
							<image class="img" :src="item.head_pic_text" mode="aspectFill"></image>
						</view>
						<view class="text_32 flex1 flex_c fj_c item-name">
							<view class="">
								{{ item.name }}
							</view>
							<view class="text_22 color__">ID：{{ item.id }}</view>
							<view class="m-line">
								<m-line color="#cecece" length="100%" :hairline="true"></m-line>
							</view>
						</view>
					</view>
				</template>

				<view class="flex_c_c no-data" v-else>
					<view class="no-data-img">
						<image
							class="img"
							src="data:image/svg+xml;base64,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"
							mode="aspectFill"
						></image>
					</view>
					<view class="text_26 color__">输入ID搜索对方</view>
				</view>
				<view style="height: 180rpx"></view>
				<m-bottom-paceholder></m-bottom-paceholder>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import { to, show, throttle } from '@/utils/index.js';
export default {
	data() {
		return {
			focus: false,
			searchStr: '',
			inputObj: {
				name: '',
				avatar: ''
			},
			items: [],
			list: []
		};
	},
	onLoad() {
		// this.getList();
	},
	methods: {
		to,
		onClick(item) {
			item.isChoice = !item.isChoice;
			if (item.isChoice) {
				this.items.push(item);
			} else {
				this.items = this.items.filter((im) => {
					return item.id != im.id;
				});
			}
		},
		onItems(item) {
			item.isChoice = !item.isChoice;
			this.items = this.items.filter((im) => {
				return item.id != im.id;
			});
			if (!item.isChoice) {
				this.list = this.list.filter((im) => {
					return item.id == im.id ? { ...im, isChoice: false } : { ...im };
				});
			}
		},
		// 完成
		async submit() {
			if (!this.inputObj.avatar) return show('需设置群头像');
			if (!this.inputObj.name) return show('需输入群名称');
			if (!this.items.length) return show('选择群成员');
		},
		async getList() {
			let list = [
				{
					head_pic_text:'https://tse3-mm.cn.bing.net/th/id/OIP-C.w2305CRIJPAtlS6N6VYxZwAAAA?rs=1&pid=ImgDetMain',
					name:'小帅',
					id:'18276673333'
				},
				{
					head_pic_text:'https://www.keaitupian.cn/cjpic/frombd/0/253/2681933182/3871204222.jpg',
					name:'张三',
					id:'16577777777'
				},
				{
					head_pic_text:'https://tse1-mm.cn.bing.net/th/id/OIP-C.-vAO3bCwn3G4ns_RcKL_ggAAAA?rs=1&pid=ImgDetMain',
					name:'小美',
					id:'12002020233'
				},
				{
					head_pic_text:'https://tupian.qqw21.com/article/UploadPic/2020-2/202021011511555603.jpg',
					name:'wiss',
					id:'13466666666'
				}
			];
			list.forEach((item) => {
				item['isChoice'] = false;
			});
			this.items.forEach((item) => {
				list.forEach((im) => {
					if (item.id === im.id) {
						im['isChoice'] = true;
					}
				});
			});
			this.list = list;
		},
		focusFn() {
			this.focus = true;
		},
		blurFn() {
			this.focus = false;
		},
		search() {
			this.focus = true;
			throttle(
				() => {
					this.getList();
				},
				1000,
				false
			);
		},
		chooseItem(item) {
			this.$emit('itemclick', item);
			this.$refs.popup.close();
		},

		chooseImage() {
			uni.chooseImage({
				count: 1, //默认9
				sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
				sourceType: ['album', 'camera'], //从相册选择
				success: (res) => {
					this.inputObj.avatar = res.tempFilePaths[0];
				}
			});
		},
	}
};
</script>
<style>
/deep/ ::-webkit-scrollbar {
	width: 0;
	height: 0;
	color: transparent;
	display: none;
}
</style>
<style lang="scss" scoped>
.page {
	position: relative;
	width: 100%;
	height: 100vh;
	background-color: #f7f7f7;
	overflow: hidden;
	border-radius: 20rpx 20rpx 0 0;
	.top {
		width: 100%;
		height: 370rpx;
		.top-title {
			width: calc(100% - 60rpx);
			height: 120rpx;
			margin: 0 auto;
			.top-title-text {
				width: 140rpx;
			}
			.top-title-button {
				width: 140rpx;
				height: 66rpx;
				border-radius: 10rpx;
				background-color: #aaaaaa;
			}
			.top_title_button {
				background-color: #4ac165;
			}
		}

		.infr {
			width: calc(100% - 40rpx);
			height: 100rpx;
			margin: 0 auto 20rpx auto;
			.infr-img {
				box-sizing: border-box;
				border-radius: 10rpx;
				width: 90rpx;
				height: 90rpx;
				margin-right: 20rpx;
				background-color: #fff;
				.imgx {
					width: 70%;
					height: 70%;
				}
			}
			.infr-name {
				box-sizing: border-box;
				padding: 0 20rpx;
				height: 90rpx;
				border-radius: 10rpx;
				background-color: #fff;
				input {
					width: 100%;
					height: 100%;
				}
			}
		}

		.search {
			position: relative;
			width: calc(100% - 40rpx);
			height: 110rpx;
			margin: 0 auto;
			border-radius: 14rpx;
			background-color: #fff;
			.choice-list {
				box-sizing: border-box;
				width: 0;
				height: 110rpx;
				white-space: nowrap;
				::-webkit-scrollbar {
					display: none;
					width: 0 !important;
					height: 0 !important;
					-webkit-appearance: none;
					background: transparent;
					color: transparent;
				}
				.screen-scroll-view {
					min-width: 100%;
					height: 100%;
					box-sizing: border-box;
				}

				.list-box {
					width: 80rpx;
					height: 80rpx;
					border-radius: 10rpx;
					margin-left: 20rpx;
					margin-top: 15rpx;
					background-color: #f1f1f1;
					display: inline-block;
					overflow: hidden;
				}
			}
			.search-box {
				position: relative;
				width: 250rpx;
				height: 120rpx;
				.search-input {
					box-sizing: border-box;
					padding: 0 20rpx;
					position: absolute;
					z-index: 3;
					top: 0;
					left: 0;
					width: 100%;
					height: 100%;
					input {
						width: 100%;
						height: 100%;
						text-align: center;
					}
				}
				.search-icon {
					width: 34rpx;
					height: 34rpx;
					margin-right: 16rpx;
				}
				.search-text {
					color: #9b9b9b;
				}
			}
		}
	}
}

.item {
	box-sizing: border-box;
	width: 100%;
	padding: 0 0 0 20rpx;

	.choice {
		opacity: 0;
		width: 0rpx;
		height: 0rpx;
		margin-right: 0rpx;
		background-color: #fff;
		border-radius: 50%;
		border: 1px solid #999;
		transition: all 0.3s;
		.img {
			width: 80%;
			height: 80%;
			margin-top: 4rpx;
		}
	}
	.choice_ {
		background-color: #4ac165;
		border: 1px solid #4ac165;
	}
	.showChoice {
		opacity: 1;
		width: 40rpx;
		height: 40rpx;
		margin-right: 20rpx;
	}
	.item-img {
		width: 80rpx;
		height: 80rpx;
		border-radius: 10rpx;
		margin-right: 30rpx;
		overflow: hidden;
		background-color: #f1f1f1;
	}
	.item-name {
		position: relative;
		width: 100%;
		height: 120rpx;
		.m-line {
			position: absolute;
			left: 0;
			right: 0;
			bottom: 0;
		}
	}
}

.next-list {
	position: relative;
	width: 100%;
	height: 0;
	border-radius: 10rpx 10rpx 0 0;
	box-sizing: border-box;
	background-color: #fff;
	overflow: hidden;
	.next-scroll-left {
		height: 100%;

		.left-list {
		}
	}

	.no-data {
		width: 100%;
		.no-data-img {
			width: 200rpx;
			height: 200rpx;
			margin-top: 100rpx;
			margin-bottom: 20rpx;
		}
	}
}
</style>
