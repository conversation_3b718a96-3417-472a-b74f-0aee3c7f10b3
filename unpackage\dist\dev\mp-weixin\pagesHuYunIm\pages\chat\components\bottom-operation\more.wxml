<view class="more data-v-89f035d4" style="{{'height:'+(value?'430rpx':'0px')+';'}}"><view class="more-line data-v-89f035d4"><m-line vue-id="b41582a0-1" color="#d4d4d4" length="100%" hairline="{{true}}" class="data-v-89f035d4" bind:__l="__l"></m-line></view><view class="flex_r more-list data-v-89f035d4"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onClick',['$0'],[[['list','',index]]]]]]]}}" class="flex_c_c more-item data-v-89f035d4" bindtap="__e"><view class="icon_ more-item-icon data-v-89f035d4"><image class="img data-v-89f035d4" src="{{item.icon}}" mode="aspectFill"></image></view><view class="text_26 data-v-89f035d4" style="color:#686868;">{{''+item.title+''}}</view></view></block></view></view>