# 撤回消息时间判断修复

## 🐛 发现的问题

### 1. 注释与代码不一致
- **问题**：注释写的是"3分钟以内的消息才可以撤回"，但代码设置的是5分钟
- **影响**：容易误导开发者

### 2. 时间解析不够稳健
- **问题**：只使用 `replace(/-/g, '/')` 方法处理时间格式
- **影响**：某些时间格式可能解析失败，导致撤回功能异常

### 3. 缺少错误处理
- **问题**：如果 `createTime` 格式异常，会导致程序崩溃
- **影响**：用户体验差，可能导致整个操作面板无法显示

## ✅ 修复方案

### 1. 统一注释和代码
```javascript
const MAX_RECALLABLE_TIME = 5 * 60 * 1000 // 5分钟以内的消息才可以撤回
```

### 2. 添加稳健的时间解析工具函数
```javascript
/**
 * 解析时间字符串为时间戳
 * @param {string|number} timeStr - 时间字符串或时间戳
 * @returns {number} 时间戳（毫秒）
 */
parseTimestamp(timeStr) {
  if (!timeStr) return 0
  
  try {
    // 如果已经是数字类型的时间戳
    if (typeof timeStr === 'number') {
      return timeStr
    }
    
    // 字符串格式处理
    if (typeof timeStr === 'string') {
      // 先尝试直接解析
      let timestamp = new Date(timeStr).getTime()
      
      // 如果解析失败，尝试替换格式后解析
      if (isNaN(timestamp)) {
        timestamp = new Date(timeStr.replace(/-/g, '/')).getTime()
      }
      
      // 如果还是失败，尝试其他格式
      if (isNaN(timestamp)) {
        // 处理可能的其他格式，如 "2024-01-01T10:30:00"
        const isoFormat = timeStr.replace(' ', 'T')
        timestamp = new Date(isoFormat).getTime()
      }
      
      return isNaN(timestamp) ? 0 : timestamp
    }
  } catch (error) {
    console.error('解析时间失败:', error, timeStr)
  }
  
  return 0
}
```

### 3. 简化时间判断逻辑
```javascript
// 使用工具函数解析时间
const timestamp = this.parseTimestamp(item.createTime)

console.log('🚀 ~ init ~ createTime:', item.createTime, 'timestamp:', timestamp)
console.log('🚀 ~ init ~ 时间差:', Date.now() - timestamp, '毫秒')
console.log('🚀 ~ init ~ 是否可撤回:', Date.now() - timestamp < MAX_RECALLABLE_TIME)

if (timestamp > 0 && Date.now() - timestamp < MAX_RECALLABLE_TIME) {
  // 可以撤回的逻辑
}
```

## 🔍 支持的时间格式

修复后的代码支持以下时间格式：

1. **时间戳（数字）**：`1640995200000`
2. **标准日期字符串**：`"2024-01-01 10:30:00"`
3. **ISO格式**：`"2024-01-01T10:30:00"`
4. **带连字符格式**：`"2024-01-01 10:30:00"`（会自动转换为斜杠格式）
5. **其他标准格式**：JavaScript Date 构造函数能识别的格式

## 🚀 调试信息

添加了详细的调试日志，方便排查问题：

```javascript
console.log('🚀 ~ init ~ createTime:', item.createTime, 'timestamp:', timestamp)
console.log('🚀 ~ init ~ 时间差:', Date.now() - timestamp, '毫秒')
console.log('🚀 ~ init ~ 是否可撤回:', Date.now() - timestamp < MAX_RECALLABLE_TIME)
```

## 📝 测试建议

1. **测试不同时间格式**：
   - 确保各种 createTime 格式都能正确解析
   - 测试边界情况（刚好5分钟、超过5分钟）

2. **测试异常情况**：
   - createTime 为 null 或 undefined
   - createTime 为无效的时间字符串
   - createTime 为空字符串

3. **验证撤回逻辑**：
   - 5分钟内的消息应该显示撤回选项
   - 超过5分钟的消息不应该显示撤回选项
   - 只有自己发送的消息才能撤回

## 🎯 预期效果

- ✅ 时间解析更加稳健，支持多种格式
- ✅ 添加了完善的错误处理，避免程序崩溃
- ✅ 提供详细的调试信息，便于问题排查
- ✅ 代码更加清晰，注释与实现保持一致
