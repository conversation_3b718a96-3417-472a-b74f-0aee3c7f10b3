@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.row_.data-v-b1219e00 {
  flex-direction: row-reverse;
}
.text_box.data-v-b1219e00 {
  flex-direction: row-reverse;
}
.text.data-v-b1219e00 {
  position: relative;
  z-index: 99;
  box-sizing: border-box;
  padding: 16rpx 26rpx;
  border-radius: 8rpx;
  background-color: #fff;
  word-break: break-all;
  vertical-align: center;
}
.text_r.data-v-b1219e00 {
  position: relative;
  background-color: #95ec6a;
}
.text_l.data-v-b1219e00 {
  position: relative;
}
.text_r.data-v-b1219e00::after {
  position: absolute;
  z-index: -1;
  content: "";
  top: 26rpx;
  right: -8rpx;
  width: 18rpx;
  height: 18rpx;
  border-radius: 2px;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  background-color: #95ec6a;
}
.text_l.data-v-b1219e00::after {
  position: absolute;
  z-index: -1;
  content: "";
  top: 26rpx;
  left: -8rpx;
  width: 18rpx;
  height: 18rpx;
  border-radius: 2px;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  background-color: #fff;
}
.hover_classr.data-v-b1219e00 {
  background-color: #89d961;
}
.hover_classl.data-v-b1219e00 {
  background-color: #e2e2e2;
}
.hover_classr.data-v-b1219e00::after {
  position: absolute;
  z-index: -1;
  content: "";
  top: 26rpx;
  right: -8rpx;
  width: 18rpx;
  height: 18rpx;
  border-radius: 2px;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  background-color: #89d961;
}
.hover_classl.data-v-b1219e00::after {
  position: absolute;
  z-index: -1;
  content: "";
  top: 26rpx;
  left: -8rpx;
  width: 18rpx;
  height: 18rpx;
  border-radius: 2px;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  background-color: #e2e2e2;
}
