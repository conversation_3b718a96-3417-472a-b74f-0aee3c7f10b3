page {
	height: 100%;
	background-color: #f8f8f8;
}

.chatInterface {
	height: 100%;
	background-color: #f8f8f8;
}

.chatInterface .scroll-view {
	min-height: 100vh;
	padding-left: 20rpx;
	padding-right: 20rpx;
	box-sizing: border-box;
	-webkit-overflow-scrolling: touch;
	padding-bottom: 240rpx;
	background-color: #f8f8f8;
}

.chatInterface .scroll-view .history-loaded {
	font-size: 28rpx;
	height: 60rpx;
	line-height: 60rpx;
	width: 100%;
	text-align: center;
	color: #cccccc;
}

.chatInterface .scroll-view .load {
	font-size: 28rpx;
	height: 60rpx;
	line-height: 60rpx;
	width: 100%;
	text-align: center;
	color: #666;
}

.chatInterface .scroll-view .message-item {
	display: flex;
	margin: 40rpx 0;
}

.chatInterface .scroll-view .message-item .message-item-checkbox {
	height: 80rpx;
	display: flex;
	align-items: center;
}

.chatInterface .scroll-view .message-item .message-item-content {
	flex: 1;
	overflow: hidden;
	display: flex;
}

.name {
	position: relative;
	top: -6rpx;
}

.chatInterface .scroll-view .message-item .message-item-content.self {
	overflow: hidden;
	display: flex;
	justify-content: flex-start;
	flex-direction: row-reverse;
}

.chatInterface .scroll-view .message-item .avatar {
	width: 80rpx;
	height: 80rpx;
	flex-shrink: 0;
	flex-grow: 0;
	border-radius: 10rpx;
	overflow: hidden;
}

.chatInterface .scroll-view .message-item .avatar image {
	width: 100%;
	height: 100%;
}

.chatInterface .scroll-view .content-box {
	margin: 0 20rpx;
	max-width: 520rpx;
}

.chatInterface .scroll-view .content {
	font-size: 34rpx;
	line-height: 44rpx;
}

.chatInterface .scroll-view .content .message-payload {
	display: flex;
	align-items: center;
}

.chatInterface .scroll-view .content .image-content {
	border-radius: 12rpx;
	max-height: 200px;
	background-color: #ececec;
}

.chatInterface .scroll-view .content .text-content {
	padding: 16rpx;
	border-radius: 12rpx;
	color: #000000;
	background: #FFFFFF;
	word-break: break-all;
	text-align: left;
	vertical-align: center;
	display: block;
}

.chatInterface .scroll-view .content .text-content img {
	width: 50rpx;
	height: 50rpx;
}

.chatInterface .scroll-view .content .file-content {
	width: 560rpx;
	height: 152rpx;
	font-size: 34rpx;
	background: white;
	display: flex;
	align-items: center;
	border-radius: 10rpx;
	padding: 0 20rpx;
}

.chatInterface .scroll-view .content .file-content .file-info {
	width: 386rpx;
	height: 132rpx;
}

.chatInterface .scroll-view .content .file-content .file-info .file-name {
	width: 400rpx;
	margin-top: 15rpx;
	font-size: 34rpx;
	text-overflow: ellipsis;
	overflow: hidden;
	display: -webkit-box;
	word-break: break-all;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}

.chatInterface .scroll-view .content .file-content .file-info .file-size {
	width: 200rpx;
	font-size: 34rpx;
	color: #ccc;
}

.chatInterface .scroll-view .content .file-content .file-img {
	width: 100rpx;
	height: 100rpx;
}

.chatInterface .scroll-view .content .pending {
	/* background: url(".~@/static/images/pending.gif") no-repeat center; */
	background: url(".~@/pagesGoEasy/static/images/pending.gif") no-repeat center;
	background-size: 40rpx;
	width: 40rpx;
	height: 40rpx;
	margin-right: 16rpx;
}

.chatInterface .scroll-view .content .send-fail {
	background: url(".~@/pagesGoEasy/static/images/failed.png") no-repeat center;
	background-size: 40rpx;
	width: 40rpx;
	height: 40rpx;
	margin-right: 16rpx;
}

.action-box {
	display: flex;
	backdrop-filter: blur(0.27rpx);
	width: 100%;
	position: fixed;
	bottom: 0;
	left: 0;
	flex-direction: column;
	background-color: #F1F1F1;
}

.action-top {
	display: flex;
	align-items: center;
	box-sizing: border-box;
	background: #F6F6F6;
	backdrop-filter: blur(27.1828px);
	border-top: 1px solid #ECECEC;
	padding: 0 20rpx;
}

.consult-input {
	flex: 1;
	height: 80rpx;
	padding-left: 20rpx;
	margin: 20rpx;
	margin-left: 0;
	border: none;
	outline: none;
	box-sizing: border-box;
	border-radius: 6px;
	background: #FFFFFF;
	font-size: 34rpx;
}

.more {
	width: 62rpx;
	height: 62rpx;
	margin-right: 10rpx;
	display: flex;
}

.send-btn-box {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 110rpx;
	height: 60rpx;
	border-radius: 10rpx;
	background: #608eca;

}

.send-btn-box .btn {
	color: #FFFFFF;
	font-size: 34rpx;
}

.action-bottom {
	height: 420rpx;
	width: 100%;
	padding: 20rpx;
	box-sizing: border-box;
	display: flex;
}



.action-box .action-bottom .more-icon {
	display: flex;
	align-items: center;
	flex-direction: column;
	padding: 0 30rpx;
}

.action-box .action-bottom .more-icon .img-box {
	width: 88rpx;
	height: 88rpx;
}

.action-box .action-bottom .operation-icon {
	width: 60rpx;
	height: 60rpx;
	min-width: 60rpx;
	min-height: 60rpx;
	padding: 25rpx;
	border-radius: 20rpx;
	background: #FFFFFF;
}

.action-box .action-bottom .operation-title {
	font-size: 30rpx;
	line-height: 50rpx;
	color: #82868E;
}

.action-box .action-top .record-input {
	flex: 1;
	width: 480rpx;
	height: 80rpx;
	line-height: 80rpx;
	padding-left: 20rpx;
	margin: 20rpx;
	margin-left: 0;
	border: none;
	outline: none;
	box-sizing: border-box;
	border-radius: 6px;
	background: #cccccc;
	color: #FFFFFF;
	font-size: 34rpx;
	text-align: center;
}

.chatInterface .messageSelector-box {
	display: flex;
	justify-content: center;
	align-items: center;
	backdrop-filter: blur(0.27rpx);
	width: 100%;
	position: fixed;
	bottom: 0;
	left: 0;
	border-radius: 12rpx;
	background: #F6F6F6;
	height: 150rpx;
	padding: 20rpx 0;
	font-size: 34rpx;
}

.chatInterface .messageSelector-box .messageSelector-btn {
	width: 60rpx;
	height: 60rpx;
}

uni-checkbox:not([disabled]) .uni-checkbox-input:hover {
	border-color: #d1d1d1 !important;
}

uni-checkbox .uni-checkbox-input {
	border-radius: 50% !important;
}

/* #ifdef MP-WEIXIN */
checkbox .wx-checkbox-input {
	border-radius: 50% !important;
}

checkbox .wx-checkbox-input.wx-checkbox-input-checked {
	color: #007aff !important;
}

/* #endif */

.chatInterface .action-popup {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	justify-content: center;
	align-items: center;
}

.chatInterface .action-popup .layer {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(51, 51, 51, 0.5);
	z-index: 999;
}

.chatInterface .action-popup .action-list {
	width: 350rpx;
	background: #fff;
	position: relative;
	z-index: 1000;
	border-radius: 20rpx;
	overflow: hidden;
}

.chatInterface .action-popup .action-item {
	text-align: center;
	height: 100rpx;
	line-height: 100rpx;
	font-size: 34rpx;
	color: #000;
	border-bottom: 1px solid #EFEFEF;
}

.chatInterface .action-popup .action-item:last-child {
	border: none;
}

.chatInterface .record-loading {
	position: fixed;
	top: 50%;
	left: 50%;
	width: 300rpx;
	height: 300rpx;
	margin: -150rpx -150rpx;
	background: #262628;
	background: url(".~@/pagesGoEasy/static/images/recording-loading3.gif") no-repeat center;
	background-size: 100%;
	border-radius: 40rpx;
}

.chatInterface .img-layer {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: #000000;
	z-index: 9999;
	padding: 6rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.chatInterface .img-layer uni-image {
	height: 100% !important;
}

.chatInterface .img-layer {
	height: 100% !important;
	width: 100% !important;
}


.chatInterface .video-snapshot {
	position: relative;
	border-radius: 10rpx;
	overflow: hidden;
	font-size: 0px !important;
	line-height: 0px !important;
}

.chatInterface .video-snapshot video {
	max-height: 300rpx;
	max-width: 400rpx;
}

.chatInterface .video-snapshot .video-play-icon {
	position: absolute;
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	background: url(".~@/pagesGoEasy/static/images/play.png") no-repeat center;
	background-size: 100%;
	top: 50%;
	left: 50%;
	margin: -40rpx;
}

.uni-toast {
	background-color: #ffffff !important;
}

.time-lag {
	font-size: 28rpx;
	text-align: center;
	color: #999;
}

.chatInterface .audio-content {
	height: 86rpx;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.chatInterface .audio-facade {
	min-width: 20rpx;
	padding: 6rpx 10rpx;
	line-height: 72rpx;
	background: #FFFFFF;
	font-size: 34rpx;
	border-radius: 14rpx;
	color: #000000;
	display: flex;
	max-width: 500rpx !important;
}

.chatInterface .audio-facade-bg {
	background: url("/pagesGoEasy/static/images/voice2.png") no-repeat center;
	background-size: 30rpx;
	width: 40rpx;
}

.chatInterface .audio-facade-bg.play-icon {
	background: url("/pagesGoEasy/static/images/play.gif") no-repeat center;
	background-size: 34rpx;
}

.message-read {
	color: grey;
	font-size: 34rpx;
	text-align: end;
	height: 36rpx;
}

.message-unread {
	color: #d02129;
	font-size: 34rpx;
	text-align: end;
}

.message-recalled {
	display: flex;
	align-items: center;
	justify-content: center;
	line-height: 56rpx;
	font-size: 28rpx;
	text-align: center;
	color: grey;
}

.message-recalled .message-recalled-self {
	display: flex;
}

.message-recalled .message-recalled-self span {
	margin-left: 10rpx;
	color: #d02129;
}