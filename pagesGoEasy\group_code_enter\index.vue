<template>
	<view class="flex_c_c page">
		<view class="avatar">
			<image class="img" :src="pageData.avatar" mode="aspectFill"></image>
		</view>
		<view class="text_30 bold_ group-name" v-if="pageData.name">群聊：{{ pageData.name }}({{ pageData.group_num }})</view>
		<view class="size_white text_30 icon_ button" @click="onEnter">加入群聊</view>
	</view>
</template>

<script>
import { to, jsonUrl, show } from '@/utils/index.js';
export default {
	data() {
		return {
			pageData: {}
		};
	},
	onLoad(e) {
		const data = jsonUrl(e);
		this.init(data.id);
	},
	methods: {
		async init(id) {
			uni.showLoading({
				title: '加载中',
				mask: true
			});
			const res = await this.API_group();
			uni.hideLoading();
			if (res) {
				let groupIds = res.data.data;
				id = Number(id);
				if (groupIds.includes(id)) {
					show('已在群中');
					to(`/pagesGoEasy/chat_page/index?groupId=${id}`, {}, 'redirectTo');
				} else {
					this.getData(id);
				}
			} else {
				this.getData(id);
			}
		},
		async getData(group_id) {
			const res = await this.API_info(group_id);
			if (res) {
				this.pageData = res.data;
			}
		},
		async onEnter() {
			uni.showLoading({
				title: '加载中',
				mask: true
			});
			const res = await this.API_joinGroup();
			if (res) {
				to(`/pagesGoEasy/chat_page/index?groupId=${this.pageData.id}`, {}, 'redirectTo');
			}
			uni.hideLoading();
		},

		API_group() {
			return new Promise((res) => {
				http.get('Group/member_group_ids', {}, true, (r) => {
					if (r.data.code == 0) return res(r);
					return show(r.data.msg), res(false);
				});
			});
		},

		API_info(group_id) {
			return new Promise((res) => {
				http.get(
					'Group/info',
					{
						group_id
					},
					true,
					(r) => {
						if (r.data.code == 0) return res(r);
						return show(r.data.msg), res(false);
					}
				);
			});
		},
		API_joinGroup() {
			let member_id = [];
			return new Promise((res) => {
				http.get(
					'Group/joinGroup',
					{
						group_id: this.pageData.id,
						member_id: getApp().globalData.currentUser.member_id
					},
					true,
					(r) => {
						if (r.data.code == 0) return res(r);
						return show(r.data.msg), res(false);
					}
				);
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.page {
	position: fixed;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	.avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 8rpx;
		overflow: hidden;
		background-color: #f1f1f1;
	}
	.group-name {
		width: 500rpx;
		text-align: center;
		margin: 20rpx auto;
	}
	.button {
		width: 300rpx;
		height: 80rpx;
		border-radius: 10rpx;
		margin: 200rpx auto;
		background-color: #58be6b;
	}
}
</style>
