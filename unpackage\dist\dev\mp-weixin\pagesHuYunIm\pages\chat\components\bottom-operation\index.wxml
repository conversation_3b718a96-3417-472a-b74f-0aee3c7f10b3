<view class="data-v-0c5cade1"><view data-event-opts="{{[['tap',[['onBottom',['$event']]]]]}}" class="bottom-operation-box data-v-0c5cade1" catchtap="__e"><view hidden="{{!(keyboardHeight)}}" class="flex_r line-break data-v-0c5cade1"><view data-event-opts="{{[['tap',[['lineBreak',['$event']]]]]}}" class="icon_ text_28 color__ line-break-box data-v-0c5cade1" bindtap="__e"><view class="icon_ line-break-icon data-v-0c5cade1"><image class="img data-v-0c5cade1" src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTAgMGgxMDI0djEwMjRIMHoiIGZpbGw9IiMyYzJjMmMiIGZpbGwtb3BhY2l0eT0iLjAxIi8+PHBhdGggZD0iTTY4Mi42NjcgMTI4YTI5OC42NjcgMjk4LjY2NyAwIDAgMSAxMC4yNCA1OTcuMTYzbC0xMC4yNC4xN2gtNTEyYTQyLjY2NyA0Mi42NjcgMCAwIDEtNC45OTItODUuMDM0bDQuOTkyLS4yOTloNTEyYTIxMy4zMzMgMjEzLjMzMyAwIDAgMCA5LjI1OC00MjYuNDUzbC05LjI1OC0uMjE0aC01MTJhNDIuNjY3IDQyLjY2NyAwIDAgMS00Ljk5Mi04NS4wMzRsNC45OTItLjI5OWg1MTJ6IiBmaWxsPSIjMmMyYzJjIi8+PHBhdGggZD0iTTI0Ny4xNjggNDYwLjUwMWE0Mi42NjcgNDIuNjY3IDAgMCAxIDYzLjg3MiA1Ni4zMmwtMy41NDEgNC4wMTEtMTYwIDE2MCAxNTguMTY1IDE0MC42M2E0Mi42NjcgNDIuNjY3IDAgMCAxIDYuODI3IDU1Ljk3OGwtMy4yODYgNC4yNjdhNDIuNjY3IDQyLjY2NyAwIDAgMS01NS45NzggNi44MjZsLTQuMjY3LTMuMzI4LTE5Mi0xNzAuNjY2YTQyLjY2NyA0Mi42NjcgMCAwIDEtNS4yNDgtNTguMTU1bDMuNDEzLTMuODgzIDE5Mi0xOTJ6IiBmaWxsPSIjMmMyYzJjIi8+PC9zdmc+" mode="aspectFill"></image></view>换行</view></view><view class="flex_r bottom-operation data-v-0c5cade1"><view data-event-opts="{{[['tap',[['onKeyboard',['$event']]]]]}}" class="icon_ bottom-operation-icon data-v-0c5cade1" bindtap="__e"><image class="img data-v-0c5cade1" src="{{isKeyboard?a:a_b}}" mode="aspectFill"></image></view><view style="width:10rpx;" class="data-v-0c5cade1"></view><view class="flex_c_c flex1 data-v-0c5cade1"><block wx:if="{{isKeyboard}}"><view class="bottom-operation-input data-v-0c5cade1"><textarea class="input data-v-0c5cade1" auto-height="true" confirm-type="send" type="text" focus="{{isFocus}}" maxlength="{{-1}}" adjust-position="{{false}}" confirm-hold="{{true}}" show-confirm-bar="{{false}}" data-event-opts="{{[['input',[['__set_model',['','text','$event',[]]],['input',['$event']]]],['confirm',[['sendingText',['$event']]]],['focus',[['focus',['$event']]]],['blur',[['handleBlur',['$event']]]],['keyboardheightchange',[['keyboardheightchange',['$event']]]]]}}" value="{{text}}" bindinput="__e" bindconfirm="__e" bindfocus="__e" bindblur="__e" bindkeyboardheightchange="__e"></textarea></view></block><block wx:else><view data-event-opts="{{[['touchend',[['touchend',['$event']]]],['touchmove',[['touchmove',['$event']]]],['touchstart',[['touchstart',['$event']]]]]}}" class="icon_ text_32 bold_ bottom-operation-input data-v-0c5cade1" bindtouchend="__e" bindtouchmove="__e" bindtouchstart="__e"><view class="data-v-0c5cade1">按住</view><view style="width:10rpx;" class="data-v-0c5cade1"></view><view class="data-v-0c5cade1">说话</view></view></block><block wx:if="{{isQuote}}"><view class="icon_ text_26 quote data-v-0c5cade1"><view class="flex1 quote-row data-v-0c5cade1"><block wx:if="{{quoteSource.type==='image'||quoteSource.type==='image_transmit'}}"><view class="data-v-0c5cade1"><m-image vue-id="7f398f45-1" value="{{quoteSource}}" class="data-v-0c5cade1" bind:__l="__l"></m-image></view></block><block wx:else><block wx:if="{{quoteSource.type==='voice'}}"><view class="data-v-0c5cade1"><m-audio vue-id="7f398f45-2" value="{{quoteSource}}" class="data-v-0c5cade1" bind:__l="__l"></m-audio></view></block><block wx:else><block wx:if="{{quoteSource.type==='text'||quoteSource.type==='text_quote'}}"><view class="data-v-0c5cade1"><m-text vue-id="7f398f45-3" value="{{quoteSource}}" class="data-v-0c5cade1" bind:__l="__l"></m-text></view></block><block wx:else><view class="data-v-0c5cade1"><m-other vue-id="7f398f45-4" value="{{quoteSource}}" class="data-v-0c5cade1" bind:__l="__l"></m-other></view></block></block></block></view><view data-event-opts="{{[['tap',[['cancelQuote',['$event']]]]]}}" class="quote-icon data-v-0c5cade1" bindtap="__e"><image class="img data-v-0c5cade1" src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTg5NS4xIDUxMi40Yy0uNi0xMDIuMy00MS0xOTguNC0xMTMuOC0yNzAuNy03Mi44LTcyLjMtMTY5LjItMTEyLTI3MS41LTExMS45LTEwMi4zLjItMTk4LjMgNDAuMi0yNzAuMyAxMTIuN1MxMjguMiA0MTEuMyAxMjguOCA1MTMuNmMuNiAxMDIuMyA0MSAxOTguNCAxMTMuOCAyNzAuN3MxNjkuMiAxMTIgMjcxLjUgMTExLjhjMTAyLjQtLjEgMTk4LjUtNDAuMSAyNzAuNC0xMTIuNiA3Mi03Mi41IDExMS4zLTE2OC43IDExMC42LTI3MS4xek02MjkgNjY3LjhsLTExNi44LTExNi0xMTYgMTE2LjhjLTEwLjcgMTAuOC0yOCAxMC44LTM4LjguMS0xMC43LTEwLjctMTAuOC0yOC0uMS0zOC44bDExNS45LTExNi44LTExNi44LTExNS45Yy0xMC43LTEwLjctMTAuOC0yOC0uMS0zOC44IDEwLjctMTAuNyAyOC0xMC44IDM4LjgtLjFsMTE2LjggMTE1LjkgMTE1LjktMTE2LjhjMTAuNy0xMC43IDI4LTEwLjggMzguOC0uMSAxMC43IDEwLjcgMTAuOCAyOCAuMSAzOC44TDU1MC44IDUxMi45bDExNi44IDExNS45YzEwLjggMTAuNyAxMC44IDI4IC4xIDM4LjgtMTAuNiAxMC44LTI4IDEwLjgtMzguNy4yem0wIDAiIGRhdGEtc3BtLWFuY2hvci1pZD0iYTMxM3guc2VhcmNoX2luZGV4LjAuaTExLjFkYjQzYTgxVE1Sd1EyIiBjbGFzcz0ic2VsZWN0ZWQiIGZpbGw9IiNhMWExYTEiLz48L3N2Zz4=" mode="aspectFill"></image></view></view></block></view><view style="width:10rpx;" class="data-v-0c5cade1"></view><view data-event-opts="{{[['tap',[['tapEmoji',['$event']]]]]}}" class="icon_ bottom-operation-icon data-v-0c5cade1" bindtap="__e"><image class="img data-v-0c5cade1" src="{{b}}" mode="aspectFill"></image></view><block wx:if="{{$root.g0}}"><view data-event-opts="{{[['tap',[['sendingText',['$event']]]]]}}" class="icon_ bottom-operation-icon data-v-0c5cade1" bindtap="__e"><button class="send-btn data-v-0c5cade1">发送</button></view></block><block wx:else><view data-event-opts="{{[['tap',[['tapMore',['$event']]]]]}}" class="icon_ bottom-operation-icon data-v-0c5cade1" bindtap="__e"><image class="img data-v-0c5cade1" src="{{c}}" mode="aspectFill"></image></view></block></view><view class="data-v-0c5cade1"><emoji bind:onEmoji="__e" bind:deleteFn="__e" bind:sendingText="__e" bind:sendingEmojiPack="__e" bind:input="__e" vue-id="7f398f45-5" value="{{isEmoji}}" data-event-opts="{{[['^onEmoji',[['onEmoji']]],['^deleteFn',[['deleteFn']]],['^sendingText',[['sendingText']]],['^sendingEmojiPack',[['sendingEmojiPack']]],['^input',[['__set_model',['','isEmoji','$event',[]]]]]]}}" class="data-v-0c5cade1" bind:__l="__l"></emoji></view><view class="data-v-0c5cade1"><more bind:onMore="__e" bind:input="__e" vue-id="7f398f45-6" value="{{isMore}}" data-event-opts="{{[['^onMore',[['onMore']]],['^input',[['__set_model',['','isMore','$event',[]]]]]]}}" class="data-v-0c5cade1" bind:__l="__l"></more></view><view class="keyboard data-v-0c5cade1" style="{{'height:'+(keyboardHeight+'px')+';'}}"></view><block wx:if="{{keyboardHeight===0}}"><view class="data-v-0c5cade1"><m-bottom-paceholder vue-id="7f398f45-7" class="data-v-0c5cade1" bind:__l="__l"></m-bottom-paceholder></view></block><m-recorder vue-id="7f398f45-8" isCancel="{{isCancel}}" value="{{isRecorder}}" data-event-opts="{{[['^recorderTop',[['recorderTop']]],['^touchend',[['touchend']]],['^input',[['__set_model',['','isRecorder','$event',[]]]]]]}}" bind:recorderTop="__e" bind:touchend="__e" bind:input="__e" class="data-v-0c5cade1" bind:__l="__l"></m-recorder></view><member-selection-loading vue-id="7f398f45-9" title="选择提醒的人" group_id="{{to.id}}" data-ref="memberSelectionLoadingRef" data-event-opts="{{[['^itemclick',[['itemclick']]]]}}" bind:itemclick="__e" class="data-v-0c5cade1 vue-ref" bind:__l="__l"></member-selection-loading></view>