@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-cd712850 {
  position: fixed;
  z-index: 1;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}
.navigationBar-box.data-v-cd712850 {
  width: 100%;
  background-color: #ececec;
}
.navigationBar-box .navigationBar.data-v-cd712850 {
  width: 100%;
}
.navigationBar-box .navigationBar .navigationBar-icon.data-v-cd712850 {
  position: relative;
  width: 40rpx;
  height: 40rpx;
  margin-left: 30rpx;
}
.navigationBar-box .navigationBar .navigationBar-icon .dropDown.data-v-cd712850 {
  position: absolute;
  width: 260rpx;
  min-height: 0rpx;
  top: -20rpx;
  right: -14rpx;
  transition: 0.3s;
  visibility: 0;
  opacity: 0;
  -webkit-transform: translateY(50rpx);
          transform: translateY(50rpx);
  color: #000;
}
.navigationBar-box .navigationBar .navigationBar-icon .dropDown .list.data-v-cd712850 {
  width: 100%;
  height: 0rpx;
  font-size: 0rpx;
}
.navigationBar-box .navigationBar .navigationBar-icon .dropDown .list .list-icon.data-v-cd712850 {
  width: 0rpx;
  height: 0rpx;
}
.navigationBar-box .navigationBar .navigationBar-icon .dropDown.data-v-cd712850::after {
  transition: 0.3s;
  content: "";
  display: inline-block;
  border: 15rpx solid #4c4c4c;
  border-left-color: transparent;
  border-right-color: transparent;
  border-top-color: transparent;
  position: absolute;
  top: -28rpx;
  right: 26rpx;
}
.navigationBar-box .navigationBar .navigationBar-icon .active.data-v-cd712850 {
  position: absolute;
  z-index: 99;
  min-height: 120rpx;
  padding-top: 10rpx;
  -webkit-transform: translateY(100rpx);
          transform: translateY(100rpx);
  opacity: 1;
  visibility: 1;
  background-color: #4c4c4c;
  border-radius: 8rpx;
  box-shadow: 0 0 20rpx #dcdcdc;
}
.navigationBar-box .navigationBar .navigationBar-icon .active .list.data-v-cd712850 {
  box-sizing: border-box;
  padding: 0 30rpx;
  transition: 0.3s;
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 30rpx;
}
.navigationBar-box .navigationBar .navigationBar-icon .active .list .list-icon.data-v-cd712850 {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}
.navigationBar-box .navigationBar .navigationBar-icon2.data-v-cd712850 {
  width: 50rpx;
  height: 50rpx;
  margin-right: 34rpx;
}
.navigationBar-box .navigationBar .navigationBar-text.data-v-cd712850 {
  font-size: 16px;
}
.conversations.data-v-cd712850 {
  box-sizing: border-box;
  width: 750rpx;
  height: 0;
}
.conversations .scroll-item-box.data-v-cd712850 {
  width: 750rpx;
  height: 140rpx;
  margin-bottom: 0px;
}
.conversations .scroll-item-box .scroll-item.data-v-cd712850 {
  position: relative;
  height: 100rpx;
  margin: 25rpx 0 15rpx 0;
}
.conversations .scroll-item-box .scroll-item .item-head.data-v-cd712850 {
  position: relative;
  width: 90rpx;
  height: 90rpx;
  margin: 0 20rpx 0 30rpx;
}
.conversations .scroll-item-box .scroll-item .item-head .item-head-img.data-v-cd712850 {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  overflow: hidden;
  background-color: #f1f1f1;
}
.conversations .scroll-item-box .scroll-item .item-head .item-head_unread.data-v-cd712850 {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #fa5251;
}
.conversations .scroll-item-box .scroll-item .scroll-item_info.data-v-cd712850 {
  position: relative;
  box-sizing: border-box;
  height: 90rpx;
  width: 590rpx;
}
.conversations .scroll-item-box .scroll-item .scroll-item_info .item_info-text.data-v-cd712850 {
  color: #b6b6b6;
}
.conversations .scroll-item-box .scroll-item .scroll-item_info .m-line.data-v-cd712850 {
  position: absolute;
  bottom: -25rpx;
  width: calc(100% + 80rpx);
}
.conversations .scroll_item_box.data-v-cd712850 {
  background-color: #ededed;
}
.conversations .hover_classr.data-v-cd712850 {
  background-color: #e7e7e7;
}
.no-conversation.data-v-cd712850 {
  width: 300rpx;
  height: 300rpx;
  margin: 150rpx auto 20rpx auto;
}
