<view class="flex_c row data-v-60de8d8d"><view data-event-opts="{{[['tap',[['onClick',['$event']]]]]}}" class="{{['flex_r','text-box','data-v-60de8d8d',(isMy)?'text_box':'']}}" catchtap="__e"><view class="{{['text','data-v-60de8d8d',isMy?'text_r':'text_l']}}"><view class="flex_c_c article data-v-60de8d8d"><view class="flex_r fa_c article-infr data-v-60de8d8d"><view class="article-infr-img data-v-60de8d8d"><image class="img data-v-60de8d8d" src="{{value.payload.share_image}}" mode="aspectFill"></image></view><view class="text_22 color__ article-infr-text data-v-60de8d8d">{{''+value.payload.title+''}}</view></view><view class="text_30 nowrap_ article-title data-v-60de8d8d">{{''+value.payload.short_title+''}}</view><view class="article-img data-v-60de8d8d"><image class="img data-v-60de8d8d" src="{{value.payload.share_image}}" mode="aspectFill"></image></view><view class="m-line data-v-60de8d8d"><m-line vue-id="8a18b5ce-1" color="#f0f0f0" length="100%" hairline="{{true}}" class="data-v-60de8d8d" bind:__l="__l"></m-line></view><view class="flex_r fa_c article-b data-v-60de8d8d"><view class="article-b-icon data-v-60de8d8d"></view><view class="text_20 color__ article-b-text data-v-60de8d8d">xxxx</view></view></view></view></view></view>