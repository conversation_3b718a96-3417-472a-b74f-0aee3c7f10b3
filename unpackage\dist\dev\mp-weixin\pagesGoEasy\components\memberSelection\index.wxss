
 ::-webkit-scrollbar {
	width: 0;
	height: 0;
	color: transparent;
	display: none;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.next.data-v-6958b26b {
  position: relative;
  width: 100%;
  height: 82vh;
  background-color: #fff;
  overflow: hidden;
  border-radius: 20rpx 20rpx 0 0;
}
.next .top.data-v-6958b26b {
  width: 100%;
  height: 250rpx;
}
.next .top .top-title.data-v-6958b26b {
  width: calc(100% - 60rpx);
  height: 120rpx;
  margin: 0 auto;
}
.next .top .top-title .top-title-icon.data-v-6958b26b {
  width: 44rpx;
  height: 44rpx;
}
.next .top .search.data-v-6958b26b {
  position: relative;
  width: calc(100% - 40rpx);
  height: 80rpx;
  margin: 0 auto;
  border-radius: 14rpx;
  background-color: #ededed;
}
.next .top .search .search-input.data-v-6958b26b {
  box-sizing: border-box;
  padding: 0 20rpx;
  position: absolute;
  z-index: 3;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.next .top .search .search-input input.data-v-6958b26b {
  width: 100%;
  height: 100%;
  text-align: center;
}
.next .top .search .search-icon.data-v-6958b26b {
  width: 34rpx;
  height: 34rpx;
  margin-right: 16rpx;
}
.next .top .search .search-text.data-v-6958b26b {
  color: #9b9b9b;
}
.next-list.data-v-6958b26b {
  position: relative;
  width: 100%;
  height: 0;
  box-sizing: border-box;
}
.next-list .next-scroll-left.data-v-6958b26b {
  height: 100%;
}
.next-list .next-scroll-left .left-list.data-v-6958b26b {
  height: auto;
}
.next-list .next-scroll-left .left-list .left-item-title.data-v-6958b26b {
  width: calc(100% - 24rpx);
  height: 60rpx;
  padding-left: 24rpx;
  text-align: left;
  line-height: 60rpx;
  font-size: 30rpx;
  color: #666666;
}
.next-list .next-scroll-left .left-list .left-item-card.data-v-6958b26b {
  width: 100%;
  height: 112rpx;
  background-color: #ffffff;
  box-sizing: border-box;
  padding-left: 24rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.next-list .next-scroll-left .left-list .left-item-card .left-item-card-img.data-v-6958b26b {
  width: 80rpx;
  min-width: 80rpx;
  height: 80rpx;
  background-color: #cfcfcf;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  overflow: hidden;
}
.next-list .next-scroll-left .left-list .left-item-card .img-info.data-v-6958b26b {
  background: none;
  border: solid #f0f0f0 1rpx;
}
.next-list .next-scroll-left .left-list .left-item-card .left-item-card-info.data-v-6958b26b {
  width: 100%;
  margin-left: 20rpx;
  height: 100%;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-direction: column;
}
.next-list .next-scroll-left .left-list .left-item-card .left-item-card-info .left-item-card-name.data-v-6958b26b {
  font-size: 30rpx;
  line-height: 30rpx;
  color: #333333;
}
.next-list .next-scroll-left .left-list .left-item-card .left-item-card-info .left-item-card-phone.data-v-6958b26b {
  margin-top: 14rpx;
  font-size: 28rpx;
  line-height: 28rpx;
  color: #999999;
}
.next-list .next-scroll-right.data-v-6958b26b {
  position: absolute;
  right: 0rpx;
  top: 40%;
  -webkit-transform: translateY(-47%);
          transform: translateY(-47%);
  z-index: 999 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.next-list .next-scroll-right .next-scroll-right-top.data-v-6958b26b {
  width: 32rpx;
  height: 32rpx;
  margin-right: 14rpx;
  z-index: 999 !important;
}
.next-list .next-scroll-right .next-scroll-right-name.data-v-6958b26b {
  width: 32rpx;
  padding-right: 14rpx;
  height: 28rpx;
  font-size: 22rpx;
  color: #515151;
  line-height: 28rpx;
  margin-top: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.next-list .next-scroll-right .next-scroll-right-select.data-v-6958b26b {
  padding: 0;
  margin-right: 14rpx;
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  background: #0cbf5e;
  color: #ffffff;
}
.next-list .no-data.data-v-6958b26b {
  width: 100%;
}
.next-list .no-data .no-data-img.data-v-6958b26b {
  width: 200rpx;
  height: 200rpx;
  margin-top: 100rpx;
  margin-bottom: 20rpx;
}
