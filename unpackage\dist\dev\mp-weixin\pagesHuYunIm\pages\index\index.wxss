@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-6441f5a4 {
  height: 100vh;
  background-color: #f7f7f7;
  display: flex;
  flex-direction: column;
}
/* 会话列表样式 */
.chat-list.data-v-6441f5a4 {
  flex: 1;
  background-color: #ffffff;
}
.list-item.data-v-6441f5a4 {
  display: flex;
  padding: 24rpx 32rpx;
  border-bottom: 1px solid #f0f0f0;
  background-color: #ffffff;
  transition: background-color 0.2s;
}
.list-item.data-v-6441f5a4:active {
  background-color: #f5f5f5;
}
.list-item.data-v-6441f5a4:last-child {
  border-bottom: none;
}
/* 头像容器样式 */
.avatar-container.data-v-6441f5a4 {
  position: relative;
  margin-right: 24rpx;
}
.avatar-container .avatar.data-v-6441f5a4 {
  width: 96rpx;
  height: 96rpx;
  border-radius: 12rpx;
  background-color: #f0f0f0;
}
.avatar-container .unread-badge.data-v-6441f5a4 {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background-color: #ff4444;
  border-radius: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #ffffff;
}
.avatar-container .unread-badge .unread-count.data-v-6441f5a4 {
  color: #ffffff;
  font-size: 20rpx;
  font-weight: bold;
  line-height: 1;
  padding: 0 8rpx;
}
/* 内容区域样式 */
.content.data-v-6441f5a4 {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
}
.title-row.data-v-6441f5a4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}
.title-row .name.data-v-6441f5a4 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.title-row .time.data-v-6441f5a4 {
  font-size: 24rpx;
  color: #999999;
  margin-left: 16rpx;
  flex-shrink: 0;
}
.message-row.data-v-6441f5a4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.message-row .last-message.data-v-6441f5a4 {
  flex: 1;
  min-width: 0;
}
.message-row .last-message .message-text.data-v-6441f5a4 {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.message-row .last-message .message-text.placeholder.data-v-6441f5a4 {
  color: #cccccc;
  font-style: italic;
}
.message-row .message-status.data-v-6441f5a4 {
  margin-left: 16rpx;
}
.message-row .message-status .mute-icon.data-v-6441f5a4 {
  font-size: 24rpx;
  color: #999999;
}
/* 空状态样式 */
.empty-state.data-v-6441f5a4 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
}
.empty-state .empty-icon.data-v-6441f5a4 {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.3;
}
.empty-state .empty-text.data-v-6441f5a4 {
  font-size: 32rpx;
  color: #666666;
  margin-bottom: 16rpx;
}
.empty-state .empty-desc.data-v-6441f5a4 {
  font-size: 28rpx;
  color: #999999;
}
/* 连接状态指示器 */
.connection-status.data-v-6441f5a4 {
  position: fixed;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  z-index: 1000;
}
.connection-status .status-text.data-v-6441f5a4 {
  font-size: 28rpx;
}
/* 响应式适配 */
@media screen and (max-width: 750rpx) {
.list-item.data-v-6441f5a4 {
    padding: 20rpx 24rpx;
}
.avatar-container.data-v-6441f5a4 {
    margin-right: 20rpx;
}
.avatar-container .avatar.data-v-6441f5a4 {
    width: 80rpx;
    height: 80rpx;
}
.title-row .name.data-v-6441f5a4 {
    font-size: 30rpx;
}
.message-row .message-text.data-v-6441f5a4 {
    font-size: 26rpx;
}
}
