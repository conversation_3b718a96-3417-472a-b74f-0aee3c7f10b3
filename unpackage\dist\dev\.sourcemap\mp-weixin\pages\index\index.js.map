{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pages/index/index.vue?3df3", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pages/index/index.vue?6667", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pages/index/index.vue?0a52", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pages/index/index.vue?987d", "uni-app:///pages/index/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pages/index/index.vue?4345", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pages/index/index.vue?1c2e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "title", "userMap", "users", "onLoad", "methods", "to", "initMqtt", "onConnect", "console", "onMessage", "onReconnect", "onError", "onEnd", "mqttClient", "handleMqttMessage", "chatMsg", "onUnload"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACwL;AACxL,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA2sB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACY/tB;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;EAAA,CACA;EACAC;IACAC;IAEA;AACA;AACA;IACAC;MAAA;MACA;MACA,8BACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA,CACA;;MAEA;QACAC;UACAC;QACA;QACAC;UACA;QACA;QACAC;UACAF;QACA;QACAG;UACAH;QACA;QACAI;UACAJ;QACA;MACA;;MAEA;MACAK;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;QACA;QACA;QACA;UACAC;QACA;QACA;QACA;UACA;YACA;YACA;YACA;UACA;QACA;QACA;QACAP;MACA;IACA;EACA;EAEAQ;IACA;IACAH;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC5FA;AAAA;AAAA;AAAA;AAAwgC,CAAgB,w7BAAG,EAAC,C;;;;;;;;;;;ACA5hC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<image class=\"logo\" src=\"/static/logo.png\"></image>\r\n\t\t<view class=\"text-area\">\r\n\t\t\t<text class=\"title\">{{ title }}</text>\r\n\t\t</view>\r\n\t\t<button @click=\"to('/pagesGoEasy/sessionList/index')\">会话页面</button>\r\n\t\t<button @click=\"to('/pagesHuYunIm/pages/index/index')\" class=\"mqtt-btn\">MQTT工具包</button>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { to } from '@/utils/index.js'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\ttitle: 'Hello',\r\n\t\t\tuserMap: {}, // 用户映射表\r\n\t\t\tusers: [] // 用户列表\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\t// this.initMqtt()\r\n\t},\r\n\tmethods: {\r\n\t\tto,\r\n\r\n\t\t/**\r\n\t\t * 初始化MQTT连接\r\n\t\t */\r\n\t\tinitMqtt() {\r\n\t\t\t// 使用配置工具创建用户信息\r\n\t\t\tconst userInfo = createUserInfo(\r\n\t\t\t\t'1921822887908581377', // userId\r\n\t\t\t\t'范发发', // nickname\r\n\t\t\t\t'hbs119', // channelCode\r\n\t\t\t\t'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImhiczExOSIsImNsaWVudGlkIjoiMTkyMTgyMjg4NzkwODU4MTM3NyIsImV4cCI6MTc1NTA1MzA0OH0.mXmgwG-lPKr3Krrva_i9k1B4PN45QCwr_GFkFFjg4hU', // token\r\n\t\t\t\t'https://dummyimage.com/200x200/3c9cff/fff', // avatar\r\n\t\t\t\t'DEV' // 环境\r\n\t\t\t)\r\n\r\n\t\t\tconst callbacks = {\r\n\t\t\t\tonConnect: () => {\r\n\t\t\t\t\tconsole.log('MQTT连接成功，可以开始聊天了')\r\n\t\t\t\t},\r\n\t\t\t\tonMessage: (_, mqttMsg) => {\r\n\t\t\t\t\tthis.handleMqttMessage(mqttMsg)\r\n\t\t\t\t},\r\n\t\t\t\tonReconnect: () => {\r\n\t\t\t\t\tconsole.log('MQTT重连中...')\r\n\t\t\t\t},\r\n\t\t\t\tonError: (error) => {\r\n\t\t\t\t\tconsole.error('MQTT连接错误:', error)\r\n\t\t\t\t},\r\n\t\t\t\tonEnd: () => {\r\n\t\t\t\t\tconsole.log('MQTT连接已断开')\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// 使用工具类连接MQTT\r\n\t\t\tmqttClient.connect(userInfo, callbacks)\r\n\t\t},\r\n\t\t/**\r\n\t\t * 处理MQTT消息\r\n\t\t * @param {Object} mqttMsg - MQTT消息对象\r\n\t\t */\r\n\t\thandleMqttMessage(mqttMsg) {\r\n\t\t\tif (mqttMsg.command === MESSAGE_TYPES.CHAT_MSG) {\r\n\t\t\t\tconst chatMsg = mqttMsg.data\r\n\t\t\t\t// 设置用户昵称\r\n\t\t\t\tif (this.userMap.hasOwnProperty(chatMsg.userId)) {\r\n\t\t\t\t\tchatMsg.nickname = this.userMap[chatMsg.userId].nickname\r\n\t\t\t\t}\r\n\t\t\t\t// 更新用户列表中的消息信息\r\n\t\t\t\tfor (let i = 0; i < this.users.length; i++) {\r\n\t\t\t\t\tif (this.users[i].id === chatMsg.groupId) {\r\n\t\t\t\t\t\tthis.users[i].lastMsg = chatMsg\r\n\t\t\t\t\t\tthis.users[i].updateTime = chatMsg.createTime\r\n\t\t\t\t\t\tthis.users[i].notReadNum = this.users[i].notReadNum + 1\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// 消息通知\r\n\t\t\t\tconsole.log('收到聊天消息:', chatMsg)\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\r\n\tonUnload() {\r\n\t\t// 页面卸载时断开MQTT连接\r\n\t\tmqttClient.disconnect()\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n.content {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.logo {\r\n\theight: 200rpx;\r\n\twidth: 200rpx;\r\n\tmargin-top: 200rpx;\r\n\tmargin-left: auto;\r\n\tmargin-right: auto;\r\n\tmargin-bottom: 50rpx;\r\n}\r\n\r\n.text-area {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n}\r\n\r\n.title {\r\n\tfont-size: 36rpx;\r\n\tcolor: #8f8f94;\r\n}\r\n\r\n.mqtt-btn {\r\n\tmargin-top: 20rpx;\r\n\tbackground-color: #007aff;\r\n\tcolor: white;\r\n\tborder-radius: 10rpx;\r\n\tpadding: 20rpx;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755048918224\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}