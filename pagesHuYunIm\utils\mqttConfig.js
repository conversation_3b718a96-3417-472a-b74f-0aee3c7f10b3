/**
 * MQTT配置文件
 * 统一管理MQTT连接相关的配置信息
 */

// MQTT服务器配置
export const MQTT_CONFIG = {
  // WebSocket连接地址
  WS_URL: {
    // 开发环境
    DEV: 'wxs:mqtt.r.cdhuyun.com:443/mqtt',
    // 生产环境
    PROD: 'wxs:mqtt.r.cdhuyun.com:443/mqtt',
    // 测试环境
    TEST: 'wxx:mqtt.r.cdhuyun.com:8084/mqtt'
  },

  // 默认连接选项
  DEFAULT_OPTIONS: {
    clean: false,
    rejectUnauthorized: false,
    keepalive: 60,
    connectTimeout: 30 * 1000,
    reconnectPeriod: 1000,
    protocolVersion: 4
  },

  // 心跳配置
  PING: {
    INTERVAL: 10000, // 10秒
    TOPIC_PREFIX: '/chat/server',
    TOPIC_SUFFIX: '/ping',
    MESSAGE: '1'
  },

  // 订阅配置
  SUBSCRIBE: {
    USER_TOPIC_PREFIX: '/chat/client',
    DEFAULT_QOS: 0
  },

  // 发布配置
  PUBLISH: {
    DEFAULT_QOS: 0,
    DEFAULT_RETAIN: false
  }
}

// 主题模板
export const TOPIC_TEMPLATES = {
  // 用户频道
  USER_CHANNEL: (userId) => `/chat/client/${userId}`,

  // 心跳主题
  PING: (userId) => `/chat/server/${userId}/ping`,

  // 聊天室
  CHAT_ROOM: (roomId) => `/chat/room/${roomId}`,

  // 私聊
  PRIVATE_CHAT: (fromUserId, toUserId) => `/chat/private/${fromUserId}/${toUserId}`,

  // 群聊
  GROUP_CHAT: (userId) => `/chat/server/${userId}/msg`,

  // 系统通知
  SYSTEM_NOTIFY: (userId) => `/system/notify/${userId}`,

  // 在线状态
  ONLINE_STATUS: (userId) => `/status/online/${userId}`
}

// 消息类型常量
export const MESSAGE_TYPES = {
  CHAT_MSG: 'chatMsg',
  SYSTEM_MSG: 'systemMsg',
  TYPING: 'typing',
  READ_RECEIPT: 'readReceipt',
  ONLINE_STATUS: 'onlineStatus',
  HEARTBEAT: 'heartbeat'
}

// 获取当前环境的WebSocket URL
export function getWsUrl(env = 'DEV') {
  return MQTT_CONFIG.WS_URL[env] || MQTT_CONFIG.WS_URL.DEV
}

// 创建用户信息对象
export function createUserInfo(userId, nickname, channelCode, token, avatar = '', env = 'DEV') {
  return {
    userId,
    nickname,
    channelCode,
    avatar,
    wsUrl: getWsUrl(env),
    token
  }
}

// 创建连接选项
export function createConnectOptions(userInfo, customOptions = {}) {
  return {
    clientId: userInfo.userId,
    username: userInfo.channelCode,
    password: userInfo.token,
    ...MQTT_CONFIG.DEFAULT_OPTIONS,
    ...customOptions
  }
}

// 创建标准消息格式
export function createMessage(command, data, extra = {}) {
  return {
    command,
    data,
    timestamp: Date.now(),
    ...extra
  }
}

// 创建聊天消息
export function createChatMessage(content, userId, nickname, groupId = null, messageType = 'text') {
  return createMessage(MESSAGE_TYPES.CHAT_MSG, {
    content,
    userId,
    nickname,
    groupId,
    messageType,
    createTime: Date.now()
  })
}

// 创建系统消息
export function createSystemMessage(content, type = 'info') {
  return createMessage(MESSAGE_TYPES.SYSTEM_MSG, {
    content,
    type,
    createTime: Date.now()
  })
}

// 验证用户信息
export function validateUserInfo(userInfo) {
  const required = ['userId', 'channelCode', 'token', 'wsUrl']
  const missing = required.filter((field) => !userInfo[field])

  if (missing.length > 0) {
    throw new Error(`用户信息缺少必要字段: ${missing.join(', ')}`)
  }

  return true
}

// 默认导出配置对象
export default {
  MQTT_CONFIG,
  TOPIC_TEMPLATES,
  MESSAGE_TYPES,
  getWsUrl,
  createUserInfo,
  createConnectOptions,
  createMessage,
  createChatMessage,
  createSystemMessage,
  validateUserInfo
}
