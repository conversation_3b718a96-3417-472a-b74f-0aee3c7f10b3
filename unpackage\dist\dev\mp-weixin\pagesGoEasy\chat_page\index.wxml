<view id="{{page_font_size}}" class="data-v-23291a5a"><view data-event-opts="{{[['touchmove',[['touchmove',['$event']]]]]}}" class="flex_c page data-v-23291a5a" bindtouchmove="__e"><view class="navigationRef data-v-23291a5a"><navigation vue-id="41fab1ec-1" groupCount="{{groupCount}}" title="{{pagueObj.name}}" group_id="{{pagueObj.id}}" data-ref="navigationRef" class="data-v-23291a5a vue-ref" bind:__l="__l"></navigation></view><scroll-view class="flex1 scroll-Y data-v-23291a5a" id="scroll-view" lower-threshold="100" scroll-y="{{true}}" scroll-with-animation="{{true}}" scroll-top="{{scroll_top}}" data-event-opts="{{[['tap',[['onPage',['$event']]]],['scroll',[['scroll',['$event']]]],['scrolltoupper',[['scrolltoupper',['$event']]]],['scrolltolower',[['scrolltolower',['$event']]]]]}}" catchtap="__e" bindscroll="__e" bindscrolltoupper="__e" bindscrolltolower="__e"><block wx:if="{{reserveHeight>0}}"><view class="scroll-view-str data-v-23291a5a" style="{{'height:'+(reserveHeight+'px')+';'}}"></view></block><view class="messageList_ data-v-23291a5a"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="messageId"><block wx:if="{{!item.$orig.isHide}}"><view class="z_index2 data-v-23291a5a" style="transform:rotate(-180deg);"><view class="icon_ text_26 color__ time data-v-23291a5a">{{''+item.m0+''}}</view><block wx:if="{{!item.$orig.recalled}}"><view class="data-v-23291a5a"><item vue-id="{{'41fab1ec-2-'+index}}" isMy="{{item.m1}}" myid="{{myid}}" item="{{item.$orig}}" data-event-opts="{{[['^onClick',[['onItem']]],['^onLongpress',[['onLongpress']]],['^mention',[['mention']]]]}}" bind:onClick="__e" bind:onLongpress="__e" bind:mention="__e" class="data-v-23291a5a" bind:__l="__l"></item></view></block><block wx:else><view class="icon_ text_26 recalled data-v-23291a5a"><view class="data-v-23291a5a"><block wx:if="{{item.m2}}"><text class="data-v-23291a5a">你</text></block><block wx:else><text class="data-v-23291a5a">{{item.$orig.senderData.name}}</text></block>撤回了一条消息</view><block wx:if="{{item.m3}}"><view data-event-opts="{{[['tap',[['recalledEdit',['$0'],[[['history.messages','messageId',item.$orig.messageId]]]]]]]}}" class="recalled-edit data-v-23291a5a" bindtap="__e">重新编辑</view></block></view></block></view></block></block></view><view style="{{'height:'+($store.state.StatusBar.customBar-8+4+'px')+';'}}" class="data-v-23291a5a"></view></scroll-view><view class="bottomOperationRef data-v-23291a5a"><bottom-operation vue-id="41fab1ec-3" to="{{to}}" userList="{{userList}}" data-ref="bottomOperationRef" data-event-opts="{{[['^pushList',[['pushList']]],['^onBottom',[['onBottom']]],['^backToBottom',[['bottomOperationScrollToBottom']]],['^focus',[['focus']]],['^keyboardheightchange',[['keyboardheightchange']]]]}}" bind:pushList="__e" bind:onBottom="__e" bind:backToBottom="__e" bind:focus="__e" bind:keyboardheightchange="__e" class="data-v-23291a5a vue-ref" bind:__l="__l"></bottom-operation></view></view><m-screen-animation-lihua vue-id="41fab1ec-4" zIndex="9999" data-ref="mScreenAnimationLihua" class="data-v-23291a5a vue-ref" bind:__l="__l"></m-screen-animation-lihua><m-screen-animation-hongbao vue-id="41fab1ec-5" data-ref="mScreenAnimationHongbao" class="data-v-23291a5a vue-ref" bind:__l="__l"></m-screen-animation-hongbao><video-player-ref vue-id="41fab1ec-6" url="{{videoPlayer.url}}" value="{{videoPlayer.show}}" data-event-opts="{{[['^onVideoFullScreenChange',[['onVideoFullScreenChange']]],['^input',[['__set_model',['$0','show','$event',[]],['videoPlayer']]]]]}}" bind:onVideoFullScreenChange="__e" bind:input="__e" class="data-v-23291a5a" bind:__l="__l"></video-player-ref><open-red-packet vue-id="41fab1ec-7" data-ref="openRedPacketRef" class="data-v-23291a5a vue-ref" bind:__l="__l"></open-red-packet><operate bind:quote="__e" bind:thank="__e" bind:transmit="__e" vue-id="41fab1ec-8" data-ref="operateRef" data-event-opts="{{[['^quote',[['quote']]],['^thank',[['thank']]],['^transmit',[['transmit']]]]}}" class="data-v-23291a5a vue-ref" bind:__l="__l"></operate><m-group-selection bind:sendMessage="__e" vue-id="41fab1ec-9" data-ref="groupSelectionRef" data-event-opts="{{[['^sendMessage',[['sendMessage']]]]}}" class="data-v-23291a5a vue-ref" bind:__l="__l"></m-group-selection></view>