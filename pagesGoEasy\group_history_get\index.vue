<template>
	<view>
		<view class="flex_r week">
			<view class="icon_ week-item" v-for="(item, index) in ['日', '一', '二', '三', '四', '五', '六']" :key="index">{{ item }}</view>
		</view>
		<m-line color="#B8B8B8" length="100%" :hairline="true"></m-line>
		<view class="flex_r date">
			<view style="width: 14.2%" v-for="(item, index) in seizeList" :key="index"></view>
			<view class="icon_ text_32 date-item" v-for="(item, index) in dateLst" :key="item.date" @click="onItem(item)">
				<text class="bold_">{{ item.date }}</text>
				<view class="icon_ text_22 date-item-text" v-if="index === dateLst.length - 1">今天</view>
			</view>
		</view>
	</view>
</template>

<script>
import { to } from '@/utils/index.js';
let groupId = null;
export default {
	data() {
		return {
			seizeList: [],
			dateLst: []
		};
	},
	onLoad(e) {
		groupId = e.group_id;
		this.init();
	},
	methods: {
		init() {
			// 获取当前日期
			var currentDate = new Date();
			// 获取当前日期对应的天数
			var daysSinceToday = 10;
			var dates = [];
			var daysOfWeek = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'];
			for (var i = 0; i < daysSinceToday; i++) {
				var date = new Date(currentDate);
				date.setDate(date.getDate() - i);
				var dayOfWeek = daysOfWeek[date.getDay()];
				dates.push({
					date_: date.toISOString().slice(0, 10),
					date: date.toISOString().slice(8, 10),
					week: dayOfWeek,
					getDay: date.getDay()
				});
			}
			this.dateLst = dates.reverse();
			for (let i = 0; i <= dates[0].getDay - 1; i++) {
				this.seizeList.push({});
			}
		},
		onItem(item) {
			const lastMessageTimeStamp = Date.parse(new Date(item.date_).toString());
			to(`/pagesGoEasy/chat_page/index?groupId=${groupId}&lastMessageTimeStamp=${lastMessageTimeStamp + 86400000}`);
		}
	}
};
</script>

<style scoped lang="scss">
.week {
	width: calc(100% - 40rpx);
	margin: 20rpx auto 0 auto;
	height: 70rpx;
	.week-item {
		width: 14.2%;
	}
}
.date {
	width: calc(100% - 40rpx);
	flex-wrap: wrap;
	margin: 0 auto;
	.date-item {
		box-sizing: border-box;
		position: relative;
		width: 14.2%;
		height: 100rpx;
		.date-item-text {
			position: absolute;
			bottom: -4rpx;
			left: 0;
			width: 100%;
			color: #08ba01;
		}
	}
}
</style>
