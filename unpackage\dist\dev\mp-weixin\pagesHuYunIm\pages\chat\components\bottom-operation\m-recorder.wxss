@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.m-recorder.data-v-33685ec2 {
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.75);
}
.m-recorder .m-recorder-gif.data-v-33685ec2 {
  position: absolute;
  top: 50vh;
  left: calc(50% - 160rpx);
  z-index: 99;
  width: 340rpx;
  height: 170rpx;
  box-sizing: border-box;
  padding: 16rpx;
  border-radius: 30rpx;
  background-color: #95ec6a;
}
.m-recorder .m-recorder-gif .img.data-v-33685ec2 {
  width: 100%;
  height: 100%;
}
.m-recorder .m-recorder-gif.data-v-33685ec2::after {
  position: absolute;
  z-index: -1;
  content: "";
  bottom: -10rpx;
  left: calc(50% - 18rpx);
  width: 36rpx;
  height: 36rpx;
  border-radius: 2px;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  background-color: #95ec6a;
}
.m-recorder .m-recorder-str.data-v-33685ec2 {
  box-sizing: border-box;
  position: absolute;
  top: calc(100vh - 300rpx);
  left: calc(50% - 150vw);
  width: 300vw;
  height: 300vw;
  border: 12rpx solid #cccccc;
  border-radius: 50%;
  background-image: radial-gradient(#fff, #dddddd, #797979);
}
.m-recorder .m-recorder-str .m-recorder-str-icon.data-v-33685ec2 {
  position: absolute;
  top: 100rpx;
  left: calc(50% - 25rpx);
  width: 60rpx;
  height: 60rpx;
}
.m-recorder .m-recorder-str2.data-v-33685ec2 {
  box-sizing: border-box;
  position: absolute;
  top: calc(100vh - 400rpx);
  left: calc(50% - 150vw);
  width: 300vw;
  height: 300vw;
  opacity: 0.7;
  background-color: rgba(240, 240, 240, 0.3);
  border-radius: 50%;
  transition: all 0.2s;
}
.m-recorder .m-recorder-str2 .m-recorder-text.data-v-33685ec2 {
  width: 200rpx;
  position: absolute;
  top: 30rpx;
  left: calc(50% - 100rpx);
}
.m-recorder .m_recorder_str2.data-v-33685ec2 {
  background-color: #fa5251;
  box-shadow: #fa5251 0px 0px 50px;
}
