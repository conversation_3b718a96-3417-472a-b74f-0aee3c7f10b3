import { MQTT_CONFIG, TOPIC_TEMPLATES, validateUserInfo, createConnectOptions } from './mqttConfig.js'

/**
 * MQTT客户端工具类
 * 提供MQTT连接、订阅、发布等功能的封装
 */
class MqttClient {
  constructor() {
    this.client = null
    this.pingInterval = null
    this.isConnected = false
    this.userInfo = null
    this.options = {}
    this.callbacks = {
      onConnect: null,
      onMessage: null,
      onReconnect: null,
      onError: null,
      onEnd: null
    }
    this.mqttLib = null // MQTT库引用
  }

  /**
   * 设置MQTT库
   * @param {Object} mqtt - MQTT库实例
   */
  setMqttLib(mqtt) {
    this.mqttLib = mqtt
  }

  /**
   * 初始化MQTT连接
   * @param {Object} userInfo - 用户信息
   * @param {string} userInfo.userId - 用户ID
   * @param {string} userInfo.nickname - 用户昵称
   * @param {string} userInfo.channelCode - 频道代码
   * @param {string} userInfo.avatar - 用户头像
   * @param {string} userInfo.wsUrl - WebSocket连接地址
   * @param {string} userInfo.token - 认证token
   * @param {Object} callbacks - 回调函数集合
   * @param {Function} callbacks.onConnect - 连接成功回调
   * @param {Function} callbacks.onMessage - 消息接收回调
   * @param {Function} callbacks.onReconnect - 重连回调
   * @param {Function} callbacks.onError - 错误回调
   * @param {Function} callbacks.onEnd - 连接结束回调
   * @param {Object} customOptions - 自定义连接选项
   * @param {Object} mqttLib - MQTT库实例（可选，如果未设置则使用已设置的库）
   */
  connect(userInfo, callbacks = {}, customOptions = {}, mqttLib = null) {
    // 设置MQTT库
    if (mqttLib) {
      this.setMqttLib(mqttLib)
    }
    
    if (!this.mqttLib) {
      throw new Error('MQTT库未设置，请先调用setMqttLib()或在connect()中传入mqttLib参数')
    }
    
    // 验证用户信息
    validateUserInfo(userInfo)
    
    this.userInfo = userInfo
    this.callbacks = { ...this.callbacks, ...callbacks }
    
    // 使用配置文件创建连接选项
    this.options = createConnectOptions(userInfo, customOptions)

    // 创建MQTT连接
    this.client = this.mqttLib.connect(userInfo.wsUrl, this.options)
    
    // 绑定事件监听器
    this._bindEvents()
    
    return this.client
  }

  /**
   * 绑定MQTT事件监听器
   * @private
   */
  _bindEvents() {
    if (!this.client) return

    // 连接成功事件
    this.client.on('connect', () => {
      console.log('MQTT连接成功')
      this.isConnected = true
      
      // 清除之前的心跳
      this._clearPing()
      
      // 开始心跳
      this._startPing()
      
      // 订阅用户频道
      this._subscribeUserChannel()
      
      // 执行用户自定义连接回调
      if (this.callbacks.onConnect) {
        this.callbacks.onConnect()
      }
    })

    // 重连事件
    this.client.on('reconnect', () => {
      console.log('MQTT重连中...')
      if (this.callbacks.onReconnect) {
        this.callbacks.onReconnect()
      }
    })

    // 错误事件
    this.client.on('error', (error) => {
      console.log('MQTT连接错误:', error)
      this.isConnected = false
      if (this.callbacks.onError) {
        this.callbacks.onError(error)
      }
    })

    // 连接结束事件
    this.client.on('end', () => {
      console.log('MQTT连接结束')
      this.isConnected = false
      this._clearPing()
      if (this.callbacks.onEnd) {
        this.callbacks.onEnd()
      }
    })

    // 消息接收事件
    this.client.on('message', (topic, message) => {
      try {
        const mqttMsg = JSON.parse(message.toString())
        console.log('收到MQTT消息:', mqttMsg)
        
        // 执行用户自定义消息回调
        if (this.callbacks.onMessage) {
          this.callbacks.onMessage(topic, mqttMsg, message.toString())
        }
      } catch (error) {
        console.error('解析MQTT消息失败:', error)
      }
    })
  }

  /**
   * 开始心跳
   * @private
   * @param {number} interval - 心跳间隔（毫秒），默认使用配置文件中的值
   */
  _startPing(interval = MQTT_CONFIG.PING.INTERVAL) {
    if (!this.userInfo || !this.client) return
    
    this.pingInterval = setInterval(() => {
      if (this.isConnected) {
        const pingTopic = TOPIC_TEMPLATES.PING(this.userInfo.userId)
        this.client.publish(pingTopic, MQTT_CONFIG.PING.MESSAGE)
        console.log('发送心跳包')
      }
    }, interval)
  }

  /**
   * 清除心跳
   * @private
   */
  _clearPing() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval)
      this.pingInterval = null
    }
  }

  /**
   * 订阅用户频道
   * @private
   */
  _subscribeUserChannel() {
    if (!this.userInfo || !this.client) return
    
    const topic = TOPIC_TEMPLATES.USER_CHANNEL(this.userInfo.userId)
    this.client.subscribe(topic, { qos: MQTT_CONFIG.SUBSCRIBE.DEFAULT_QOS }, (err) => {
      if (!err) {
        console.log(`订阅成功: ${topic}`)
      } else {
        console.error(`订阅失败: ${topic}`, err)
      }
    })
  }

  /**
   * 订阅指定主题
   * @param {string|Array} topics - 主题或主题数组
   * @param {Object} options - 订阅选项
   * @param {Function} callback - 订阅回调
   */
  subscribe(topics, options = {}, callback = null) {
    if (!this.client || !this.isConnected) {
      console.warn('MQTT未连接，无法订阅')
      return false
    }
    
    this.client.subscribe(topics, options, callback)
    return true
  }

  /**
   * 取消订阅
   * @param {string|Array} topics - 主题或主题数组
   * @param {Function} callback - 取消订阅回调
   */
  unsubscribe(topics, callback = null) {
    if (!this.client) {
      console.warn('MQTT客户端不存在')
      return false
    }
    
    this.client.unsubscribe(topics, callback)
    return true
  }

  /**
   * 发布消息
   * @param {string} topic - 主题
   * @param {string|Object} message - 消息内容
   * @param {Object} options - 发布选项
   * @param {Function} callback - 发布回调
   */
  publish(topic, message, options = {}, callback = null) {
    if (!this.client || !this.isConnected) {
      console.warn('MQTT未连接，无法发布消息')
      return false
    }
    
    // 如果消息是对象，转换为JSON字符串
    const messageStr = typeof message === 'object' ? JSON.stringify(message) : message
    
    this.client.publish(topic, messageStr, options, callback)
    return true
  }

  /**
   * 断开连接
   * @param {boolean} force - 是否强制断开
   * @param {Function} callback - 断开回调
   */
  disconnect(force = false, callback = null) {
    if (this.client) {
      this._clearPing()
      this.client.end(force, callback)
      this.isConnected = false
    }
  }

  /**
   * 获取连接状态
   * @returns {boolean} 是否已连接
   */
  getConnectStatus() {
    return this.isConnected
  }

  /**
   * 获取客户端实例
   * @returns {Object} MQTT客户端实例
   */
  getClient() {
    return this.client
  }

  /**
   * 设置心跳间隔
   * @param {number} interval - 心跳间隔（毫秒）
   */
  setPingInterval(interval) {
    this._clearPing()
    if (this.isConnected) {
      this._startPing(interval)
    }
  }

  /**
   * 获取用户信息
   * @returns {Object} 当前用户信息
   */
  getUserInfo() {
    return this.userInfo
  }

  /**
   * 获取连接选项
   * @returns {Object} 当前连接选项
   */
  getOptions() {
    return this.options
  }

  /**
   * 重新连接
   */
  reconnect() {
    if (this.client) {
      this.client.reconnect()
    }
  }
}

// 创建单例实例
const mqttClient = new MqttClient()

export default mqttClient
