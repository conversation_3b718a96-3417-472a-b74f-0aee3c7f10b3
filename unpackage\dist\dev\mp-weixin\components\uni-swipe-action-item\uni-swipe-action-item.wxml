<wxs src="./index.wxs" module="swipe"></wxs>
<view class="uni-swipe data-v-bb66970c"><view class="uni-swipe_box data-v-bb66970c" data-threshold="{{threshold}}" data-disabled="{{disabled}}" change:prop="{{swipe.sizeReady}}" prop="{{btn}}" bindtouchstart="{{swipe.touchstart}}" bindtouchmove="{{swipe.touchmove}}" bindtouchend="{{swipe.touchend}}" bindmousedown="{{swipe.mousedown}}" bindmousemove="{{swipe.mousemove}}" bindmouseup="{{swipe.mouseup}}" bindmouseleave="{{swipe.mouseleave}}"><view class="uni-swipe_button-group button-group--left data-v-bb66970c"><block wx:if="{{$slots.left}}"><slot name="left"></slot></block><block wx:else><block wx:for="{{leftOptions}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="uni-swipe_button button-hock data-v-bb66970c" style="{{'background-color:'+(item.style&&item.style.backgroundColor?item.style.backgroundColor:'#C7C6CD')+';'+('font-size:'+(item.style&&item.style.fontSize?item.style.fontSize:'16px')+';')}}" data-button="{{btn}}" data-event-opts="{{[['touchstart',[['appTouchStart',['$event']]]],['touchend',[['appTouchEnd',['$event',index,'$0','left'],[[['leftOptions','',index]]]]]],['tap',[['onClickForPC',[index,'$0','left'],[[['leftOptions','',index]]]]]]]}}" bindtouchstart="__e" bindtouchend="__e" catchtap="__e"><text class="uni-swipe_button-text data-v-bb66970c" style="{{'color:'+(item.style&&item.style.color?item.style.color:'#FFFFFF')+';'}}">{{item.text}}</text></view></block></block></view><view class="uni-swipe_text--center data-v-bb66970c"><slot></slot></view><view class="uni-swipe_button-group button-group--right data-v-bb66970c"><block wx:if="{{$slots.right}}"><slot name="right"></slot></block><block wx:else><block wx:for="{{rightOptions}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="uni-swipe_button button-hock data-v-bb66970c" style="{{'background-color:'+(item.style&&item.style.backgroundColor?item.style.backgroundColor:'#C7C6CD')+';'+('font-size:'+(item.style&&item.style.fontSize?item.style.fontSize:'16px')+';')}}" data-button="{{btn}}" data-event-opts="{{[['touchstart',[['appTouchStart',['$event']]]],['touchend',[['appTouchEnd',['$event',index,'$0','right'],[[['rightOptions','',index]]]]]]]}}" bindtouchstart="__e" bindtouchend="__e"><text class="uni-swipe_button-text data-v-bb66970c" style="{{'color:'+(item.style&&item.style.color?item.style.color:'#FFFFFF')+';'}}">{{item.text}}</text></view></block></block></view></view></view>