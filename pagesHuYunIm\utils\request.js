import { HTTP_REQUEST_URL, HEADER, TOKENNAME, TIMEOUT } from '../config/api'
import store from '../store'
console.log('🚀 ~ store:', store)
/**
 * 发送请求
 */
function baseRequest(url, method, data, { noAuth = false, noVerify = false }) {
  let Url = HTTP_REQUEST_URL,
    header = HEADER
  if (store.state.token) header[TOKENNAME] = store.state.token
  return new Promise((reslove, reject) => {
    uni.request({
      // url: Url + '/huyun' + url,
      url: Url + url,
      method: method || 'GET',
      header: header,
      data: data || {},
      timeout: TIMEOUT,
      success: (res) => {
        console.log('res', res)
        if (noVerify) reslove(res.data, res)
        else if (res.data.code == 200) reslove(res.data.result, res)
        else if ([110002, 110003, 110004].indexOf(res.data.status) !== -1) {
          // toLogin();
          reject(res.data)
        } else if (res.data.status == 100103) {
          uni.showModal({
            title: `提示`,
            content: res.data.msg,
            showCancel: false,
            confirmText: '我知道了'
          })
        } else reject(res.data.msg || `系统错误`)
      },
      fail: (msg) => {
        let data = {
          mag: '请求失败',
          status: 1 //1没网
        }
        // #ifdef APP-PLUS
        reject(data)
        // #endif
        // #ifndef APP-PLUS
        reject('请求失败')
        // #endif
      }
    })
  })
}

/**
 * 上传文件
 * @param {String} filePath 文件路径
 * @param {String} url 上传接口地址，默认为 '/jeecg-boot/huyun/front/chat/upload'
 * @param {String} name 后端接收文件的字段名，默认为 'file'
 * @param {Object} formData 额外的表单数据
 * @returns {Promise}
 */
function uploadFile(filePath, url = '/jeecg-boot/huyun/front/chat/upload', name = 'file', formData = {}) {
  let Url = HTTP_REQUEST_URL,
    header = {
      'x-access-token': store.state.app?.token || store.state.token
      // 注意：不要手动设置 Content-Type，uni-app 会自动处理
    }

  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: Url + url,
      filePath: filePath,
      name: name,
      formData: formData,
      header: header,
      success: (res) => {
        console.log('uploadFile success:', res)
        if (res.statusCode === 200) {
          try {
            const data = JSON.parse(res.data)
            if (data.code === 200 || data.code === 0) {
              resolve(data.result || data.message || data, res)
            } else {
              reject(data.msg || data.message || '上传失败')
            }
          } catch (error) {
            console.error('解析上传响应失败:', error)
            reject('服务器响应格式错误')
          }
        } else {
          reject(`上传失败，状态码: ${res.statusCode}`)
        }
      },
      fail: (err) => {
        console.error('uploadFile fail:', err)
        reject(err.errMsg || '上传失败')
      }
    })
  })
}

const request = {}

;['options', 'get', 'post', 'put', 'head', 'delete', 'trace', 'connect'].forEach((method) => {
  request[method] = (api, data, opt) => baseRequest(api, method, data, opt || {})
})

// 添加上传文件方法
request.uploadFile = uploadFile

export default request
