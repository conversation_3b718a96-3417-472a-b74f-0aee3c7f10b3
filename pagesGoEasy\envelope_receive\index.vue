<template>
	<view :id="page_font_size">
		<view class="navigationBar-box">
			<!-- #ifdef MP -->
			<view :style="{ height: customBar - statusBar + 'px' }"></view>
			<!-- #endif -->
			<!-- #ifndef MP -->
			<view :style="{ height: statusBar + 'px' }"></view>
			<!-- #endif -->

			<view class="flex_r fa_c fj_b navigationBar" :style="{ height: customBar - statusBar + 'px' }">
				<view class="icon_ navigationBar-icon" @click="to()">
					<image
						class="img"
						src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTcwNy4zMjMgOTYwLjU1Nmw1Ni4wOTMtNTQuNTAzLTQwMy45MTctMzkyLjQ2OSA0MDMuOTE3LTM5Mi40NzUtNTYuMDkzLTU0LjUwMkwyNDcuMzIgNTEzLjU4NGw0NjAuMDA0IDQ0Ni45NzJ6bTAgMHoiIGZpbGw9IiNmZmYiLz48L3N2Zz4="
						mode="aspectFill"
					></image>
				</view>
				<view class="flex1 icon_ navigationBar-text bold_"></view>
				<view class="navigationBar-icon"></view>
			</view>
		</view>

		<view class="top">
			<view class="top-str">
				<view class="top-str-bc">
					<!-- #ifdef APP -->
					<cacheImage
						:src="pageObj.red_packet_bg"
						ext="jpg"
						mstyle="
							 {
								width: 100vw;
								height: 420rpx;
							}
						"
					></cacheImage>
					<!-- #endif -->
					<!-- #ifndef APP -->
					<image class="img" :src="pageObj.red_packet_bg" mode="aspectFill"></image>
					<!-- #endif -->
				</view>
			</view>
		</view>
		<view style="height: 420rpx"></view>
		<view class="icon_ info">
			<view class="info-img" style="flex-shrink: 0">
				<image class="img" src="https://tse4-mm.cn.bing.net/th/id/OIP-C.duz6S7Fvygrqd6Yj_DcXAQHaF7?rs=1&pid=ImgDetMain" mode="aspectFill"></image>
			</view>
			<view class="text_34 bold_ info-name">荷塘月色 发出的红包</view>
		</view>
		<view class="text_28 color__ icon_ title">恭喜发财 大吉大利</view>

		<!-- 金额 -->
		<template>
			<view class="flex_r money">
				<view class="money-value">
					<text class="bold_ money-value-">100.00</text>
					<view class="text_28 money-text">￥</view>
				</view>
			</view>
			<view class="icon_ text_26 money-illustrate" @click="to('/pagesUser/user/account/index')">
				已存入账户，可用于购买商品
				<view class="icon_ money-illustrate-icon">
					<image
						class="img"
						src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTQ3Ny44IDgxNi40bDMwNC4xLTMwNC42LTMwMy44LTMwMy4yYy0xOC41LTE4LjUtMTguNS00OC41IDAtNjcuMSAxOC41LTE4LjYgNDguNi0xOC42IDY3LjEtLjFsMzM3LjMgMzM2LjdjMTguNSAxOC41IDE4LjUgNDguNSAwIDY3LjFMNTQ0LjkgODgzLjRjLTE4LjUgMTguNi00OC42IDE4LjYtNjcuMS4xLTE4LjUtMTguNS0xOC41LTQ4LjUgMC02Ny4xeiIgZmlsbD0iI2QzYWQ3MyIgZGF0YS1zcG0tYW5jaG9yLWlkPSJhMzEzeC5zZWFyY2hfaW5kZXguMC5pMjEuMzdkYjNhODF2QUluTFciIGNsYXNzPSJzZWxlY3RlZCIvPjwvc3ZnPg=="
						mode="aspectFill"
					></image>
				</view>
			</view>
		</template>
		<!-- <view class="interval"></view> -->

		<view class="list">
			<view class="text_30 list-title">{{ titleText }}</view>
			<m-line color="#e5e5e5" margin="0 0 30rpx 0" length="100%" :hairline="true"></m-line>
			<view
				class="flex_r item"
				v-for="(item, index) in list"
				:key="item.id"
				@click="to('/pagesGoEasy/group_member_infor/index', { member_id: item.member_id, group_id: item.group_id })"
			>
				<view class="item-img">
					<image class="img" :src="item.member_info.avatar" mode="aspectFill"></image>
				</view>
				<view class="flex1 item-r">
					<view class="flex_r text_30 item-name">
						<view class="flex1">{{ item.member_info.name }}</view>
						<view class="">
							<text class="bold_">{{ item.red }}</text>
							<text class="color__ text_18" style="margin-left: 6rpx">￥</text>
						</view>
					</view>
					<view class="text_22 flex_r fj_b item-time">
						<text>{{ formatDate(item.create_time) }}</text>
						<view class="icon_ label" v-if="best(index)">
							<view class="label-icon">
								<image
									class="img"
									src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTAgMGgxMDI0djEwMjRIMHoiIGZpbGw9Im5vbmUiLz48cGF0aCBkPSJNOTg1LjYgMzUyYzAtNDAuMzItMzItNzIuMzItNzEuMDQtNzIuMzItMzkuNjggMC03MS4wNCAzMi42NC03MS4wNCA3Mi4zMiAwIDE1LjM2IDQuNDggMjkuNDQgMTIuOCA0MS42LTQzLjUyIDc0LjI0LTc0Ljg4IDE3NC43Mi0xMzUuNjggMTc0LjcyLTkuNiAwLTE5LjItLjY0LTI4LjE2LTIuNTYtOTYtMTcuMjgtMTQ3LjItMjUzLjQ0LTE1Mi4zMi0yODYuNzIgMjMuMDQtMTIuMTYgMzkuNjgtMzcuMTIgMzkuNjgtNjUuOTIgMC00MC4zMi0zMi03Mi4zMi03MS4wNC03Mi4zMmE3Mi41MTIgNzIuNTEyIDAgMCAwLTI0Ljk2IDE0MC4xNmMtNS4xMiAzNy4xMi01My43NiAyNzYuNDgtMTQ3LjIgMjg5LjkyLTcuNjggMS4yOC0xNS4zNiAxLjkyLTIzLjY4IDEuOTItNjAuMTYgMC0xMDIuNC0xMDQuOTYtMTM3LjYtMTc0LjcyIDMuODQtOC4zMiA1Ljc2LTE3LjkyIDUuNzYtMjguMTYgMC00MC4zMi0zMi03Mi4zMi03MS4wNC03Mi4zMi0xOS4yIDAtMzYuNDggNy42OC00OS4yOCAyMC40OC0xNC4wOCAxMy40NC0yMi40IDMyLTIyLjQgNTEuODQgMCA0MC4zMiAzMiA3Mi4zMiA3MS4wNCA3Mi4zMiAxMjQuMTYgMzEwLjQgMTg1LjYgNDY1LjkyIDE4NS42IDQ2NS45MlM0MDEuOTIgOTYwIDUxMS4zNiA5NjBzMjE3LjYtNTEuODQgMjE3LjYtNTEuODQgNjQuNjQtMTYxLjI4IDE5My4yOC00ODQuNDhjMzUuMi0zLjg0IDYzLjM2LTM0LjU2IDYzLjM2LTcxLjY4eiIgZmlsbD0iI0ZCRDMwRiIvPjxwYXRoIGQ9Ik0yOTQuNCA5MDQuMzJjMCAyOS40NCAxMDYuODggNTkuNTIgMjE1LjA0IDU5LjUyUzcyOS42IDkzNC40IDcyOS42IDkwNC4zMmMwLTI5LjQ0LTExMi42NC01OS41Mi0yMjAuMTYtNTkuNTJTMjk0LjQgODc0LjI0IDI5NC40IDkwNC4zMnoiIGZpbGw9IiNGRkE3MDYiLz48L3N2Zz4="
									mode="aspectFill"
								></image>
							</view>
							手气最佳
						</view>
					</view>
					<view class="item-r-m-line">
						<m-line color="#e5e5e5" length="100%" :hairline="true"></m-line>
					</view>
				</view>
			</view>
		</view>
		<m-bottom-paceholder></m-bottom-paceholder>
		<!-- 礼花 -->
		<m-screen-animation-lihua ref="mScreenAnimationLihua" zIndex="9999"></m-screen-animation-lihua>
	</view>
</template>
<script>
// #ifdef APP
import cacheImage from '@/pagesGoEasy/chat_page/components/cache-image/cache-image.vue';
// #endif
import { to, jsonUrl, formatDate, getAudioContext } from '@/utils/index.js';
import { mapState } from 'vuex';
let data = null;

// 到账声音
const envelope_receive_audio_context = uni.createInnerAudioContext();
export default {
	components: {
		// #ifdef APP
		cacheImage
		// #endif
	},
	data() {
		return {
			loadingState: 'lom',
			pageObj: {
				member_info: {}
			},
			page: 1,
			list: []
		};
	},
	onLoad(e) {
		this.list = [
			{
				red: '0.01',
				create_time: '2024-07-29 14:01:04',
				member_info: {
					name: '明天会更好',
					avatar: 'https://tse4-mm.cn.bing.net/th/id/OIP-C.NimIzUOhgk2QHjPwRE0Q8gHaE5?rs=1&pid=ImgDetMain',
					member_id: 105974
				}
			},
			{
				red: '100.00',
				create_time: '2024-07-29 14:01:04',
				member_info: {
					name: '复兴中华',
					avatar: 'https://img.zcool.cn/community/014cdd5a96ba16a801219586209ded.png@1280w_1l_2o_100sh.png',
					member_id: 87253
				}
			},
			{
				red: '0.1',
				create_time: '2024-07-29 14:01:04',
				member_info: {
					name: '运筹帷幄',
					avatar: 'https://img.zcool.cn/community/0121e25a98e23aa801206d96c1cf46.jpg@1280w_1l_2o_100sh.jpg',
					member_id: 87253
				}
			}
		];
	},
	onReachBottom() {},

	computed: mapState({
		page_font_size: (state) => state.page_font_size,
		statusBar: (state) => state.StatusBar.statusBar,
		customBar: (state) => state.StatusBar.customBar,
		titleText() {
			if (!this.pageObj.red_num) return '';
			if (this.pageObj.type === 2 && this.pageObj.is_end) return `专属红包，${this.secondsToHms}被领取`;
			if (this.pageObj.is_end) return `${this.pageObj.red_num}个红包，${this.secondsToHms}被抢光`;
			return `领取${this.pageObj.has_num}/${this.pageObj.red_num}个`;
		},
		// 判断手气最佳
		best() {
			return (index) => {
				return index === 1;
			};
		},
		secondsToHms() {
			let d = Number(this.pageObj.time);
			var h = Math.floor(d / 3600);
			var m = Math.floor((d % 3600) / 60);
			var s = Math.floor((d % 3600) % 60);
			var hDisplay = h > 0 ? h + (h == 1 ? '小时' : '小时') : '';
			var mDisplay = m > 0 ? m + (m == 1 ? '分钟' : '分钟') : '';
			var sDisplay = s > 0 ? s + (s == 1 ? '秒' : '秒') : '';
			return `${hDisplay}${mDisplay}${sDisplay}`;
		}
	}),
	methods: {
		to,
		formatDate
	}
};
</script>

<style scoped lang="scss">
.navigationBar-box {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 99;
	.navigationBar {
		width: 100%;
		.navigationBar-icon {
			width: 44rpx;
			height: 44rpx;
			margin: 0 30rpx;
			.img {
				width: 90%;
				height: 90%;
			}
		}
		.navigationBar-text {
		}
	}
}
.top {
	position: fixed;
	z-index: 3;
	top: 0;
	left: 0;
	width: 100%;
	height: 420rpx;
	background-color: #fff;
	.top-str {
		box-sizing: border-box;
		position: absolute;
		left: calc(50% - 600rpx);
		bottom: 30rpx;
		width: 1200rpx;
		height: 800rpx;
		border-radius: 50%;
		background-color: #f25745;
		border: 6rpx solid #f8ca75;
		overflow: hidden;
		.top-str-bc {
			position: absolute;
			left: calc(50% - 50vw);
			bottom: 0;
			z-index: 2;
			width: 100vw;
			height: 420rpx;
		}
	}
}
.info {
	box-sizing: border-box;
	padding: 0 40rpx;
	width: 100%;
	min-height: 60rpx;
	.info-img {
		width: 50rpx;
		height: 50rpx;
		border-radius: 10rpx;
		overflow: hidden;
		margin-right: 10rpx;
		background-color: #f4f4f4;
	}
	.info-name {
	}
}

.title {
	box-sizing: border-box;
	padding: 0 40rpx;
	width: 100%;
	min-height: 60rpx;
}
.money {
	width: 100%;
	height: 100rpx;
	margin-top: 40rpx;
	color: #c2a26f;
	align-items: flex-end;
	justify-content: center;
	.money-value {
		position: relative;
		.money-value- {
			font-size: 90rpx;
		}
		.money-text {
			position: absolute;
			right: -70rpx;
			bottom: 20rpx;
		}
	}
}
.money-illustrate {
	width: 100%;
	color: #c2a26f;
	height: 40rpx;
	margin-top: 10rpx;
	.money-illustrate-icon {
		width: 30rpx;
		height: 30rpx;
	}
}
.interval {
	width: 100%;
	height: 16rpx;
	margin-top: 60rpx;
	background-color: #e5e5e5;
}
.list {
	box-sizing: border-box;
	padding: 0 30rpx;
	width: 100%;
	margin-top: 40rpx;
	.list-title {
		height: 80rpx;
		line-height: 80rpx;
		color: #b2b2b2;
	}
	.item {
		width: 100%;
		height: 100rpx;
		margin-bottom: 20rpx;
		.item-img {
			width: 80rpx;
			height: 80rpx;
			margin-right: 20rpx;
			border-radius: 10rpx;
			overflow: hidden;
			background-color: #f1f1f1;
		}
		.item-r {
			.item-name {
				height: 50rpx;
			}
			.item-time {
				height: 34rpx;
				color: #b2b2b2;
				.label {
					color: #edb746;
					.label-icon {
						width: 34rpx;
						height: 34rpx;
						margin-right: 10rpx;
					}
				}
			}
			.item-r-m-line {
				width: 100%;
				margin-top: 20rpx;
				margin-bottom: 10rpx;
			}
		}
	}
}
</style>
