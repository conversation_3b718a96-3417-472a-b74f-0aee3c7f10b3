# 语音消息时长获取功能

## 功能说明

当 `item.msgType === 'voice'` 时，系统会自动获取MP3音频文件的实际时长，而不是使用硬编码的10秒。

## 实现原理

1. **初始设置**: 首先设置默认时长为10秒，确保界面能够立即显示
2. **异步获取**: 使用 `uni.createInnerAudioContext()` 异步获取音频文件的真实时长
3. **动态更新**: 获取到真实时长后，更新 `item.payload.duration` 并刷新视图

## 代码实现

### 主要修改文件
- `pagesHuYunIm/pages/chat/index.vue`

### 核心方法

#### 1. processMessages 方法中的语音处理逻辑
```javascript
// 处理语音消息内容
if (item.msgType === 'voice' && typeof item.content === 'string' && item.content.length > 10) {
  let audioUrl = item.content
  
  try {
    // 解析语音时长 item.content 连接时长
    const contentObj = JSON.parse(item.content)
    // 如果content是JSON格式，可能包含audioSrc字段
    if (contentObj.audioSrc) {
      audioUrl = contentObj.audioSrc
    }
  } catch (error) {
    console.warn('解析语音消息内容失败，使用原始content作为URL:', error)
    // 如果解析失败，直接使用content作为URL
  }

  // 先设置默认值
  item.payload = { url: audioUrl, duration: 10 }
  
  // 异步获取真实音频时长
  this.getAudioDuration(audioUrl).then(duration => {
    if (duration > 0) {
      item.payload.duration = duration
      // 强制更新视图
      this.$forceUpdate()
    }
  }).catch(error => {
    console.warn('获取音频时长失败:', error)
  })
}
```

#### 2. getAudioDuration 方法
```javascript
/**
 * 获取音频文件时长
 * @param {string} audioUrl - 音频文件URL
 * @returns {Promise<number>} 音频时长（秒）
 */
getAudioDuration(audioUrl) {
  return new Promise((resolve, reject) => {
    // 在uni-app中使用createInnerAudioContext获取音频时长
    const audioContext = uni.createInnerAudioContext()
    let isResolved = false
    
    // 清理函数
    const cleanup = () => {
      if (audioContext) {
        audioContext.destroy()
      }
    }
    
    // 设置音频源
    audioContext.src = audioUrl
    
    // 监听音频加载完成事件
    audioContext.onCanplay(() => {
      if (isResolved) return
      
      // 获取音频时长
      const duration = audioContext.duration
      console.log('音频时长:', duration, '秒', 'URL:', audioUrl)
      
      cleanup()
      
      if (duration && duration > 0) {
        isResolved = true
        resolve(Math.ceil(duration)) // 向上取整
      } else {
        isResolved = true
        reject(new Error('无法获取音频时长'))
      }
    })
    
    // 监听音频加载事件（备用方案）
    audioContext.onLoadedmetadata && audioContext.onLoadedmetadata(() => {
      if (isResolved) return
      
      const duration = audioContext.duration
      console.log('通过loadedmetadata获取音频时长:', duration, '秒')
      
      if (duration && duration > 0) {
        cleanup()
        isResolved = true
        resolve(Math.ceil(duration))
      }
    })
    
    // 监听错误事件
    audioContext.onError((error) => {
      if (isResolved) return
      
      console.error('音频加载失败:', error, 'URL:', audioUrl)
      cleanup()
      isResolved = true
      reject(error)
    })
    
    // 设置超时处理
    setTimeout(() => {
      if (!isResolved) {
        cleanup()
        isResolved = true
        reject(new Error('获取音频时长超时'))
      }
    }, 8000) // 8秒超时
  })
}
```

## 功能特点

1. **兼容性**: 支持两种content格式
   - 直接URL字符串: `"https://example.com/audio.mp3"`
   - JSON格式: `{"audioSrc": "https://example.com/audio.mp3"}`

2. **容错性**: 
   - 如果获取时长失败，保持默认的10秒
   - 设置8秒超时，避免长时间等待
   - 防止重复解析和内存泄漏

3. **用户体验**: 
   - 先显示默认时长，确保界面响应
   - 异步获取真实时长后动态更新
   - 时长向上取整，显示更友好

4. **性能优化**:
   - 使用 `isResolved` 标志防止重复处理
   - 及时清理音频上下文，避免内存泄漏
   - 提供备用的 `onLoadedmetadata` 事件监听

## 使用场景

- 聊天消息中的语音消息显示
- 语音消息列表的时长显示
- 语音消息引用时的时长显示

## 注意事项

1. 需要确保音频URL可访问
2. 跨域音频文件可能无法获取时长
3. 网络较慢时可能需要等待较长时间
4. 建议在服务端预处理音频时长，客户端作为备用方案
