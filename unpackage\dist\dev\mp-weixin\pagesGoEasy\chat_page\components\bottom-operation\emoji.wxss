@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.emoji.data-v-2da725c6 {
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
  transition: all 0.2s;
  background-color: #f6f6f6;
}
.emoji .emoji-title.data-v-2da725c6 {
  box-sizing: border-box;
  padding: 0 20rpx;
  width: 100%;
  height: 100rpx;
}
.emoji .emoji-title .emoji-title-item.data-v-2da725c6 {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  transition: all 0.3s;
}
.emoji .emoji-title .emoji-title-item .emoji-title-item-img.data-v-2da725c6 {
  width: 70%;
  height: 70%;
}
.emoji .emoji-title .emoji_title_item.data-v-2da725c6 {
  background-color: #fff;
}
.swiper.data-v-2da725c6 {
  width: 100%;
  height: 590rpx;
  background-color: #ececec;
}
.swiper .scroll-Y.data-v-2da725c6 {
  width: 100%;
  height: 100%;
}
.swiper .swiper-item.data-v-2da725c6 {
  position: relative;
  width: 100%;
  height: 100%;
}
.swiper .swiper-item .swiper-item-box.data-v-2da725c6 {
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  flex-wrap: wrap;
  align-items: flex-start;
}
.swiper .swiper-item .swiper-item-box .swiper-item-box-l.data-v-2da725c6 {
  width: 100vw;
  flex-wrap: wrap;
  align-items: flex-start;
}
.swiper .swiper-item .swiper-item-box .swiper-item-box-r.data-v-2da725c6 {
  width: 37.5vw;
  flex-wrap: wrap;
  align-items: flex-start;
}
.swiper .swiper-item .swiper-item-box .emoji-item.data-v-2da725c6 {
  width: 12.5vw;
  height: 12.5vw;
  flex-shrink: 0;
  transition: all 0.1s;
}
.swiper .swiper-item .swiper-item-box .emoji-item .img.data-v-2da725c6 {
  width: 70%;
  height: 70%;
}
.swiper .swiper-item .swiper-item-box-b.data-v-2da725c6 {
  width: 38vw;
  position: absolute;
  z-index: 99;
  right: 0rpx;
  bottom: 0px;
  padding: 0 0 40rpx 0;
  background-image: linear-gradient(to top, #ececec, #ececec, #ececec, #ececec, #ececec, rgba(0, 0, 0, 0));
}
.swiper .swiper-item .swiper-item-box-delete.data-v-2da725c6 {
  width: 116rpx;
  height: 12.5vw;
  border-radius: 10rpx;
  margin-right: 20rpx;
  background-color: #fff;
}
.swiper .swiper-item .swiper-item-box-delete .img.data-v-2da725c6 {
  width: 45%;
  height: 45%;
  margin-right: 4rpx;
}
.swiper .swiper-item .swiper-item-box-button.data-v-2da725c6 {
  width: 116rpx;
  height: 12.5vw;
  border-radius: 10rpx;
  margin-right: 10rpx;
  background-color: #05c160;
}
