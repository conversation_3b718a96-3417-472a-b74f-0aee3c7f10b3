<view class="page"><m-return vue-id="7dd5a908-1" bind:__l="__l"></m-return><view class="contacts"><view class="contacts-container"><block wx:if="{{$root.g0}}"><view class="user-list"><block wx:for="{{groups}}" wx:for-item="group" wx:for-index="key" wx:key="key"><view data-event-opts="{{[['tap',[['enterChat',['$0'],[[['groups','',key,'group_id']]]]]]]}}" class="user-list-item" bindtap="__e"><view class="user-item-avatar"><image class="img" src="{{group.group_info.avatar}}" mode="aspectFill"></image></view><view class="user-item-info"><label class="user-item-info__name _span">{{group.group_info.name}}</label></view></view></block></view></block><block wx:else><view class="nouser-list">暂无数据</view></block></view></view></view>