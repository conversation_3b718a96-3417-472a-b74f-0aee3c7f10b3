@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-00dcdc92 {
  position: fixed;
  z-index: 1;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: #ededed;
}
.scroll-Y.data-v-00dcdc92 {
  width: 100%;
  height: 0;
  transition: all 0.2s;
  background-color: #ededed;
}
.scroll-Y.data-v-00dcdc92 ::-webkit-scrollbar {
  display: none;
}
.scroll-view-str.data-v-00dcdc92 {
  width: 100%;
}
.time.data-v-00dcdc92 {
  width: 100%;
  color: #a3a3a3;
  line-height: 100rpx;
}
.recalled.data-v-00dcdc92 {
  width: 100%;
  height: 50rpx;
  margin: 20rpx 0;
  color: #a3a3a3;
}
.recalled .recalled-edit.data-v-00dcdc92 {
  color: #5a6693;
  margin-left: 14rpx;
}
