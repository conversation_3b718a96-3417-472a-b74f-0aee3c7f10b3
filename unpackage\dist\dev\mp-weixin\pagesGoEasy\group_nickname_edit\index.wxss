@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.title.data-v-9459d982 {
  width: 100%;
  height: 80rpx;
  text-align: center;
  margin: 0 auto;
}
.text.data-v-9459d982 {
  width: 100%;
  height: 60rpx;
  text-align: center;
  margin: 0 auto;
}
.row.data-v-9459d982 {
  width: calc(100% - 60rpx);
  height: 110rpx;
  margin: 30rpx auto 200rpx auto;
  border-top: 0.5px solid rgba(153, 153, 153, 0.3);
  border-bottom: 0.5px solid rgba(153, 153, 153, 0.3);
}
.row .row-img.data-v-9459d982 {
  width: 80rpx;
  height: 80rpx;
  margin-left: 10rpx;
  border-radius: 10rpx;
  overflow: hidden;
  background-color: #f1f1f1;
}
.row .row-input.data-v-9459d982 {
  height: 80rpx;
  margin: 0 20rpx;
}
.row .row-input input.data-v-9459d982 {
  height: 100%;
  line-height: 80rpx;
}
.row .row-icon.data-v-9459d982 {
  width: 38rpx;
  height: 38rpx;
  margin-right: 10rpx;
}
.confirm.data-v-9459d982 {
  width: 300rpx;
  height: 80rpx;
  border-radius: 10rpx;
  margin: 0 auto;
  background-color: #05c160;
}
