
 ::-webkit-scrollbar {
	width: 0;
	height: 0;
	color: transparent;
	display: none;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-77a1928e {
  position: relative;
  width: 100%;
  height: 100vh;
  background-color: #f7f7f7;
  overflow: hidden;
  border-radius: 20rpx 20rpx 0 0;
}
.page .top.data-v-77a1928e {
  width: 100%;
  height: 370rpx;
}
.page .top .top-title.data-v-77a1928e {
  width: calc(100% - 60rpx);
  height: 120rpx;
  margin: 0 auto;
}
.page .top .top-title .top-title-text.data-v-77a1928e {
  width: 140rpx;
}
.page .top .top-title .top-title-button.data-v-77a1928e {
  width: 140rpx;
  height: 66rpx;
  border-radius: 10rpx;
  background-color: #aaaaaa;
}
.page .top .top-title .top_title_button.data-v-77a1928e {
  background-color: #4ac165;
}
.page .top .infr.data-v-77a1928e {
  width: calc(100% - 40rpx);
  height: 100rpx;
  margin: 0 auto 20rpx auto;
}
.page .top .infr .infr-img.data-v-77a1928e {
  box-sizing: border-box;
  border-radius: 10rpx;
  width: 90rpx;
  height: 90rpx;
  margin-right: 20rpx;
  background-color: #fff;
}
.page .top .infr .infr-img .imgx.data-v-77a1928e {
  width: 70%;
  height: 70%;
}
.page .top .infr .infr-name.data-v-77a1928e {
  box-sizing: border-box;
  padding: 0 20rpx;
  height: 90rpx;
  border-radius: 10rpx;
  background-color: #fff;
}
.page .top .infr .infr-name input.data-v-77a1928e {
  width: 100%;
  height: 100%;
}
.page .top .search.data-v-77a1928e {
  position: relative;
  width: calc(100% - 40rpx);
  height: 110rpx;
  margin: 0 auto;
  border-radius: 14rpx;
  background-color: #fff;
}
.page .top .search .choice-list.data-v-77a1928e {
  box-sizing: border-box;
  width: 0;
  height: 110rpx;
  white-space: nowrap;
}
.page .top .search .choice-list.data-v-77a1928e ::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
  color: transparent;
}
.page .top .search .choice-list .screen-scroll-view.data-v-77a1928e {
  min-width: 100%;
  height: 100%;
  box-sizing: border-box;
}
.page .top .search .choice-list .list-box.data-v-77a1928e {
  width: 80rpx;
  height: 80rpx;
  border-radius: 10rpx;
  margin-left: 20rpx;
  margin-top: 15rpx;
  background-color: #f1f1f1;
  display: inline-block;
  overflow: hidden;
}
.page .top .search .search-box.data-v-77a1928e {
  position: relative;
  width: 250rpx;
  height: 120rpx;
}
.page .top .search .search-box .search-input.data-v-77a1928e {
  box-sizing: border-box;
  padding: 0 20rpx;
  position: absolute;
  z-index: 3;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.page .top .search .search-box .search-input input.data-v-77a1928e {
  width: 100%;
  height: 100%;
  text-align: center;
}
.page .top .search .search-box .search-icon.data-v-77a1928e {
  width: 34rpx;
  height: 34rpx;
  margin-right: 16rpx;
}
.page .top .search .search-box .search-text.data-v-77a1928e {
  color: #9b9b9b;
}
.item.data-v-77a1928e {
  box-sizing: border-box;
  width: 100%;
  padding: 0 0 0 20rpx;
}
.item .choice.data-v-77a1928e {
  opacity: 0;
  width: 0rpx;
  height: 0rpx;
  margin-right: 0rpx;
  background-color: #fff;
  border-radius: 50%;
  border: 1px solid #999;
  transition: all 0.3s;
}
.item .choice .img.data-v-77a1928e {
  width: 80%;
  height: 80%;
  margin-top: 4rpx;
}
.item .choice_.data-v-77a1928e {
  background-color: #4ac165;
  border: 1px solid #4ac165;
}
.item .showChoice.data-v-77a1928e {
  opacity: 1;
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}
.item .item-img.data-v-77a1928e {
  width: 80rpx;
  height: 80rpx;
  border-radius: 10rpx;
  margin-right: 30rpx;
  overflow: hidden;
  background-color: #f1f1f1;
}
.item .item-name.data-v-77a1928e {
  position: relative;
  width: 100%;
  height: 120rpx;
}
.item .item-name .m-line.data-v-77a1928e {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
}
.next-list.data-v-77a1928e {
  position: relative;
  width: 100%;
  height: 0;
  border-radius: 10rpx 10rpx 0 0;
  box-sizing: border-box;
  background-color: #fff;
  overflow: hidden;
}
.next-list .next-scroll-left.data-v-77a1928e {
  height: 100%;
}
.next-list .no-data.data-v-77a1928e {
  width: 100%;
}
.next-list .no-data .no-data-img.data-v-77a1928e {
  width: 200rpx;
  height: 200rpx;
  margin-top: 100rpx;
  margin-bottom: 20rpx;
}
