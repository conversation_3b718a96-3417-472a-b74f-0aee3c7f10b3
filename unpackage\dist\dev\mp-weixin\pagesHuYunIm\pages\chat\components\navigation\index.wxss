@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.topping.data-v-2861e4a0 {
  position: relative;
  top: -10rpx;
  box-sizing: border-box;
  padding: 0 20rpx;
  width: calc(100% - 40rpx);
  height: 0rpx;
  margin: 0rpx auto;
  box-sizing: border-box;
  border-radius: 10rpx;
  background-color: #fff;
  overflow: hidden;
  opacity: 0;
  transition: all 0.3s;
}
.topping .topping-icon.data-v-2861e4a0 {
  width: 50rpx;
  height: 50rpx;
  margin-bottom: 4rpx;
  margin-right: 16rpx;
}
.topping_.data-v-2861e4a0 {
  position: relative;
  top: 0rpx;
  height: 80rpx;
  margin: 20rpx auto;
  opacity: 1;
}
.wx-srt.data-v-2861e4a0 {
  height: 20rpx;
}
.popup-box.data-v-2861e4a0 {
  box-sizing: border-box;
  padding: 0 20rpx 20rpx 20rpx;
  width: 100%;
  max-height: 70vh;
  border-radius: 0 0 20rpx 20rpx;
  background-color: #fff;
}
.popup-box .content.data-v-2861e4a0 {
  box-sizing: border-box;
  width: 100%;
  overflow-x: auto;
}
.popup-box .popup-box-bottom.data-v-2861e4a0 {
  width: 100%;
  height: 60rpx;
  margin-top: 10rpx;
  flex-direction: row-reverse;
}
.popup-box .popup-box-bottom .button.data-v-2861e4a0 {
  width: 150rpx;
  height: 60rpx;
  border-radius: 10rpx;
  background-color: #05c160;
}
.popup-box .popup-box-bottom .button .button-icon.data-v-2861e4a0 {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 4rpx;
  margin-right: 10rpx;
}
.navigationBar-box.data-v-2861e4a0 {
  position: fixed;
  z-index: 98;
  top: 0;
  left: 0;
  width: 100%;
  padding-bottom: 8px;
  background-color: rgba(237, 237, 237, 0.9);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
.navigationBar-box .navigationBar.data-v-2861e4a0 {
  width: 100%;
}
.navigationBar-box .navigationBar .navigationBar-icon.data-v-2861e4a0 {
  width: 40rpx;
  height: 40rpx;
  margin: 0 30rpx;
}
.navigationBar-box .navigationBar .navigationBar-icon .img.data-v-2861e4a0 {
  width: 90%;
  height: 90%;
}
.navigationBar-box .navigationBar .navigationBar-text.data-v-2861e4a0 {
  font-size: 16px;
  margin: 0 20rpx;
}
