<template>
	<view class="page">
		<m-return></m-return>
		<view class="contacts">
			<view class="contacts-container">
				<view class="user-list" v-if="groups.length">
					<view class="user-list-item" v-for="(group, key) in groups" :key="key" @click="enterChat(group.group_id)">
						<view class="user-item-avatar">
							<image class="img" :src="group.group_info.avatar" mode="aspectFill" />
						</view>
						<view class="user-item-info">
							<span class="user-item-info__name">{{ group.group_info.name }}</span>
						</view>
					</view>
				</view>
				<view class="nouser-list" v-else>暂无数据</view>
			</view>
		</view>
	</view>
</template>

<script>
import { to, jsonUrl } from '@/utils/index.js';
export default {
	data() {
		return {
			groups: []
		};
	},
	onLoad(e) {
		this.init();
	},
	methods: {
		to,
		init() {
			henglang.get('Group/group', {}, false, (res) => {
				this.groups = res.data.data;
			});
		},
		enterChat(group_id) {
			console.log(group_id)
			to(`/pagesGoEasy/chat_page/index?groupId=${group_id}`);
		}
	}
};
</script>

<style>
.contacts {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	padding-bottom: 0;
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
}

.contacts .contacts-container {
	width: 100%;
	overflow: auto;
}

.contacts .user-list-item {
	box-sizing: border-box;
	height: 132rpx;
	padding: 32rpx;
	display: flex;
	align-items: center;
}

.contacts .contacts-title {
	height: 80rpx;
	line-height: 80rpx;
	font-size: 30rpx;
	color: #666666;
	background: #f3f4f7;
	text-indent: 44rpx;
}

.contacts .user-list {
	flex-grow: 1;
	background: #ffffff;
	display: flex;
	flex-direction: column;
}

.nouser-list {
	width: 100%;
	height: 200rpx;
	text-align: center;
	line-height: 200rpx;
}

.contacts .user-item-avatar {
	width: 96rpx;
	height: 96rpx;
	border-radius: 10rpx;
	margin-right: 32rpx;
	overflow: hidden;
	position: relative;
}
.contacts .user-item-info {
	height: 130rpx;
	padding-right: 32rpx;
	line-height: 88rpx;
	flex-grow: 1;
	border-bottom: 1px solid #efefef;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.contacts .user-item-info__name {
	font-size: 30rpx;
	font-family: Source Han Sans CN;
	font-style: normal;
	font-weight: bold;
	color: #262628;
}

.contacts .user-item-info__tips {
	height: 44rpx;
	width: 64rpx;
	border-radius: 24rpx;
	font-size: 26rpx;
	line-height: 44rpx;
	background: #d02129;
	color: #ffffff;
	font-family: Cabin;
	text-align: center;
}

.contacts .online-dot {
	position: absolute;
	width: 32rpx !important;
	height: 32rpx !important;
	right: 0;
	bottom: 0;
}

.contacts .online-tips {
	font-size: 28rpx;
	color: #666666;
}
</style>
