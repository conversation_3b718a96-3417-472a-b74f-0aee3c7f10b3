
page {
	background-color: #ededed;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.row.data-v-6f99e77d {
  box-sizing: border-box;
  padding: 10rpx 20rpx 10rpx 30rpx;
  width: calc(100% - 60rpx);
  height: 100rpx;
  background-color: #fff;
  margin: 30rpx auto;
  border-radius: 10rpx;
}
.row .row-img.data-v-6f99e77d {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}
.row .row-input.data-v-6f99e77d {
  width: 200rpx;
  height: 100%;
  margin: 0 10rpx;
}
.row .row-input .input.data-v-6f99e77d {
  width: 100%;
  height: 50rpx;
  text-align: right;
}
.row-num.data-v-6f99e77d {
  position: relative;
  top: -10rpx;
  width: calc(100% - 60rpx);
  margin: 0 auto;
  box-sizing: border-box;
  padding: 0 30rpx;
}
.title.data-v-6f99e77d {
  position: relative;
  padding-top: 20rpx;
  top: 10rpx;
  width: calc(100% - 60rpx);
  margin: 0 auto;
  box-sizing: border-box;
  padding: 0 30rpx;
  color: #ba9d63;
}
.title .title-icon.data-v-6f99e77d {
  width: 26rpx;
  height: 26rpx;
  margin-left: 4rpx;
}
.money-box.data-v-6f99e77d {
  width: 100%;
  height: 100rpx;
  margin: 120rpx auto 40rpx auto;
}
.money-box .money-value.data-v-6f99e77d {
  font-size: 80rpx;
}
.button.data-v-6f99e77d {
  position: relative;
  overflow: hidden;
  width: 330rpx;
  height: 90rpx;
  border-radius: 16rpx;
  background-color: #ff6146;
  margin: 0 auto;
}
.illustrate.data-v-6f99e77d {
  position: absolute;
  bottom: 150rpx;
  left: 0;
  width: 100%;
  height: 50rpx;
  color: #6a6a6a;
}
.row-user .row-user-box.data-v-6f99e77d {
  flex-direction: row-reverse;
}
.row-user .row-user-box .row-user-img.data-v-6f99e77d {
  width: 54rpx;
  height: 54rpx;
  margin-right: 16rpx;
  border-radius: 6rpx;
  overflow: hidden;
  background-color: #f1f1f1;
}
.row-user .row-icon.data-v-6f99e77d {
  width: 30rpx;
  height: 30rpx;
  margin-left: 10rpx;
}
.row_input.data-v-6f99e77d {
  box-sizing: border-box;
  margin-left: 20rpx;
}
