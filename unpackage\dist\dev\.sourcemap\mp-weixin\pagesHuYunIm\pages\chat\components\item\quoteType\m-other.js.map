{"version": 3, "sources": ["webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/quoteType/m-other.vue?f118", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/quoteType/m-other.vue?0477", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/quoteType/m-other.vue?08e5", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/quoteType/m-other.vue?3955", "uni-app:///pagesHuYunIm/pages/chat/components/item/quoteType/m-other.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/quoteType/m-other.vue?baee", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/quoteType/m-other.vue?472c"], "names": ["props", "isMy", "type", "default", "value", "data", "created", "computed", "renderTextMessage", "video", "red_envelope", "map", "emoji_pack", "article", "share_SBCF", "share_mall", "functional_module", "methods", "getTimes", "h", "m", "s"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACoM;AACpM,gBAAgB,yLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAywB,CAAgB,6qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACU7xB;;;;;;;;;;;AACA;AACA;AAAA,eACA;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;EACA;EACAC;EACAC;IACAC;MAAA;MACA;QAAAN;MACA;QACAO;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACA;MACAC;MACAC;MACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAAg9C,CAAgB,ouCAAG,EAAC,C;;;;;;;;;;;ACAp+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/pages/chat/components/item/quoteType/m-other.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./m-other.vue?vue&type=template&id=8c6c9358&scoped=true&\"\nvar renderjs\nimport script from \"./m-other.vue?vue&type=script&lang=js&\"\nexport * from \"./m-other.vue?vue&type=script&lang=js&\"\nimport style0 from \"./m-other.vue?vue&type=style&index=0&id=8c6c9358&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"8c6c9358\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/chat/components/item/quoteType/m-other.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-other.vue?vue&type=template&id=8c6c9358&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-other.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-other.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"quote-box\">\n\t\t<text class=\"quote-name\">\n\t\t\t{{ value.senderData.name }}：\n\t\t\t<text>{{ renderTextMessage }}</text>\n\t\t</text>\n\t</view>\n</template>\n\n<script>\nimport { EmojiDecoder, emojiMap } from '../../../../../lib/EmojiDecoder.js';\nconst emojiUrl = 'https://imgcache.qq.com/open/qcloud/tim/assets/emoji/';\nconst decoder = new EmojiDecoder(emojiUrl, emojiMap);\nexport default {\n\tprops: {\n\t\tisMy: {\n\t\t\ttype: [Boolean, Number],\n\t\t\tdefault: false\n\t\t},\n\t\tvalue: {\n\t\t\ttype: Object,\n\t\t\tdefault: {}\n\t\t}\n\t},\n\tdata() {\n\t\treturn {};\n\t},\n\tcreated() {},\n\tcomputed: {\n\t\trenderTextMessage() {\n\t\t\tconst { type = '' } = this.value;\n\t\t\tconst typeText = {\n\t\t\t\tvideo: `[视频]${this.getTimes()}`,\n\t\t\t\tred_envelope: '[蝌蚪红包]',\n\t\t\t\tmap: `[位置]${this.value.payload?.title}`,\n\t\t\t\temoji_pack: `[表情包]`,\n\t\t\t\tarticle: '[文章分享]',\n\t\t\t\tshare_SBCF: '[商家分享]',\n\t\t\t\tshare_mall: '[商品分享]',\r\n\t\t\t\tfunctional_module: '[功能分享]'\n\t\t\t};\n\t\t\treturn typeText[type];\n\t\t}\n\t},\n\tmethods: {\n\t\tgetTimes() {\n\t\t\tif (this.value.type !== 'video') return;\n\t\t\tconst t = this.value.payload.video.duration;\n\t\t\tlet h = parseInt((t / 60 / 60) % 24);\n\t\t\tlet m = parseInt((t / 60) % 60);\n\t\t\tlet s = parseInt(t % 60);\n\t\t\th = h < 10 ? '0' + h : h;\n\t\t\tm = m < 10 ? '0' + m : m;\n\t\t\ts = s < 10 ? '0' + s : s;\n\t\t\tif (h === '00') return `${m}:${s}`;\n\t\t\treturn `${h}:${m}:${s}`;\n\t\t}\n\t}\n};\n</script>\n\n<style scoped lang=\"scss\">\n.quote-box {\n\tbox-sizing: border-box;\n\tpadding: 12rpx 16rpx;\n\tborder-radius: 10rpx;\n\tmargin-top: 6rpx;\n\tbackground-color: #e1e1e1;\n\tcolor: #6b6b6b;\n}\n.quote-name {\n}\n.quote-content {\n}\n</style>\n", "import mod from \"-!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-other.vue?vue&type=style&index=0&id=8c6c9358&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-other.vue?vue&type=style&index=0&id=8c6c9358&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755067560185\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}