<template>
	<view class="page">
		<m-return></m-return>
		<view class="container">
			<view class="group_item">
				<view class="group_content">
					<view class="group_title">建群名称</view>
					<input type="text" value="" class="group_name" v-model="group_name" placeholder="请输入群名称" placeholder-class="group_name_place" />
				</view>
			</view>
			<view class="group_item group_avatar">
				<view class="group_content">
					<view class="group_title">建群头像</view>
					<image :src="group_img" mode="" class="create_group_icon" @click="chooseImage"></image>
				</view>
			</view>
			<view class="create_group_btn" @click="create_group">建群</view>
		</view>
	</view>
</template>

<script>
import { to } from '@/utils/index.js';
export default {
	data() {
		return {
			group_name: '',
			group_avatar: '',
			group_img: '/pagesGoEasy/static/icon/create_group.png',
			token: 'ZXlKMGVYQWlPaUpLVjFRaUxDSmhiR2NpT2lKSVV6STFOaUo5LmV5SnBjM01pT2lKb2RIUndjenBjTDF3dloyUm9aVzVuYkdGdVp5NWpiMjBpTENKaGRXUWlPaUlpTENKcFlYUWlPakUyTWpRd09UQXpPVElzSW01aVppSTZNVFl5TkRBNU1ETTVNaXdpWkdGMFlTSTZleUp0WlcxaVpYSmZhV1FpT2pnMk1qazBMQ0p0YjJKcGJHVWlPaUl4T0RFd01ESTFNakExTUNKOWZRLk5TSGRIWXRzVDRnRFZZSzl5d3drMkxxVXVuOGgyY2JWRnJzYmtIQjFmSkU='
		};
	},
	methods: {
		create_group() {
			let _this = this;
			if (_this.group_name == '') return henglang.showToast('请输入群组名称');
			if (_this.group_avatar == '') return henglang.showToast('请选择群组头像');
			uni.showLoading({
				title: '加载中'
			});
			new Promise((resolve, reject) => {
				uni.uploadFile({
					url: henglang.host + 'Upload/uploadCustom?folder=group_avatar',
					fileType: 'image',
					filePath: _this.group_avatar,
					name: 'photo',
					header: {
						'app-key': henglang.app_key,
						'access-token': _this.token
					},
					success: (uploadFileRes) => {
						let data = uploadFileRes.data;
						data = JSON.parse(data);
						if (data.code == 0) {
							let resolve_data = {
								group_avatar: data.data.url
							};
							resolve(resolve_data);
						} else {
							uni.hideLoading();
							henglang.showToast(data.msg, 1500);
						}
					}
				});
			}).then((data) => {
				henglang.post('Group/openGroup', { name: _this.group_name, avatar: data.group_avatar }, false, function (res) {
					uni.hideLoading();
					if (res.data.code == 0) {
						setTimeout(() => {
							to(`/pagesGoEasy/chat_page/index?groupId=${res.data.data.group_id}`);
							// to('/pagesGoEasy/admin/member?group_id=' + res.data.data.group_id, {}, 'redirectTo');
						}, 1000);
					}
					henglang.showToast(res.data.msg, 1500);
				});
			});
		},
		chooseImage() {
			let _this = this;
			uni.chooseImage({
				count: 1, //默认9
				sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
				sourceType: ['album', 'camera'], //从相册选择
				success: function (res) {
					_this.group_avatar = _this.group_img = res.tempFilePaths[0];
				}
			});
		}
	}
};
</script>

<style>
.page {
	background-color: #f5f5f5;
	height: 100vh;
	width: 100%;
}
.container {
	height: 448rpx;
	background-color: #fff;
	width: 100%;
	overflow: hidden;
}
.group_content {
	width: 690rpx;
	height: 100%;
	margin: 0 auto;
}
.group_item {
	width: 100%;
	height: 117rpx;
	line-height: 117rpx;
	border-bottom: 1rpx solid #e6e6e6;
}
.group_title {
	font-size: 35rpx;
	width: 50%;
	height: 100%;
	float: left;
}
.group_name {
	float: right;
	width: 50%;
	height: 100%;
	color: #999;
	font-size: 35rpx;
	text-align: right;
}
.group_name_place {
	text-indent: 52px;
}
.group_avatar {
	height: 156rpx !important;
}
.group_avatar .group_title {
	line-height: 156rpx;
}
.create_group_icon {
	width: 88rpx;
	height: 88rpx;
	float: right;
	margin-top: 35rpx;
}
.create_group_btn {
	width: 690rpx;
	height: 88rpx;
	margin: 0 auto;
	background: linear-gradient(to right, #50c0bb, #5dd7d2);
	border-radius: 8rpx;
	line-height: 88rpx;
	text-align: center;
	color: #fff;
	font-size: 34rpx;
	margin-top: 43rpx;
}
</style>
