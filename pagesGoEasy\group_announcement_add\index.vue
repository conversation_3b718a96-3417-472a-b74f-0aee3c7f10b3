<template>
	<view class="flex_c_c page">
		<view class="flex1 box">
			<view class="text_30 box-title">预览：</view>
			<view class="text_32" :style="{ whiteSpace: 'pre-wrap' }" v-html="renderTextMessage"></view>
		</view>
		<view class="bottom-operation-box">
			<view class="flex_r line-break" v-show="keyboardHeight">
				<view class="icon_ text_28 color__ line-break-box" @click="lineBreak">
					<view class="icon_ line-break-icon">
						<image
							class="img"
							src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTAgMGgxMDI0djEwMjRIMHoiIGZpbGw9IiMyYzJjMmMiIGZpbGwtb3BhY2l0eT0iLjAxIi8+PHBhdGggZD0iTTY4Mi42NjcgMTI4YTI5OC42NjcgMjk4LjY2NyAwIDAgMSAxMC4yNCA1OTcuMTYzbC0xMC4yNC4xN2gtNTEyYTQyLjY2NyA0Mi42NjcgMCAwIDEtNC45OTItODUuMDM0bDQuOTkyLS4yOTloNTEyYTIxMy4zMzMgMjEzLjMzMyAwIDAgMCA5LjI1OC00MjYuNDUzbC05LjI1OC0uMjE0aC01MTJhNDIuNjY3IDQyLjY2NyAwIDAgMS00Ljk5Mi04NS4wMzRsNC45OTItLjI5OWg1MTJ6IiBmaWxsPSIjMmMyYzJjIi8+PHBhdGggZD0iTTI0Ny4xNjggNDYwLjUwMWE0Mi42NjcgNDIuNjY3IDAgMCAxIDYzLjg3MiA1Ni4zMmwtMy41NDEgNC4wMTEtMTYwIDE2MCAxNTguMTY1IDE0MC42M2E0Mi42NjcgNDIuNjY3IDAgMCAxIDYuODI3IDU1Ljk3OGwtMy4yODYgNC4yNjdhNDIuNjY3IDQyLjY2NyAwIDAgMS01NS45NzggNi44MjZsLTQuMjY3LTMuMzI4LTE5Mi0xNzAuNjY2YTQyLjY2NyA0Mi42NjcgMCAwIDEtNS4yNDgtNTguMTU1bDMuNDEzLTMuODgzIDE5Mi0xOTJ6IiBmaWxsPSIjMmMyYzJjIi8+PC9zdmc+"
							mode="aspectFill"
						></image>
					</view>
					换行
				</view>
			</view>
			<view class="flex_r bottom-operation">
				<view style="width: 10rpx"></view>
				<view class="flex_c_c flex1">
					<view class="bottom-operation-input">
						<textarea
							class="input"
							auto-height="true"
							confirm-type="done"
							type="text"
							:maxlength="-1"
							:focus="isFocus"
							:adjust-position="false"
							v-model="text"
							confirm-hold
							@confirm="sendingText"
							@focus="focus"
							@blur="isFocus = false"
							@keyboardheightchange="keyboardheightchange"
						/>
					</view>
				</view>
				<view style="width: 10rpx"></view>
				<view class="icon_ bottom-operation-icon" @click="tapEmoji">
					<image class="img" :src="b" mode="aspectFill"></image>
				</view>
			</view>
			<view>
				<emoji v-model="isEmoji" @onEmoji="onEmoji" @deleteFn="deleteFn" @sendingText="sendingText" @sendingEmojiPack="sendingEmojiPack"></emoji>
			</view>
			<!-- 键盘高度 -->
			<view class="keyboard" :style="{ height: keyboardHeight + 'px' }"></view>
			<view v-if="keyboardHeight === 0">
				<m-bottom-paceholder></m-bottom-paceholder>
			</view>
		</view>
	</view>
</template>

<script>
import { show, to, jsonUrl } from '@/utils/index.js';
import emoji from './emoji.vue';

import { EmojiDecoder, emojiMap } from '../lib/EmojiDecoder.js';
const emojiUrl = 'https://imgcache.qq.com/open/qcloud/tim/assets/emoji/';
const decoder = new EmojiDecoder(emojiUrl, emojiMap);

let group_id = '';
let group_to = {};
export default {
	components: {
		emoji
	},
	data() {
		return {
			b: 'data:image/svg+xml;base64,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',
			isFocus: true, //键盘焦点
			isKeyboard: true,
			isEmoji: false,
			text: '',
			keyboardHeight: 0
		};
	},
	computed: {
		//渲染文本消息，如果包含表情，替换为图片
		//todo:本不需要该方法，可以在标签里完成，但小程序有兼容性问题，被迫这样实现
		renderTextMessage() {
			if (!this.text) return '';
			return '<span>' + decoder.decode(this.text) + '</span>';
		}
	},
	onLoad(e) {
		const data = jsonUrl(e);
		group_id = data.group_id;
		group_to = data.group_to;
		this.text = data.text || '';
		console.log(data);
	},
	methods: {
		keyboardheightchange(e) {
			this.keyboardHeight = e.detail.height;
			this.isEmoji = false;
		},
		tapEmoji() {
			this.isEmoji = !this.isEmoji;
			if (this.isEmoji) {
				this.isKeyboard = true;
			}
			// #ifdef H5
			this.keyboardHeight = 0;
			// #endif
		},
		onEmoji(key) {
			this.text = `${this.text}${key}`;
		},
		// ===========================
		// 获取焦点
		focus(e) {
			this.isFocus = true;
			this.isEmoji = false;
			// #ifdef H5
			this.keyboardHeight = 300;
			// #endif
		},
		// 插入换行符合
		lineBreak() {
			this.text = `${this.text}\r\n`;
			this.$nextTick(() => {
				this.isFocus = true;
			});
		},
		// 删除表情
		deleteFn() {
			const str = this.text.charAt(this.text.length - 1);
			if (str === ']') {
				let metaChars = /\[.*?(\u4e00*\u597d*)\]/g;
				let xstr = '';
				this.text.replace(metaChars, (match) => {
					xstr = match;
				});
				var text = this.text;
				function del(str) {
					return text.slice(0, text.length - str.length);
				}
				this.text = del(xstr);
			} else {
				this.text = this.text.substring(0, this.text.length - 1);
			}
		},

		// =====================
		// 创建发送输入框内容
		async sendingText() {
			
				this.sendingEmojiPack();
				// 更新上一个页面
				uni.$emit('getNotice');
				uni.$off('getNotice');
				await show('提交成功', 1500, 'success');
				to();
			
		},

		// 创建自定义表情包
		sendingEmojiPack() {
			const message = {
				groupId: '22',
				senderData: {},
				senderId:'8888',
				messageId: Date.now(),
				payload: {
					text: this.text
				},
				timestamp: Date.now(),
				type: 'group_notice',
				recalled: false,
				status: 'success',
				isHide: 0
			};
			
			
			uni.$emit('getNoticeSendMessage', message);
			uni.$off('getNoticeSendMessage');
		}
	}
};
</script>

<style lang="scss" scoped>
.page {
	width: 100vw;
	height: 100vh;
	.box {
		box-sizing: border-box;
		padding-top: 10rpx;
		width: calc(100% - 40rpx);
		margin: 0 auto;
	}
}
.bottom-operation-box {
	position: relative;
	z-index: 99;
	width: 100vw;
	background-color: #f6f6f6;
	.line-break {
		position: absolute;
		z-index: 99;
		left: 0;
		top: -58rpx;
		width: 100%;
		height: 60rpx;
		flex-direction: row-reverse;
		.line-break-box {
			position: relative;
			width: 160rpx;
			height: 100%;
			color: #2c2c2c;
			border-radius: 20rpx 0 0 0;
			background-color: #f6f6f6;
			.line-break-icon {
				width: 36rpx;
				height: 36rpx;
				margin-right: 10rpx;
			}
		}
		.line-break-box::before {
			position: absolute;
			left: -60rpx;
			top: 0;
			content: '';
			width: 60rpx;
			height: 60rpx;
			display: block;
			text-align: center;
			background-image: radial-gradient(240rpx at 2rpx 0px, rgba(168, 195, 59, 0) 60rpx, #f6f6f6 60rpx);
		}
	}
}
.bottom-operation {
	box-sizing: border-box;
	padding: 14rpx 10rpx;
	width: 100%;
	align-items: flex-end;
	.bottom-operation-icon {
		width: 80rpx;
		height: 80rpx;
		.img {
			width: 80%;
			height: 80%;
		}
	}
	.bottom-operation-input {
		width: 100%;
		box-sizing: border-box;
		padding: 10rpx 14rpx;
		min-height: 84rpx;
		max-height: 300rpx;
		overflow: auto;
		border-radius: 10rpx;
		background-color: #fff;
		.input {
			width: 100%;
			margin: 10rpx 0;
		}
	}
}
.keyboard {
	transition: all 0.2s;
}

// 引用
.quote {
	box-sizing: border-box;
	padding: 0 20rpx;
	width: 100%;
	height: 50rpx;
	margin-top: 8rpx;
	border-radius: 10rpx;
	background-color: #eaeaea;
	color: #686868;
	.quote-row {
		width: 200rpx;
		text-overflow: ellipsis;
		overflow: auto;
		white-space: nowrap;
		::v-deep .quote-box {
			width: 100%;
			box-sizing: border-box;
			padding: 0;
			border-radius: 0;
			margin-top: 0;
			background-color: #eaeaea;
			color: #6b6b6b;
			.quote-name {
			}

			.m-image {
				border-radius: 6rpx;
				overflow: hidden;
				.img {
					width: 40rpx;
					height: 40rpx;
					border-radius: 6rpx;
					overflow: hidden;
					background-color: #fff;
				}
			}
		}
	}

	.quote-icon {
		width: 40rpx;
		height: 40rpx;
	}
}
</style>
