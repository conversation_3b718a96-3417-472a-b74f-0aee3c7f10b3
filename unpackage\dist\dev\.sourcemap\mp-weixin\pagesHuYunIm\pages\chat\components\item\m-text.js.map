{"version": 3, "sources": [null, "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/m-text.vue?ce53", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/m-text.vue?22b6", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/m-text.vue?ed52", "uni-app:///pagesHuYunIm/pages/chat/components/item/m-text.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/m-text.vue?3d91", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/m-text.vue?fc8f"], "names": ["components", "mText", "mImage", "mAudio", "mOther", "props", "isMy", "type", "default", "value", "data", "computed", "renderTextMessage", "text", "methods", "onClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACiM;AACjM,gBAAgB,yLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAyvB,CAAgB,4qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACmC7wB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AACA;AACA;AAAA,eACA;EACAA;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;EACA;EACAC;IACA;IACA;IACAC;MACA;QAAAC;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACxEA;AAAA;AAAA;AAAA;AAAo7C,CAAgB,muCAAG,EAAC,C;;;;;;;;;;;ACAx8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/pages/chat/components/item/m-text.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./m-text.vue?vue&type=template&id=b1219e00&scoped=true&\"\nvar renderjs\nimport script from \"./m-text.vue?vue&type=script&lang=js&\"\nexport * from \"./m-text.vue?vue&type=script&lang=js&\"\nimport style0 from \"./m-text.vue?vue&type=style&index=0&id=b1219e00&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b1219e00\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/chat/components/item/m-text.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-text.vue?vue&type=template&id=b1219e00&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-text.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-text.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"flex_c row\">\n    <view class=\"flex_r text-box\" :class=\"{ text_box: isMy }\" @tap.stop=\"onClick\">\n      <view\n        class=\"text_32 text\"\n        :class=\"isMy ? 'text_r' : 'text_l'\"\n        :hover-class=\"isMy ? 'hover_classr' : 'hover_classl'\"\n        :hover-stay-time=\"60\"\n        :style=\"{ whiteSpace: 'pre-wrap' }\"\n        v-html=\"renderTextMessage\"\n      ></view>\n    </view>\n    <view class=\"text_26 flex_r\" :class=\"{ row_: isMy }\" v-if=\"value.type === 'text_quote'\">\n      <view v-if=\"value.payload.quoteSource.type === 'image' || value.payload.quoteSource.type === 'image_transmit'\">\n        <m-image :value=\"value.payload.quoteSource\"></m-image>\n      </view>\n      <view class=\"\" v-else-if=\"value.payload.quoteSource.type === 'audio' || value.payload.quoteSource.type === 'audio_quote'\">\n        <m-audio :value=\"value.payload.quoteSource\"></m-audio>\n      </view>\n      <view class=\"\" v-else-if=\"value.payload.quoteSource.type === 'text' || value.payload.quoteSource.type === 'text_quote'\">\n        <m-text :value=\"value.payload.quoteSource\"></m-text>\n      </view>\n      <view class=\"\" v-else>\n        <m-other :value=\"value.payload.quoteSource\"></m-other>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport mText from './quoteType/m-text.vue'\nimport mImage from './quoteType/m-image.vue'\nimport mAudio from './quoteType/m-audio.vue'\nimport mOther from './quoteType/m-other.vue'\n\nimport { EmojiDecoder, emojiMap } from '../../../../lib/EmojiDecoder.js'\nconst emojiUrl = 'https://imgcache.qq.com/open/qcloud/tim/assets/emoji/'\nconst decoder = new EmojiDecoder(emojiUrl, emojiMap)\nexport default {\n  components: {\n    mText,\n    mImage,\n    mAudio,\n    mOther\n  },\n  props: {\n    isMy: {\n      type: [Boolean, Number],\n      default: false\n    },\n    value: {\n      type: Object,\n      default: {}\n    }\n  },\n  data() {\n    return {}\n  },\n  computed: {\n    //渲染文本消息，如果包含表情，替换为图片\n    //todo:本不需要该方法，可以在标签里完成，但小程序有兼容性问题，被迫这样实现\n    renderTextMessage() {\n      const { text = '' } = this.value.payload\n      if (!text) return '<span>' + '[未知内容]' + '</span>'\n      return '<span>' + decoder.decode(text) + '</span>'\n    }\n  },\n  methods: {\n    onClick() {\n      this.$emit('onClick')\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.row {\n}\n.row_ {\n  flex-direction: row-reverse;\n}\n.text_box {\n  flex-direction: row-reverse;\n}\n.text {\n  position: relative;\n  z-index: 99;\n  box-sizing: border-box;\n  padding: 16rpx 26rpx;\n  border-radius: 8rpx;\n  background-color: #fff;\n\n  word-break: break-all;\n  vertical-align: center;\n}\n\n.text_r {\n  position: relative;\n  background-color: #95ec6a;\n}\n.text_l {\n  position: relative;\n}\n\n.text_r::after {\n  position: absolute;\n  z-index: -1;\n  content: '';\n  top: 26rpx;\n  right: -8rpx;\n  width: 18rpx;\n  height: 18rpx;\n  border-radius: 2px;\n  transform: rotate(45deg);\n  background-color: #95ec6a;\n}\n.text_l::after {\n  position: absolute;\n  z-index: -1;\n  content: '';\n  top: 26rpx;\n  left: -8rpx;\n  width: 18rpx;\n  height: 18rpx;\n  border-radius: 2px;\n  transform: rotate(45deg);\n  background-color: #fff;\n}\n\n.hover_classr {\n  background-color: #89d961;\n}\n.hover_classl {\n  background-color: #e2e2e2;\n}\n\n.hover_classr::after {\n  position: absolute;\n  z-index: -1;\n  content: '';\n  top: 26rpx;\n  right: -8rpx;\n  width: 18rpx;\n  height: 18rpx;\n  border-radius: 2px;\n  transform: rotate(45deg);\n  background-color: #89d961;\n}\n.hover_classl::after {\n  position: absolute;\n  z-index: -1;\n  content: '';\n  top: 26rpx;\n  left: -8rpx;\n  width: 18rpx;\n  height: 18rpx;\n  border-radius: 2px;\n  transform: rotate(45deg);\n  background-color: #e2e2e2;\n}\n</style>\n", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-text.vue?vue&type=style&index=0&id=b1219e00&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-text.vue?vue&type=style&index=0&id=b1219e00&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755069429190\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}