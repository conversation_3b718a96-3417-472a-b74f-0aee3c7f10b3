@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.bottom-operation-box.data-v-5485efa9 {
  position: relative;
  z-index: 9999;
  width: 100vw;
  background-color: #f6f6f6;
}
.bottom-operation-box .line-break.data-v-5485efa9 {
  position: absolute;
  z-index: 99;
  left: 0;
  top: -58rpx;
  width: 100%;
  height: 60rpx;
  flex-direction: row-reverse;
}
.bottom-operation-box .line-break .line-break-box.data-v-5485efa9 {
  position: relative;
  width: 160rpx;
  height: 100%;
  color: #2c2c2c;
  border-radius: 20rpx 0 0 0;
  background-color: #f6f6f6;
}
.bottom-operation-box .line-break .line-break-box .line-break-icon.data-v-5485efa9 {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
}
.bottom-operation-box .line-break .line-break-box.data-v-5485efa9::before {
  position: absolute;
  left: -60rpx;
  top: 0;
  content: "";
  width: 60rpx;
  height: 60rpx;
  display: block;
  text-align: center;
  background-image: radial-gradient(240rpx at 2rpx 0px, rgba(168, 195, 59, 0) 60rpx, #f6f6f6 60rpx);
}
.bottom-operation.data-v-5485efa9 {
  box-sizing: border-box;
  padding: 14rpx 10rpx;
  width: 100%;
  align-items: flex-end;
}
.bottom-operation .bottom-operation-icon.data-v-5485efa9 {
  width: 80rpx;
  height: 80rpx;
}
.bottom-operation .bottom-operation-icon .img.data-v-5485efa9 {
  width: 80%;
  height: 80%;
}
.bottom-operation .bottom-operation-input.data-v-5485efa9 {
  width: 100%;
  box-sizing: border-box;
  padding: 10rpx 14rpx;
  min-height: 84rpx;
  max-height: 300rpx;
  overflow: auto;
  border-radius: 10rpx;
  background-color: #fff;
}
.bottom-operation .bottom-operation-input .input.data-v-5485efa9 {
  width: 100%;
  margin: 10rpx 0;
}
.keyboard.data-v-5485efa9 {
  transition: all 0.2s;
}
.quote.data-v-5485efa9 {
  box-sizing: border-box;
  padding: 0 20rpx;
  width: 100%;
  height: 50rpx;
  margin-top: 8rpx;
  border-radius: 10rpx;
  background-color: #eaeaea;
  color: #686868;
}
.quote .quote-row.data-v-5485efa9 {
  width: 200rpx;
  text-overflow: ellipsis;
  overflow: auto;
  white-space: nowrap;
}
.quote .quote-row.data-v-5485efa9  .quote-box {
  width: 100%;
  box-sizing: border-box;
  padding: 0;
  border-radius: 0;
  margin-top: 0;
  background-color: #eaeaea;
  color: #6b6b6b;
}
.quote .quote-row.data-v-5485efa9  .quote-box .m-image {
  border-radius: 6rpx;
  overflow: hidden;
}
.quote .quote-row.data-v-5485efa9  .quote-box .m-image .img {
  width: 40rpx;
  height: 40rpx;
  border-radius: 6rpx;
  overflow: hidden;
  background-color: #fff;
}
.quote .quote-icon.data-v-5485efa9 {
  width: 40rpx;
  height: 40rpx;
}
