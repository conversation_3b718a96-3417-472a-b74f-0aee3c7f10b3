/**
 * 公共API接口
 * 提供用户列表、消息等相关接口
 */

/**
 * 获取用户列表
 * @param {Object} params - 请求参数
 * @returns {Promise} 返回Promise，包含[res, err]格式的数据
 */
export function listUser(params = {}) {
  return new Promise((resolve) => {
    // 模拟API延迟
    setTimeout(() => {
      // 模拟用户数据
      const mockData = [
        {
          id: 'group_001',
          title: '技术交流群',
          avatar: 'https://dummyimage.com/100x100/4CAF50/ffffff?text=技术',
          updateTime: Date.now() - 1000 * 60 * 5, // 5分钟前
          notReadNum: 3,
          lastMsg: {
            userId: 'user_001',
            nickname: '张三',
            content: '大家好，有个技术问题想请教一下',
            msgType: 'text',
            createTime: Date.now() - 1000 * 60 * 5
          },
          userArr: [
            {
              userId: 'user_001',
              nickname: '张三',
              avatar: 'https://dummyimage.com/80x80/2196F3/ffffff?text=张'
            },
            {
              userId: 'user_002', 
              nickname: '李四',
              avatar: 'https://dummyimage.com/80x80/FF9800/ffffff?text=李'
            }
          ]
        },
        {
          id: 'group_002',
          title: '产品讨论组',
          avatar: 'https://dummyimage.com/100x100/9C27B0/ffffff?text=产品',
          updateTime: Date.now() - 1000 * 60 * 30, // 30分钟前
          notReadNum: 0,
          lastMsg: {
            userId: 'user_003',
            nickname: '王五',
            content: '新版本的UI设计很不错',
            msgType: 'text',
            createTime: Date.now() - 1000 * 60 * 30
          },
          userArr: [
            {
              userId: 'user_003',
              nickname: '王五',
              avatar: 'https://dummyimage.com/80x80/E91E63/ffffff?text=王'
            },
            {
              userId: 'user_004',
              nickname: '赵六',
              avatar: 'https://dummyimage.com/80x80/00BCD4/ffffff?text=赵'
            }
          ]
        },
        {
          id: 'group_003',
          title: '设计师联盟',
          avatar: 'https://dummyimage.com/100x100/FF5722/ffffff?text=设计',
          updateTime: Date.now() - 1000 * 60 * 60 * 2, // 2小时前
          notReadNum: 1,
          lastMsg: {
            userId: 'user_005',
            nickname: '小美',
            content: '[图片]',
            msgType: 'image',
            createTime: Date.now() - 1000 * 60 * 60 * 2
          },
          userArr: [
            {
              userId: 'user_005',
              nickname: '小美',
              avatar: 'https://dummyimage.com/80x80/795548/ffffff?text=美'
            }
          ]
        },
        {
          id: 'group_004',
          title: '运营小组',
          avatar: 'https://dummyimage.com/100x100/607D8B/ffffff?text=运营',
          updateTime: Date.now() - 1000 * 60 * 60 * 24, // 1天前
          notReadNum: 0,
          lastMsg: {
            userId: 'user_006',
            nickname: '小明',
            content: '[语音]',
            msgType: 'voice',
            createTime: Date.now() - 1000 * 60 * 60 * 24
          },
          userArr: [
            {
              userId: 'user_006',
              nickname: '小明',
              avatar: 'https://dummyimage.com/80x80/3F51B5/ffffff?text=明'
            }
          ]
        },
        {
          id: 'group_005',
          title: '客服团队',
          avatar: 'https://dummyimage.com/100x100/009688/ffffff?text=客服',
          updateTime: Date.now() - 1000 * 60 * 60 * 24 * 2, // 2天前
          notReadNum: 99,
          lastMsg: {
            userId: 'user_007',
            nickname: '客服小助手',
            content: '请问有什么可以帮助您的吗？',
            msgType: 'text',
            createTime: Date.now() - 1000 * 60 * 60 * 24 * 2
          },
          userArr: [
            {
              userId: 'user_007',
              nickname: '客服小助手',
              avatar: 'https://dummyimage.com/80x80/8BC34A/ffffff?text=客'
            }
          ]
        }
      ]

      // 模拟成功响应
      resolve([mockData, null])
    }, 500)
  })
}

/**
 * 发送消息
 * @param {Object} params - 消息参数
 * @returns {Promise} 返回Promise，包含[res, err]格式的数据
 */
export function sendMessage(params) {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟发送成功
      resolve([{ success: true, messageId: Date.now() }, null])
    }, 200)
  })
}

/**
 * 获取消息历史
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回Promise，包含[res, err]格式的数据
 */
export function getMessageHistory(params) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockMessages = [
        {
          messageId: 'msg_001',
          userId: 'user_001',
          nickname: '张三',
          content: '大家好！',
          msgType: 'text',
          createTime: Date.now() - 1000 * 60 * 10
        },
        {
          messageId: 'msg_002',
          userId: 'user_002',
          nickname: '李四',
          content: '你好！',
          msgType: 'text',
          createTime: Date.now() - 1000 * 60 * 5
        }
      ]
      
      resolve([mockMessages, null])
    }, 300)
  })
}

/**
 * 标记消息为已读
 * @param {Object} params - 参数
 * @returns {Promise} 返回Promise，包含[res, err]格式的数据
 */
export function markAsRead(params) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve([{ success: true }, null])
    }, 100)
  })
}

export default {
  listUser,
  sendMessage,
  getMessageHistory,
  markAsRead
}
