import store from '@/store/index.js';
let font_size = {
	page_font_size: 0,
	page_font_size_max: 2,
	page_font_size_max_plus: 4,
	page_font_size_max_plus_pro: 6
};
export class EmojiDecoder {
	emojiMap = null;
	url = "";
	patterns = [];
	metaChars = /[[\]{}()*+?.\\|^$\-,&#\s]/g;

	constructor(url, emojiMap) {
		this.url = url || '';
		this.emojiMap = emojiMap || {};
		for (let i in this.emojiMap) {
			if (this.emojiMap.hasOwnProperty(i)) {
				this.patterns.push('(' + i.replace(this.metaChars, "\\$&") + ')');
			}
		}
	}

	decode(text, size, top = '4px') {
		if (!size) {
			size = `${22 + font_size[store.state.page_font_size]}px`
		}
		return text.replace(new RegExp(this.patterns.join('|'), 'g'), (match) => {
			return typeof this.emojiMap[match] != 'undefined' ?
				`<img style="position: relative;top: ${top};" height="${size}" width="${size}" src="${this.url}${this.emojiMap[match]}" />` :
				match;
		}).replace(/\n/g, '<br/>');
	}
	// 无需换行
	decodeNo(text, size = '22rpx', top = '4px') {
		return text.replace(new RegExp(this.patterns.join('|'), 'g'), (match) => {
			return typeof this.emojiMap[match] != 'undefined' ?
				`<img style="position: relative;top: ${top};" height="${size}" width="${size}" src="${this.url}${this.emojiMap[match]}" />` :
				match;
		});
	}
};
export const emojiMap = {
	"[微笑]": "<EMAIL>",
	"[NO]": "<EMAIL>",
	"[OK]": "<EMAIL>",
	"[下雨]": "<EMAIL>",
	"[么么哒]": "<EMAIL>",
	"[乒乓]": "<EMAIL>",
	"[便便]": "<EMAIL>",
	"[信封]": "<EMAIL>",
	"[偷笑]": "<EMAIL>",
	"[傲慢]": "<EMAIL>",
	"[再见]": "<EMAIL>",
	"[冷汗]": "<EMAIL>",
	"[凋谢]": "<EMAIL>",
	"[刀]": "<EMAIL>",
	"[删除]": "<EMAIL>",
	"[勾引]": "<EMAIL>",
	"[发呆]": "<EMAIL>",
	"[发抖]": "<EMAIL>",
	"[可怜]": "<EMAIL>",
	"[可爱]": "<EMAIL>",
	"[右哼哼]": "<EMAIL>",
	"[右太极]": "<EMAIL>",
	"[右车头]": "<EMAIL>",
	"[吐]": "<EMAIL>",
	"[吓]": "<EMAIL>",
	"[咒骂]": "<EMAIL>",
	"[咖啡]": "<EMAIL>",
	"[啤酒]": "<EMAIL>",
	"[嘘]": "<EMAIL>",
	"[回头]": "<EMAIL>",
	"[困]": "<EMAIL>",
	"[坏笑]": "<EMAIL>",
	"[多云]": "<EMAIL>",
	"[大兵]": "<EMAIL>",
	"[大哭]": "<EMAIL>",
	"[太阳]": "<EMAIL>",
	"[奋斗]": "<EMAIL>",
	"[奶瓶]": "<EMAIL>",
	"[委屈]": "<EMAIL>",
	"[害羞]": "<EMAIL>",
	"[尴尬]": "<EMAIL>",
	"[左哼哼]": "<EMAIL>",
	"[左太极]": "<EMAIL>",
	"[左车头]": "<EMAIL>",
	"[差劲]": "<EMAIL>",
	"[弱]": "<EMAIL>",
	"[强]": "<EMAIL>",
	"[彩带]": "<EMAIL>",
	"[彩球]": "<EMAIL>",
	"[得意]": "<EMAIL>",
	"[心碎了]": "<EMAIL>",
	"[快哭了]": "<EMAIL>",
	"[怄火]": "<EMAIL>",
	"[怒]": "<EMAIL>",
	"[惊恐]": "<EMAIL>",
	"[惊讶]": "<EMAIL>",
	"[憨笑]": "<EMAIL>",
	"[手枪]": "<EMAIL>",
	"[打哈欠]": "<EMAIL>",
	"[抓狂]": "<EMAIL>",
	"[折磨]": "<EMAIL>",
	"[抠鼻]": "<EMAIL>",
	"[抱抱]": "<EMAIL>",
	"[抱拳]": "<EMAIL>",
	"[拳头]": "<EMAIL>",
	"[挥手]": "<EMAIL>",
	"[握手]": "<EMAIL>",
	"[撇嘴]": "<EMAIL>",
	"[擦汗]": "<EMAIL>",
	"[敲打]": "<EMAIL>",
	"[晕]": "<EMAIL>",
	"[月亮]": "<EMAIL>",
	"[棒棒糖]": "<EMAIL>",
	"[汽车]": "<EMAIL>",
	"[沙发]": "<EMAIL>",
	"[流汗]": "<EMAIL>",
	"[流泪]": "<EMAIL>",
	"[激动]": "<EMAIL>",
	"[灯泡]": "<EMAIL>",
	"[炸弹]": "<EMAIL>",
	"[熊猫]": "<EMAIL>",
	"[爆筋]": "<EMAIL>",
	"[爱你]": "<EMAIL>",
	"[爱心]": "<EMAIL>",
	"[爱情]": "<EMAIL>",
	"[猪头]": "<EMAIL>",
	"[猫咪]": "<EMAIL>",
	"[献吻]": "<EMAIL>",
	"[玫瑰]": "<EMAIL>",
	"[瓢虫]": "<EMAIL>",
	"[疑问]": "<EMAIL>",
	"[白眼]": "<EMAIL>",
	"[皮球]": "<EMAIL>",
	"[睡觉]": "<EMAIL>",
	"[磕头]": "<EMAIL>",
	"[示爱]": "<EMAIL>",
	"[礼品袋]": "<EMAIL>",
	"[礼物]": "<EMAIL>",
	"[篮球]": "<EMAIL>",
	"[米饭]": "<EMAIL>",
	"[糗大了]": "<EMAIL>",
	"[红双喜]": "<EMAIL>",
	"[红灯笼]": "<EMAIL>",
	"[纸巾]": "<EMAIL>",
	"[胜利]": "<EMAIL>",
	"[色]": "<EMAIL>",
	"[药]": "<EMAIL>",
	"[菜刀]": "<EMAIL>",
	"[蛋糕]": "<EMAIL>",
	"[蜡烛]": "<EMAIL>",
	"[街舞]": "<EMAIL>",
	"[衰]": "<EMAIL>",
	"[西瓜]": "<EMAIL>",
	"[调皮]": "<EMAIL>",
	"[象棋]": "<EMAIL>",
	"[跳绳]": "<EMAIL>",
	"[跳跳]": "<EMAIL>",
	"[车厢]": "<EMAIL>",
	"[转圈]": "<EMAIL>",
	"[鄙视]": "<EMAIL>",
	"[酷]": "<EMAIL>",
	"[钞票]": "<EMAIL>",
	"[钻戒]": "<EMAIL>",
	"[闪电]": "<EMAIL>",
	"[闭嘴]": "<EMAIL>",
	"[闹钟]": "<EMAIL>",
	"[阴险]": "<EMAIL>",
	"[难过]": "<EMAIL>",
	"[雨伞]": "<EMAIL>",
	"[青蛙]": "<EMAIL>",
	"[面条]": "<EMAIL>",
	"[鞭炮]": "<EMAIL>",
	"[风车]": "<EMAIL>",
	"[飞吻]": "<EMAIL>",
	"[飞机]": "<EMAIL>",
	"[饥饿]": "<EMAIL>",
	"[香蕉]": "<EMAIL>",
	"[骷髅]": "<EMAIL>",
	"[麦克风]": "<EMAIL>",
	"[麻将]": "<EMAIL>",
	"[鼓掌]": "<EMAIL>",
	"[龇牙]": "<EMAIL>"
}