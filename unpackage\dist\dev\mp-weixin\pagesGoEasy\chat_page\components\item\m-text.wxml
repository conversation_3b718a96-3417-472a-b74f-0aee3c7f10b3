<view class="flex_c row data-v-3b7d3c38"><view data-event-opts="{{[['tap',[['onClick',['$event']]]]]}}" class="{{['flex_r','text-box','data-v-3b7d3c38',(isMy)?'text_box':'']}}" catchtap="__e"><view class="{{['text_32','text','data-v-3b7d3c38',isMy?'text_r':'text_l']}}" style="{{'white-space:'+('pre-wrap')+';'}}" hover-class="{{isMy?'hover_classr':'hover_classl'}}" hover-stay-time="{{60}}"><rich-text nodes="{{renderTextMessage}}"></rich-text></view></view><block wx:if="{{value.type==='text_quote'}}"><view class="{{['text_26','flex_r','data-v-3b7d3c38',(isMy)?'row_':'']}}"><block wx:if="{{value.payload.quoteSource.type==='image'||value.payload.quoteSource.type==='image_transmit'}}"><view class="data-v-3b7d3c38"><m-image vue-id="342acbce-1" value="{{value.payload.quoteSource}}" class="data-v-3b7d3c38" bind:__l="__l"></m-image></view></block><block wx:else><block wx:if="{{value.payload.quoteSource.type==='audio'||value.payload.quoteSource.type==='audio_quote'}}"><view class="data-v-3b7d3c38"><m-audio vue-id="342acbce-2" value="{{value.payload.quoteSource}}" class="data-v-3b7d3c38" bind:__l="__l"></m-audio></view></block><block wx:else><block wx:if="{{value.payload.quoteSource.type==='text'||value.payload.quoteSource.type==='text_quote'}}"><view class="data-v-3b7d3c38"><m-text vue-id="342acbce-3" value="{{value.payload.quoteSource}}" class="data-v-3b7d3c38" bind:__l="__l"></m-text></view></block><block wx:else><view class="data-v-3b7d3c38"><m-other vue-id="342acbce-4" value="{{value.payload.quoteSource}}" class="data-v-3b7d3c38" bind:__l="__l"></m-other></view></block></block></block></view></block></view>