<block wx:if="{{show}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="flex_r operate-box data-v-26d70dbe" style="{{$root.s0}}" bindtap="__e"><view class="{{['data-v-26d70dbe',(isSelf)?'flex1':'']}}" style="transition:all 0.2s;"></view><view class="flex_r operate data-v-26d70dbe"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{item.icon}}"><view data-event-opts="{{[['tap',[['onClick',['$0'],[[['list','',index]]]]]]]}}" class="flex_c_c size_white operate-item data-v-26d70dbe" catchtap="__e"><view class="operate-item-icon data-v-26d70dbe"><image class="img data-v-26d70dbe" src="{{item.icon}}" mode="aspectFill"></image></view><view class="text_24 data-v-26d70dbe">{{''+item.title+''}}</view></view></block></block><view class="operate-str data-v-26d70dbe" style="{{$root.s1}}"></view></view></view></block>