{"version": 3, "sources": ["webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/components/m-bottom-paceholder/m-bottom-paceholder.vue?3912", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/components/m-bottom-paceholder/m-bottom-paceholder.vue?9d12", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/components/m-bottom-paceholder/m-bottom-paceholder.vue?8fa3", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/components/m-bottom-paceholder/m-bottom-paceholder.vue?1460", "uni-app:///components/m-bottom-paceholder/m-bottom-paceholder.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/components/m-bottom-paceholder/m-bottom-paceholder.vue?82bc", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/components/m-bottom-paceholder/m-bottom-paceholder.vue?9705"], "names": ["props", "height", "type", "default", "computed", "placeholder<PERSON><PERSON><PERSON>", "style"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4I;AAC5I;AACuE;AACL;AACsC;;;AAGxG;AACwL;AACxL,gBAAgB,yLAAU;AAC1B,EAAE,yFAAM;AACR,EAAE,0GAAM;AACR,EAAE,mHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAytB,CAAgB,yrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eCM7uB;EACAA;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MAOA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC1BA;AAAA;AAAA;AAAA;AAAg3C,CAAgB,gvCAAG,EAAC,C;;;;;;;;;;;ACAp4C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/m-bottom-paceholder/m-bottom-paceholder.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./m-bottom-paceholder.vue?vue&type=template&id=2f38b608&scoped=true&\"\nvar renderjs\nimport script from \"./m-bottom-paceholder.vue?vue&type=script&lang=js&\"\nexport * from \"./m-bottom-paceholder.vue?vue&type=script&lang=js&\"\nimport style0 from \"./m-bottom-paceholder.vue?vue&type=style&index=0&id=2f38b608&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2f38b608\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/m-bottom-paceholder/m-bottom-paceholder.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-bottom-paceholder.vue?vue&type=template&id=2f38b608&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.placeholderStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-bottom-paceholder.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-bottom-paceholder.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"placeholder\" :style=\"[placeholderStyle]\">\r\n\t</view>\r\n</template>\r\n<!-- <m-bottom-paceholder></m-bottom-paceholder> -->\r\n<script>\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\theight: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: '20'\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tplaceholderStyle() {\r\n\t\t\t\tconst style = {}\r\n\t\t\t\tstyle.height = `${this.height}rpx`\r\n\t\t\t\t// #ifndef MP-WEIXIN\r\n\t\t\t\tconst {\r\n\t\t\t\t\tsafeAreaInsets\r\n\t\t\t\t} = uni.getSystemInfoSync()\r\n\t\t\t\tstyle.paddingBottom = `${safeAreaInsets.bottom}px`\r\n\t\t\t\t// #endif\r\n\t\t\t\treturn style;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.placeholder {\r\n\t\twidth: 100%;\r\n\t\theight: 20rpx;\r\n\t\t/* #ifdef MP-WEIXIN*/\r\n\t\tpadding-bottom: 0;\r\n\t\tpadding-bottom: constant(safe-area-inset-bottom); //适配底部\r\n\t\tpadding-bottom: env(safe-area-inset-bottom);\r\n\t\t/*#endif*/\r\n\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-bottom-paceholder.vue?vue&type=style&index=0&id=2f38b608&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-bottom-paceholder.vue?vue&type=style&index=0&id=2f38b608&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755048919997\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}