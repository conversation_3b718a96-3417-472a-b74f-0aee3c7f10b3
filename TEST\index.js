const 自己的信息 = {
	member_id: '1921822887908581377'
}
const 会话列表数据 = [{

	"groupId": "22",
	"type": "group",
	"lastMessage": {
		"groupId": "22",
		"senderData": {
			"name": "荷塘月色",
			"avatar": "https://tse4-mm.cn.bing.net/th/id/OIP-C.duz6S7Fvygrqd6Yj_DcXAQHaF7?rs=1&pid=ImgDetMain",
			"member_id": 105974,
		},
		"senderId": "105974",
		"messageId": "****************",
		"timestamp": *************,
		"type": "audio",
		"recalled": false,
		"status": "success",
		"isHide": 0,
		"pause": 4
	},
	"unread": 1,
	"top": true,
	"data": {
		"name": "饭搭子5人组",
		"avatar": "https://pic1.zhimg.com/v2-f157b063296169e493cca2ed8f429a29_r.jpg",
	}

}, {
	"groupId": "22",
	"type": "group",
	"lastMessage": {
		"groupId": "22",
		"senderData": {
			"name": "荷塘月色",
			"avatar": "https://tse4-mm.cn.bing.net/th/id/OIP-C.duz6S7Fvygrqd6Yj_DcXAQHaF7?rs=1&pid=ImgDetMain",
			"member_id": 105974,
		},
		"senderId": "105974",
		"messageId": "****************",
		"timestamp": *************,
		"type": "audio",
		"recalled": false,
		"status": "success",
		"isHide": 0,
		"pause": 4
	},
	"unread": 0,
	"top": false,
	"data": {
		"name": "饭搭子5人组",
		"avatar": "https://pic3.zhimg.com/v2-4a24869d39b5ca086a76026ba0fb626b_r.jpg",
	}
}, {
	"groupId": "22",
	"type": "group",
	"lastMessage": {
		"groupId": "22",
		"senderData": {
			"name": "荷塘月色",
			"avatar": "https://tse4-mm.cn.bing.net/th/id/OIP-C.duz6S7Fvygrqd6Yj_DcXAQHaF7?rs=1&pid=ImgDetMain",
			"member_id": 105974,
		},
		"senderId": "105974",
		"messageId": "****************",
		"timestamp": *************,
		"type": "audio",
		"recalled": false,
		"status": "success",
		"isHide": 0,
		"pause": 4
	},
	"unread": 1,
	"top": false,
	"data": {
		"name": "饭搭子5人组",
		"avatar": "https://ts1.cn.mm.bing.net/th/id/R-C.********************************?rik=N645Smo8IvV7ag&riu=http%3a%2f%2fimg.zcool.cn%2fcommunity%2f01248d590ad55ca801214550847417.jpg%402o.jpg&ehk=cFxJMScknKBRJWa3z7OAfsSa3lUenwfnFZD4al4u1yc%3d&risl=&pid=ImgRaw&r=0",
	}
}]


const 对话数据 = [
	// 文本
	{
		"groupId": "22",
		"senderData": {
			"name": "荷塘月色",
			"avatar": "https://tse4-mm.cn.bing.net/th/id/OIP-C.duz6S7Fvygrqd6Yj_DcXAQHaF7?rs=1&pid=ImgDetMain",
			"member_id": 87253,
			"group_id": 22
		},
		"senderId": "86294",
		"messageId": "3002014294347082",
		"timestamp": 1722066171410,
		"type": "text",
		"payload": {
			"text": "恭喜 @明天会更好 获得本月最佳干饭奖！[鞭炮][红双喜][鞭炮]"
		},
		"recalled": false,
		"status": "success",
		"isHide": 0
	},
	// 专属红红包
	{
		"groupId": "22",
		"senderData": {
			"name": "荷塘月色",
			"avatar": "https://tse4-mm.cn.bing.net/th/id/OIP-C.duz6S7Fvygrqd6Yj_DcXAQHaF7?rs=1&pid=ImgDetMain",
			"member_id": 87253,
			"group_id": 22
		},
		"senderId": "87253",
		"messageId": "****************",
		"payload": {
			"message_id": "ODcyNTMxNzIyMDY2MTcwNTgwRFpHMQ==",
			"remark": "恭喜发财，大吉大利",
			"account_type_text": "蝌蚪红包",
			"type": "exclusive",
			"red_packet_bg": " /static/images/red/red_bg.jpg",
			"red_packet_bg_mini": " /static/images/red/red_bg.jpg",
			"exclusive": {
				"member_id": 62413,
				"name": "明天会更好",
			}
		},
		"timestamp": *************,
		"type": "red_envelope",
		"recalled": false,
		"status": "success",
		"isHide": 0,
		"had_draw": 0,
		"isClick": 0
	},
	// 拼手气红包
	{
		"groupId": "22",
		"senderData": {
			"name": "荷塘月色",
			"avatar": "https://tse4-mm.cn.bing.net/th/id/OIP-C.duz6S7Fvygrqd6Yj_DcXAQHaF7?rs=1&pid=ImgDetMain",
			"member_id": 87253,
		},
		"senderId": "87253",
		"messageId": "****************",
		"payload": {
			"message_id": "ODcyNTMxNzIyMDY2MTQ3MDgxUjVBUw==",
			"remark": "恭喜发财，大吉大利",
			"account_type_text": "蝌蚪红包",
			"type": "fortune"
		},
		"timestamp": *************,
		"type": "red_envelope",
		"recalled": false,
		"status": "success",
		"isHide": 0,
		"had_draw": 0,
		"isClick": 0
	},
	// 语言
	{
		"groupId": "22",
		"senderData": {
			"name": "明天会更好",
			"avatar": "https://tse4-mm.cn.bing.net/th/id/OIP-C.NimIzUOhgk2QHjPwRE0Q8gHaE5?rs=1&pid=ImgDetMain",
			"member_id": 105974,
		},
		"senderId": "105974",
		"messageId": "****************",
		"timestamp": *************,
		"type": "audio",
		"payload": {
			"contentType": "audio/mp3",
			"name": "uni-audio.mp3",
			"size": 3950,
			"url": "https://xxxxxxxxxxxxx.mp3",
			"duration": 1.652
		},
		"recalled": false,
		"status": "success",
		"isHide": 0,
		"pause": 4
	},
	// 视频
	{
		"groupId": "22",
		"senderData": {
			"name": "春暖花开",
			"avatar": "https://tse4-mm.cn.bing.net/th/id/OIP-C.9tSEVFN0I6YNPit7ENmXiAAAAA?rs=1&pid=ImgDetMain",
			"member_id": 87253
		},
		"senderId": "87253",
		"messageId": "3002033701392785",
		"timestamp": 1721976243714,
		"type": "video",
		"payload": {
			"video": {
				"name": "3002033627992466_uni-video.mp4",
				"url": "https://s1.aigei.com/src/img/jpg/da/da9650d0ba7d42e792fee1ca241e092d.jpg?imageMogr2/auto-orient/thumbnail/!580x435r/gravity/Center/crop/580x435/quality/85/%7CimageView2/2/w/580&e=**********&token=P7S2Xpzfz11vAkASLTkfHN7Fw-oOZBecqeJaxypL:Xl0xuOSOTsQBJxwHv6ClfQGoY14=",
				"width": 640,
				"height": 352,
				"contentType": "video/mp4",
				"size": 501774,
				"duration": 23
			},
			"thumbnail": {
				"name": "uni-thumbnail.jpg",
				"url": "//s1.aigei.com/src/img/jpg/da/da9650d0ba7d42e792fee1ca241e092d.jpg?imageMogr2/auto-orient/thumbnail/!282x159r/gravity/Center/crop/282x159/quality/85/%7CimageView2/2/w/282&e=**********&token=P7S2Xpzfz11vAkASLTkfHN7Fw-oOZBecqeJaxypL:9ik3ZZSOuAFKt8cMkv-PbH2vNhg=",
				"width": 364,
				"height": 200,
				"contentType": "image/jpg"
			}
		},
		"recalled": false,
		"status": "success",
		"isHide": 0
	},
	// 文本
	{
		"groupId": "22",
		"senderData": {
			"name": "本人",
			"avatar": "https://b.zol-img.com.cn/soft/6/221/cee9f2R9bRgRI.jpg",
			"member_id": 105974,
		},
		"senderId": "88888",
		"messageId": "3002014294344082",
		"timestamp": 1721974392819,
		"type": "text",
		"payload": {
			"text": "哇！那今晚晚饭你买单！@明天会更好"
		},
		"recalled": false,
		"status": "success",
		"isHide": 0
	},
	// 商品分享
	{
		"groupId": "22",
		"senderData": {
			"name": "运筹帷幄",
			"avatar": "https://img.zcool.cn/community/0121e25a98e23aa801206d96c1cf46.jpg@1280w_1l_2o_100sh.jpg",
			"member_id": 87253
		},
		"senderId": "72838",
		"messageId": "3000958851219857",
		"timestamp": 1721873738002,
		"type": "share_mall",
		"payload": {
			"title": "沙县小吃",
			"short_title": "沙县小吃万达广场旗舰店",
			"share_image": "https://pic.0598777.com/FvHD6WmgcQ_c1DZM0fWPmGYQRk94",
		},
		"recalled": false,
		"status": "success",
		"isHide": 0
	},
	// 位置分享
	{
		"groupId": "22",
		"senderData": {
			"name": "运筹帷幄",
			"avatar": "https://img.zcool.cn/community/0121e25a98e23aa801206d96c1cf46.jpg@1280w_1l_2o_100sh.jpg",
			"member_id": 87253
		},
		"senderId": "87253",
		"messageId": "3003013379656082",
		"timestamp": 1722069673372,
		"type": "map",
		"payload": {
			"latitude": 39.918732,
			"longitude": 116.459044,
			"title": "环球金融中心",
			"address": "东三环中路1号(金台夕照地铁站A西北口步行280米)",
			"image": "http://mlmh.gdhenglang.com/api/map/staticMap?location=116.459044,39.918732&size=300*170"
		},
		"recalled": false,
		"status": "success",
		"isHide": 0
	},
	// 图片
	{
		"groupId": "22",
		"senderData": {
			"name": "复兴中华",
			"avatar": "https://img.zcool.cn/community/014cdd5a96ba16a801219586209ded.png@1280w_1l_2o_100sh.png",
			"member_id": 87253
		},
		"senderId": "87253",
		"messageId": "3004649910045073",
		"payload": {
			"contentType": "image/jpg",
			"name": "uni-image.jpg",
			"size": 148562,
			"url": "https://aimg8.dlssyht.cn/u/2011304/ueditor/image/1006/2011304/1648876930391557.jpg",
			"width": 600,
			"height": 600,
			"thumbnail": "https://aimg8.dlssyht.cn/u/2011304/ueditor/image/1006/2011304/1648876930391557.jpg"
		},
		"timestamp": 1722225745180,
		"type": "image",
		"recalled": false,
		"status": "success",
		"isHide": 0
	},
	{
		"groupId": "22",
		"senderData": {
			"name": "本人",
			"avatar": "https://b.zol-img.com.cn/soft/6/221/cee9f2R9bRgRI.jpg",
		},
		"senderId": "88888",
		"messageId": "3004707243032977",
		"payload": {
			"text": "我能炫两笼！[坏笑][坏笑]",
			"quoteSource": {
				"groupId": "22",
				"senderData": {
					"name": "复兴中华",
					"avatar": "https://img.zcool.cn/community/014cdd5a96ba16a801219586209ded.png@1280w_1l_2o_100sh.png",
					"member_id": 87253
				},
				"senderId": "87253",
				"messageId": "3004649910045073",
				"payload": {
					"contentType": "image/jpg",
					"name": "uni-image.jpg",
					"size": 148562,
					"url": "https://aimg8.dlssyht.cn/u/2011304/ueditor/image/1006/2011304/1648876930391557.jpg",
					"width": 600,
					"height": 600,
					"thumbnail": "https://aimg8.dlssyht.cn/u/2011304/ueditor/image/1006/2011304/1648876930391557.jpg",
					"quoteSource": {}
				},
				"timestamp": 1722225745180,
				"type": "image",
				"recalled": false,
				"status": "success",
				"isHide": 0
			}
		},
		"timestamp": 1722231212823,
		"type": "text_quote",
		"recalled": false,
		"status": "success",
		"isHide": 0
	},
	// 语言
	{
		"groupId": "22",
		"senderData": {
			"name": "明天会更好",
			"avatar": "https://tse4-mm.cn.bing.net/th/id/OIP-C.NimIzUOhgk2QHjPwRE0Q8gHaE5?rs=1&pid=ImgDetMain",
			"member_id": 105974,
		},
		"senderId": "88888",
		"messageId": "****************",
		"timestamp": *************,
		"type": "audio",
		"payload": {
			"contentType": "audio/mp3",
			"name": "uni-audio.mp3",
			"size": 3950,
			"url": "https://xxxxxxxxxxxxx.mp3",
			"duration": 4
		},
		"recalled": false,
		"status": "success",
		"isHide": 0,
		"pause": 4
	},
]

export {
	自己的信息,
	会话列表数据,
	对话数据
}