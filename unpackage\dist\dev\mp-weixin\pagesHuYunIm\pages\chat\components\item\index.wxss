@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.group_notice.data-v-52f9fea2 {
  width: 100%;
  height: 80rpx;
  color: #a3a3a3;
}
.row_reverse.data-v-52f9fea2 {
  flex-direction: row-reverse;
}
.item.data-v-52f9fea2 {
  box-sizing: border-box;
  padding-bottom: 20rpx;
  position: relative;
  z-index: 0;
  width: calc(100% - 50rpx);
  margin: 0 auto;
}
.item .item-name.data-v-52f9fea2 {
  margin-bottom: 4rpx;
}
.item_.data-v-52f9fea2 {
  flex-direction: row-reverse;
}
.item_ .item-name.data-v-52f9fea2 {
  position: relative;
  top: 0rpx;
  display: none;
}
.item-img.data-v-52f9fea2 {
  position: relative;
  width: 76rpx;
  height: 76rpx;
}
.item-img .item-img-pendant.data-v-52f9fea2 {
  position: absolute;
  z-index: 3;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
}
.item-img .item-img-url.data-v-52f9fea2 {
  width: 76rpx;
  height: 76rpx;
  overflow: hidden;
  border-radius: 6rpx;
  background-color: #fff;
}
.item-content.data-v-52f9fea2 {
  width: calc(100% - 164rpx);
}
.item-content .item-content-box.data-v-52f9fea2 {
  position: relative;
}
.item-content .item-content-box .loading.data-v-52f9fea2 {
  width: 40rpx;
  height: 40rpx;
  margin: 0 10rpx;
}
.item_content.data-v-52f9fea2 {
  position: relative;
  top: -10rpx;
}
.item_content2.data-v-52f9fea2 {
  position: relative;
  top: 0rpx !important;
}
