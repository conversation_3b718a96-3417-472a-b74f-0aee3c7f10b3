<template>
	<uni-popup ref="popup" :safe-area="false" type="bottom" maskBackgroundColor="rgba(000, 000, 000, 0.7)">
		<view class="flex_c_c next">
			<view class="top">
				<view class="icon_ text_32 bold_ top-title">
					<view class="top-title-icon"></view>
					<view class="flex1 icon_">{{ title }}</view>
					<view class="top-title-icon" @click="close">
						<image
							class="img"
							src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjUgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTUxMS45NzkgMTAyNEMyMjkuNjg5IDEwMjQgMCA3OTQuMzEgMCA1MTEuOTc5IDAgMjI5LjY4OSAyMjkuNjkgMCA1MTEuOTc5IDBzNTExLjk3OCAyMjkuNjkgNTExLjk3OCA1MTEuOTc5QzEwMjQgNzk0LjMxIDc5NC4zMSAxMDI0IDUxMS45OCAxMDI0em0wLTk0NS41MDZjLTIzOS4wMTcgMC00MzMuNDg1IDE5NC40NjgtNDMzLjQ4NSA0MzMuNDQyIDAgMjM5LjAxNyAxOTQuNDY4IDQzMy41MjcgNDMzLjQ4NSA0MzMuNTI3IDIzOS4wMTcgMCA0MzMuNDg0LTE5NC40NjcgNDMzLjQ4NC00MzMuNTI3IDAtMjM4Ljk3NC0xOTQuNDI1LTQzMy40NDItNDMzLjQ4NC00MzMuNDQyeiIgZmlsbD0iIzUxNTE1MSIvPjxwYXRoIGQ9Ik01NjEuNjgyIDUxMS45NzlsMTUxLjc1LTE1Mi4xNzZhMzUuOTQ2IDM1Ljk0NiAwIDAgMCAwLTUwLjcyNSAzNS42OSAzNS42OSAwIDAgMC01MC41OTggMGwtMTUxLjc1IDE1Mi4yMTgtMTUxLjc1LTE1Mi4xNzVhMzUuNjkgMzUuNjkgMCAwIDAtNTAuNTk2IDAgMzUuOTQ2IDM1Ljk0NiAwIDAgMCAwIDUwLjcyNUw0NjAuNDg3IDUxMi4wMmwtMTUxLjc1IDE1Mi4xMzNhMzUuNzc2IDM1Ljc3NiAwIDEgMCA1MC41OTggNTAuNzI1bDE1MS43NS0xNTIuMTc1IDE1MS43NDkgMTUyLjE3NWEzNS43NzYgMzUuNzc2IDAgMSAwIDUwLjU5Ny01MC43MjVMNTYxLjY4MSA1MTEuOTh6IiBmaWxsPSIjNTE1MTUxIi8+PC9zdmc+"
							mode="aspectFill"
						></image>
					</view>
				</view>
				<view class="icon_ search">
					<view class="icon_ z_index2" v-if="!focus & !searchStr">
						<view class="search-icon">
							<image
								class="img"
								src="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTQ2OS4zMzMgMEMyMDkuMDY3IDAgMCAyMDkuMDY3IDAgNDY5LjMzM3MyMDkuMDY3IDQ2OS4zMzQgNDY5LjMzMyA0NjkuMzM0UzkzOC42NjcgNzI5LjYgOTM4LjY2NyA0NjkuMzMzIDcyOS42IDAgNDY5LjMzMyAwem0wIDg1My4zMzNjLTIxMy4zMzMgMC0zODQtMTcwLjY2Ni0zODQtMzg0czE3MC42NjctMzg0IDM4NC0zODQgMzg0IDE3MC42NjcgMzg0IDM4NC0xNzAuNjY2IDM4NC0zODQgMzg0eiIgZmlsbD0iIzliOWI5YiIgZGF0YS1zcG0tYW5jaG9yLWlkPSJhMzEzeC5zZWFyY2hfaW5kZXguMC5pMS4xMTdjM2E4MVdwVG9pVyIgY2xhc3M9InNlbGVjdGVkIi8+PHBhdGggZD0iTTczOC4xMzMgNzQyLjRjMTcuMDY3LTE3LjA2NyA0Mi42NjctMTcuMDY3IDU5LjczNCAwbDIwOS4wNjYgMjAwLjUzM2MxNy4wNjcgMTcuMDY3IDE3LjA2NyA0Mi42NjcgMCA1OS43MzQtMTcuMDY2IDE3LjA2Ni00Mi42NjYgMTcuMDY2LTU5LjczMyAwTDczOC4xMzMgODAyLjEzM2MtMTcuMDY2LTE3LjA2Ni0xNy4wNjYtNDIuNjY2IDAtNTkuNzMzeiIgZmlsbD0iIzliOWI5YiIgZGF0YS1zcG0tYW5jaG9yLWlkPSJhMzEzeC5zZWFyY2hfaW5kZXguMC5pNC4xMTdjM2E4MVdwVG9pVyIgY2xhc3M9InNlbGVjdGVkIi8+PC9zdmc+"
								mode="aspectFill"
							></image>
						</view>
						<view class="text_32 search-text">搜索</view>
					</view>

					<view class="search-input">
						<input @input="search" v-model="searchStr" :focus="focus" @focus="focusFn" @blur="blurFn" :adjust-position="false" maxlength="50" />
					</view>
				</view>
			</view>
			<view class="flex1 next-list">
				<scroll-view @scroll="scrollCallback" class="next-scroll-left" scroll-y="true" :scroll-with-animation="true" :scroll-into-view="scrollIntoView">
					<view class="left-list" v-for="(item, index) of scrollLeftObj" :key="index" :id="index != '#' ? index : 'BOTTOM'">
						<view :id="`item${index}`" class="left-item-title" v-if="item && item.length">{{ index === 'no' ? '#' : index }}</view>
						<view class="left-item-card" v-for="(mess, inx) in item" :key="inx" @click.stop="chooseItem(mess)">
							<view v-if="showAvatar">
								<image
									:style="'border-radius:' + radius"
									class="left-item-card-img img-info"
									:src="mess[imgKey]"
									v-if="mess[imgKey]"
									mode="aspectFill"
									@click.stop="preview(mess[imgKey])"
								></image>
							</view>
							<view class="left-item-card-info" :style="inx < item.length - 1 ? 'border-bottom: solid #ededed 1rpx;' : ''">
								<view class="left-item-card-name">{{ mess[nameKey] || '' }}</view>
							</view>
						</view>
					</view>

					<view class="flex_c_c no-data" v-if="!hasData">
						<view class="no-data-img">
							<image
								class="img"
								src="data:image/svg+xml;base64,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"
								mode="aspectFill"
							></image>
						</view>
						<view class="text_26 color__">没查询到人员</view>
					</view>
					<view style="height: 180rpx"></view>
					<m-bottom-paceholder></m-bottom-paceholder>
				</scroll-view>
				<view class="next-scroll-right" v-if="hasData">
					<view
						class="next-scroll-right-name"
						:class="{ 'next-scroll-right-select': item == scrollIntoViewCopy }"
						v-for="(item, index) in scrollRightList"
						:key="index"
						@click.stop="chooseType(item)"
					>
						{{ item === 'no' ? '#' : item }}
					</view>
				</view>
			</view>
		</view>
	</uni-popup>
</template>

<script>
const position = {};
export default {
	props: {
		title: {
			type: String,
			default: ''
		},
		dataList: {
			type: Array,
			required: true,
			default: () => {
				return [];
			}
		},
		//显示的主键key值
		idKey: {
			type: String,
			default: 'id'
		},
		nameKey: {
			type: String,
			default: 'name'
		},
		phoneKey: {
			type: String,
			default: 'id'
		},
		imgKey: {
			type: String,
			default: 'member_avatar'
		},
		radius: {
			type: String,
			default: '10rpx'
		},
		showAvatar: {
			type: Boolean,
			default: true
		},
		isInterlock: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			focus: false,

			searchStr: '',
			scrollIntoView: '',
			scrollIntoViewCopy: '',
			scrollLeftObj: {},
			oldObj: {},
			scrollRightList: [],
			hasData: true
		};
	},
	created() {
		this.$watch(
			() => this.dataList,
			(newList) => {
				if (newList && newList.length) this.cleanData(newList);
			},
			{
				immediate: true,
				deep: true
			}
		);
	},
	methods: {
		open() {
			this.$refs.popup.open();
			if (this.isInterlock) {
				this.$nextTick(() => {
					setTimeout(() => {
						let initTop = 0;
						Object.keys(this.scrollRightList).map((key) => {
							uni.createSelectorQuery()
								.in(this)
								.select(`#item${this.scrollRightList[key]}`)
								.boundingClientRect((res) => {
									const { top } = res;
									position[this.scrollRightList[key]] = top - initTop;
									initTop = position[this.scrollRightList[0]];
								})
								.exec();
						});
					}, 300);
				});
			}
		},
		close() {
			this.$refs.popup.close();
		},
		focusFn() {
			this.focus = true;
		},
		blurFn() {
			this.focus = false;
		},
		search() {
			this.focus = true;
			if (this.searchStr) {
				let has = false;
				this.scrollLeftObj = JSON.parse(JSON.stringify(this.oldObj));
				for (let i in this.scrollLeftObj) {
					this.scrollLeftObj[i] = this.scrollLeftObj[i].filter((item) => {
						return item[this.nameKey].indexOf(this.searchStr) != -1 || `${item[this.phoneKey]}`.indexOf(this.searchStr) != -1;
					});
					if (this.scrollLeftObj[i].length) has = true;
				}
				if (has) this.hasData = true;
				else this.hasData = false;
			} else {
				this.hasData = true;
				this.scrollLeftObj = JSON.parse(JSON.stringify(this.oldObj));
			}
		},
		scrollCallback(e) {
			const { detail } = e;
			const { scrollTop, scrollHeight } = detail;
			if (this.scrollIntoView === 'TOP') {
				this.scrollIntoView = '';
			}
			if (this.isInterlock) {
				for (let key in position) {
					if (position[key] - scrollTop > 0 && position[key] - scrollTop <= scrollHeight) {
						this.scrollIntoViewCopy = key;
						return;
					}
				}
			}
		},
		scrollTop() {
			this.scrollIntoView = 'TOP';
		},
		cleanData(list) {
			this.scrollRightList = this.getLetter();
			let newList = [];
			list.forEach((res) => {
				let firsfirs = res.letter;
				if (!newList[firsfirs]) newList[firsfirs] = [];
				newList[firsfirs].push({
					[this.idKey]: res[this.idKey] || '',
					[this.nameKey]: res[this.nameKey],
					[this.phoneKey]: res[this.phoneKey] || '',
					[this.imgKey]: res[this.imgKey] || '',
					['mobile']: res.mobile
				});
			});
			this.scrollRightList.forEach((t) => {
				if (newList[t]) {
					this.$set(this.scrollLeftObj, t, newList[t]);
				} else {
					this.$set(this.scrollLeftObj, t, []);
				}
			});
			let surplusList = [];
			for (var i in newList) {
				let han = this.scrollRightList.find((v) => {
					return v == i;
				});
				if (!han) surplusList.push(newList[i]);
			}
			surplusList.forEach((item) => {
				this.scrollLeftObj['no'] = this.scrollLeftObj['no'].concat(item);
			});
			this.oldObj = JSON.parse(JSON.stringify(this.scrollLeftObj));

			// 过滤不存在的关键索引
			const existList = Object.keys(this.scrollLeftObj).filter((key) => {
				return this.scrollLeftObj[key].length;
			});
			this.scrollRightList = this.scrollRightList.filter((key) => {
				return existList.some((k) => k === key);
			});
		},
		getLetter() {
			let list = [];
			for (var i = 0; i < 26; i++) {
				list.push(String.fromCharCode(65 + i));
			}
			list.push('no');
			return list;
		},
		chooseType(item) {
			if (item == 'no') item = 'BOTTOM';
			this.scrollIntoView = item;
			this.scrollIntoViewCopy = item;
		},
		preview(img) {
			uni.previewImage({
				current: 0,
				urls: [img]
			});
		},
		chooseItem(item) {
			console.log(item)
			this.$emit('itemclick', item);
			this.$refs.popup.close();
		}
	}
};
</script>
<style>
/deep/ ::-webkit-scrollbar {
	width: 0;
	height: 0;
	color: transparent;
	display: none;
}
</style>
<style lang="scss" scoped>
.next {
	position: relative;
	width: 100%;
	height: 82vh;
	background-color: #fff;
	overflow: hidden;
	border-radius: 20rpx 20rpx 0 0;
	.top {
		width: 100%;
		height: 250rpx;
		.top-title {
			width: calc(100% - 60rpx);
			height: 120rpx;
			margin: 0 auto;
			.top-title-icon {
				width: 44rpx;
				height: 44rpx;
			}
		}

		.search {
			position: relative;
			width: calc(100% - 40rpx);
			height: 80rpx;
			margin: 0 auto;
			border-radius: 14rpx;
			background-color: #ededed;
			.search-input {
				box-sizing: border-box;
				padding: 0 20rpx;
				position: absolute;
				z-index: 3;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				input {
					width: 100%;
					height: 100%;
					text-align: center;
				}
			}
			.search-icon {
				width: 34rpx;
				height: 34rpx;
				margin-right: 16rpx;
			}
			.search-text {
				color: #9b9b9b;
			}
		}
	}
}

.next-list {
	position: relative;
	width: 100%;
	height: 0;
	box-sizing: border-box;
	.next-scroll-left {
		height: 100%;

		.left-list {
			height: auto;

			.left-item-title {
				width: calc(100% - 24rpx);
				height: 60rpx;
				padding-left: 24rpx;
				text-align: left;
				line-height: 60rpx;
				font-size: 30rpx;
				color: #666666;
			}

			.left-item-card {
				width: 100%;
				height: 112rpx;
				background-color: #ffffff;
				box-sizing: border-box;
				padding-left: 24rpx;
				display: flex;
				align-items: center;
				justify-content: flex-start;

				.left-item-card-img {
					width: 80rpx;
					min-width: 80rpx;
					height: 80rpx;
					background-color: #cfcfcf;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 36rpx;
					font-weight: bold;
					color: #ffffff;
					overflow: hidden;
				}

				.img-info {
					background: none;
					border: solid #f0f0f0 1rpx;
				}

				.left-item-card-info {
					width: 100%;
					margin-left: 20rpx;
					height: 100%;
					display: flex;
					align-items: flex-start;
					justify-content: center;
					flex-direction: column;

					.left-item-card-name {
						font-size: 30rpx;
						line-height: 30rpx;
						color: #333333;
					}

					.left-item-card-phone {
						margin-top: 14rpx;
						font-size: 28rpx;
						line-height: 28rpx;
						color: #999999;
					}
				}
			}
		}
	}

	.next-scroll-right {
		position: absolute;
		right: 0rpx;
		top: 40%;
		transform: translateY(-47%);
		z-index: 999 !important;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;

		.next-scroll-right-top {
			width: 32rpx;
			height: 32rpx;
			margin-right: 14rpx;
			z-index: 999 !important;
		}

		.next-scroll-right-name {
			width: 32rpx;
			padding-right: 14rpx;
			height: 28rpx;
			font-size: 22rpx;
			color: #515151;
			line-height: 28rpx;
			margin-top: 8rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.next-scroll-right-select {
			padding: 0;
			margin-right: 14rpx;
			width: 28rpx;
			height: 28rpx;
			border-radius: 50%;
			background: #0cbf5e;
			color: #ffffff;
		}
	}

	.no-data {
		width: 100%;
		.no-data-img {
			width: 200rpx;
			height: 200rpx;
			margin-top: 100rpx;
			margin-bottom: 20rpx;
		}
	}
}
</style>
