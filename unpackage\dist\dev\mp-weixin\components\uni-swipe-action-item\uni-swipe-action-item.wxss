@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-swipe.data-v-bb66970c {
  position: relative;
  overflow: hidden;
  margin-bottom: 16rpx;
}
.uni-swipe_box.data-v-bb66970c {
  display: flex;
  flex-shrink: 0;
  position: relative;
  /* height: 234rpx; */
}
.uni-swipe_text--center.data-v-bb66970c {
  width: 100%;
  cursor: grab;
}
.uni-swipe_button-group.data-v-bb66970c {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  position: absolute;
  top: 0;
  bottom: 0;
}
.button-group--left.data-v-bb66970c {
  left: 0;
  -webkit-transform: translateX(-100%);
          transform: translateX(-100%);
}
.button-group--right.data-v-bb66970c {
  right: 0;
  -webkit-transform: translateX(100%);
          transform: translateX(100%);
}
.uni-swipe_button.data-v-bb66970c {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0 20px;
}
.uni-swipe_button-text.data-v-bb66970c {
  flex-shrink: 0;
  font-size: 14px;
}
.ani.data-v-bb66970c {
  transition-property: -webkit-transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  transition-duration: 0.3s;
  transition-timing-function: cubic-bezier(0.165, 0.84, 0.44, 1);
}
