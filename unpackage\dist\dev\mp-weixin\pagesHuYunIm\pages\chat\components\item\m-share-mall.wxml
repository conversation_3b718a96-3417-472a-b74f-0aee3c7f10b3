<view class="flex_c row data-v-1867cc55"><view data-event-opts="{{[['tap',[['onClick',['$event']]]]]}}" class="{{['flex_r','text-box','data-v-1867cc55',(isMy)?'text_box':'']}}" catchtap="__e"><view class="{{['text','data-v-1867cc55',isMy?'text_r':'text_l']}}"><view class="flex_c_c article data-v-1867cc55"><view class="flex_r fa_c article-infr data-v-1867cc55"><view class="article-infr-img data-v-1867cc55"><image class="img data-v-1867cc55" src="{{value.payload.share_image}}" mode="aspectFill"></image></view><view class="text_22 color__ article-infr-text data-v-1867cc55">{{''+value.payload.title+''}}</view></view><view class="text_30 nowrap_ article-title data-v-1867cc55">{{''+value.payload.short_title+''}}</view><view class="article-img data-v-1867cc55"><image class="img data-v-1867cc55" src="{{value.payload.share_image}}" mode="aspectFill"></image></view><view class="m-line data-v-1867cc55"><m-line vue-id="16db815e-1" color="#f0f0f0" length="100%" hairline="{{true}}" class="data-v-1867cc55" bind:__l="__l"></m-line></view><view class="flex_r fa_c article-b data-v-1867cc55"><view class="article-b-icon data-v-1867cc55"></view><view class="text_20 color__ article-b-text data-v-1867cc55">xxxx</view></view></view></view></view></view>