@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.text.data-v-1d268b7e {
  position: relative;
  z-index: 99;
  box-sizing: border-box;
  width: 490rpx;
  min-height: 180rpx;
  padding: 22rpx 30rpx 0 30rpx;
  border-radius: 10rpx;
  background-color: #fa9e3b;
}
.text .red_packet_bg_mini.data-v-1d268b7e {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  overflow: hidden;
  opacity: 0.2;
  border-radius: 10rpx;
}
.text .redPacket-row.data-v-1d268b7e {
  width: 100%;
  min-height: 94rpx;
}
.text .redPacket-row .redPacket-icon.data-v-1d268b7e {
  width: 76rpx;
  height: 88rpx;
  border-radius: 10rpx;
  overflow: hidden;
  margin-right: 26rpx;
}
.text .redPacket-text.data-v-1d268b7e {
  width: 100%;
  min-height: 30rpx;
  padding: 4rpx 0 6rpx 0;
  font-weight: 300;
}
.text_r.data-v-1d268b7e {
  position: relative;
}
.text_l.data-v-1d268b7e {
  position: relative;
}
.text_r.data-v-1d268b7e::after {
  position: absolute;
  z-index: -1;
  content: "";
  top: 26rpx;
  right: -8rpx;
  width: 18rpx;
  height: 18rpx;
  border-radius: 2px;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  background-color: #fa9e3b;
}
.text_l.data-v-1d268b7e::after {
  position: absolute;
  z-index: -1;
  content: "";
  top: 26rpx;
  left: -8rpx;
  width: 18rpx;
  height: 18rpx;
  border-radius: 2px;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  background-color: #fa9e3b;
}
.text_.data-v-1d268b7e {
  background-color: #fde2c4;
}
.text_.data-v-1d268b7e::after {
  background-color: #fde2c4;
}
.exclusive.data-v-1d268b7e {
  width: 100%;
  flex-direction: row-reverse;
  margin-top: 2rpx;
}
.exclusive .label-icon.data-v-1d268b7e {
  width: 34rpx;
  height: 34rpx;
  margin-right: 10rpx;
}
.redPacket.data-v-1d268b7e {
  width: 100%;
  height: 80rpx;
}
.redPacket .redPacket-icon.data-v-1d268b7e {
  height: 34rpx;
  border-radius: 4px;
  margin: 0 12rpx 0 0;
  overflow: hidden;
}
