@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.scroll-view-box.data-v-b5c5336a {
  position: relative;
  width: 100%;
  height: 100%;
}
.swiper-item-box.data-v-b5c5336a {
  position: relative;
  width: 100%;
  height: 100%;
}
.swiper-item-box .list.data-v-b5c5336a {
  width: 100%;
  flex-wrap: wrap;
}
.swiper-item-box .list-item.data-v-b5c5336a {
  width: 25%;
  height: 200rpx;
}
.swiper-item-box .list-item .list-item-img.data-v-b5c5336a {
  position: relative;
  width: 150rpx;
  height: 150rpx;
}
.swiper-item-box .list-item .list-item-img .edit-choice.data-v-b5c5336a {
  position: absolute;
  z-index: 2;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.swiper-item-box .list-item .list-item-img .edit-choice .edit-choice-select.data-v-b5c5336a {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 1px solid #fff;
  background-color: rgba(216, 216, 216, 0.5);
}
.swiper-item-box .list-item .list-item-img .edit-choice .edit-choice-select .img.data-v-b5c5336a {
  width: 70%;
  height: 70%;
}
.swiper-item-box .list-item .list-item-img .edit-choice .edit_choice_select.data-v-b5c5336a {
  border: 1px solid #02c162;
  background-color: #02c162;
}
.swiper-item-box .list-item .list-item-img .edit_choice.data-v-b5c5336a {
  background-color: rgba(255, 255, 255, 0.8);
}
.swiper-item-box .list-item .list_item_img.data-v-b5c5336a {
  box-sizing: border-box;
  width: 150rpx;
  height: 150rpx;
  border-radius: 20rpx;
  border: 2px dashed #3d3d3d;
}
.swiper-item-box .list-item .list_item_img .img.data-v-b5c5336a {
  width: 70%;
  height: 70%;
}
.edit.data-v-b5c5336a {
  box-sizing: border-box;
  padding: 0 30rpx;
  position: absolute;
  z-index: 3;
  bottom: -74rpx;
  left: 0;
  width: 100%;
  height: 70rpx;
  transition: all 0.3s;
  background-color: rgba(246, 246, 246, 0.8);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
.edit .edit-item.data-v-b5c5336a {
  padding: 0 10rpx;
}
.edit_.data-v-b5c5336a {
  bottom: 0;
}
