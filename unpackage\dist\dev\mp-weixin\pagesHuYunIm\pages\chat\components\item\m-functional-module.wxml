<view class="flex_c row data-v-527e8533"><view data-event-opts="{{[['tap',[['onClick',['$event']]]]]}}" class="{{['flex_r','text-box','data-v-527e8533',(isMy)?'text_box':'']}}" catchtap="__e"><view class="{{['text','data-v-527e8533',isMy?'text_r':'text_l']}}"><view class="flex_c_c article data-v-527e8533"><view class="flex_r fa_c article-info data-v-527e8533"><view class="article-img data-v-527e8533"><image class="img data-v-527e8533" src="{{value.payload.image}}" mode="aspectFill"></image></view><view class="flex1 data-v-527e8533"><view class="text_32 article-title data-v-527e8533">{{value.payload.title}}</view><view class="text_26 color__ article-title data-v-527e8533">{{value.payload.text}}</view></view></view><view class="m-line data-v-527e8533"><m-line vue-id="055abb9a-1" color="#f0f0f0" length="100%" hairline="{{true}}" class="data-v-527e8533" bind:__l="__l"></m-line></view><view class="flex_r fa_c article-b data-v-527e8533"><view class="text_20 color__ article-b-text data-v-527e8533">功能分享</view></view></view></view></view></view>